# 2.2 OpenAI API深度实践

## 学习目标

完成本节学习后，学员将能够：
1. 熟练使用OpenAI的各种API端点和模型
2. 理解并优化API参数以获得最佳结果
3. 实现高级功能如流式响应、函数调用
4. 掌握成本控制和性能优化技巧
5. 构建完整的OpenAI API应用

## OpenAI模型家族详解

### GPT模型对比分析

#### 模型性能对比表

| 模型 | 上下文长度 | 训练数据截止 | 相对成本 | 适用场景 | 推荐用途 |
|------|------------|--------------|----------|----------|----------|
| gpt-4o | 128K | 2024-04 | 高 | 复杂推理、创作 | 高质量内容生成 |
| gpt-4-turbo | 128K | 2024-04 | 高 | 复杂任务、分析 | 专业文档处理 |
| gpt-4 | 8K | 2021-09 | 高 | 复杂推理 | 高精度任务 |
| gpt-3.5-turbo | 16K | 2021-09 | 低 | 日常对话、简单任务 | 聊天机器人 |
| gpt-3.5-turbo-instruct | 4K | 2021-09 | 低 | 指令跟随 | 简单自动化 |

#### 成本效益分析图表

**制作说明**：创建一个散点图，X轴为性能评分，Y轴为每1K tokens的成本

```
成本效益分析图
Y轴：成本（$/1K tokens）
X轴：性能评分（0-100）

数据点：
- gpt-4o: (95, 0.015)
- gpt-4-turbo: (92, 0.01)
- gpt-4: (90, 0.03)
- gpt-3.5-turbo: (75, 0.002)

图表特征：
- 使用不同颜色和大小的圆点
- 添加趋势线显示性价比
- 标注最佳性价比区域
```

### API参数深度解析

#### 核心参数详解

**temperature（温度参数）**
- **范围**：0.0 - 2.0
- **默认值**：1.0
- **作用**：控制输出的随机性和创造性

```python
# 温度参数示例
examples = {
    0.0: "确定性输出，适合事实性问答",
    0.3: "较低随机性，适合客服对话", 
    0.7: "平衡创造性，适合一般对话",
    1.0: "标准创造性，适合内容创作",
    1.5: "高创造性，适合创意写作"
}
```

**max_tokens（最大令牌数）**
- **作用**：限制响应的最大长度
- **计算方式**：输入tokens + 输出tokens ≤ 模型上下文限制
- **优化策略**：根据实际需求设置，避免浪费

**top_p（核采样）**
- **范围**：0.0 - 1.0
- **默认值**：1.0
- **与temperature的关系**：建议只调整其中一个参数

**frequency_penalty & presence_penalty**
- **范围**：-2.0 - 2.0
- **frequency_penalty**：减少重复内容
- **presence_penalty**：鼓励讨论新话题

## 高级功能实现

### 流式响应（Streaming）

流式响应允许实时接收模型输出，提升用户体验：

```python
# src/api_clients/openai_streaming.py
import openai
from typing import Iterator, Dict, Any
import json

class OpenAIStreamingClient:
    def __init__(self, api_key: str):
        self.client = openai.OpenAI(api_key=api_key)
    
    def stream_chat(self, messages: list, model: str = "gpt-3.5-turbo", 
                   **kwargs) -> Iterator[str]:
        """流式聊天响应"""
        try:
            stream = self.client.chat.completions.create(
                model=model,
                messages=messages,
                stream=True,
                **kwargs
            )
            
            for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            yield f"错误: {str(e)}"
    
    def stream_with_metadata(self, messages: list, model: str = "gpt-3.5-turbo",
                           **kwargs) -> Iterator[Dict[str, Any]]:
        """带元数据的流式响应"""
        try:
            stream = self.client.chat.completions.create(
                model=model,
                messages=messages,
                stream=True,
                **kwargs
            )
            
            for chunk in stream:
                chunk_data = {
                    'content': chunk.choices[0].delta.content,
                    'finish_reason': chunk.choices[0].finish_reason,
                    'model': chunk.model,
                    'created': chunk.created
                }
                yield chunk_data
                
        except Exception as e:
            yield {'error': str(e)}

# 使用示例
def demo_streaming():
    client = OpenAIStreamingClient("your-api-key")
    
    messages = [
        {"role": "user", "content": "请写一首关于人工智能的诗"}
    ]
    
    print("流式响应演示:")
    for content in client.stream_chat(messages):
        print(content, end='', flush=True)
    print("\n")

if __name__ == "__main__":
    demo_streaming()
```

### 函数调用（Function Calling）

Function Calling允许模型调用外部函数：

```python
# src/api_clients/function_calling.py
import openai
import json
import requests
from datetime import datetime
from typing import List, Dict, Any, Callable

class FunctionCallingClient:
    def __init__(self, api_key: str):
        self.client = openai.OpenAI(api_key=api_key)
        self.functions = {}
        self._register_default_functions()
    
    def register_function(self, func: Callable, description: str, 
                         parameters: Dict[str, Any]):
        """注册可调用函数"""
        self.functions[func.__name__] = {
            'function': func,
            'description': description,
            'parameters': parameters
        }
    
    def _register_default_functions(self):
        """注册默认函数"""
        
        # 获取当前时间
        def get_current_time():
            """获取当前时间"""
            return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 计算器
        def calculate(expression: str) -> str:
            """安全的数学计算器"""
            try:
                # 简单的安全检查
                allowed_chars = set('0123456789+-*/.() ')
                if not all(c in allowed_chars for c in expression):
                    return "错误：包含不允许的字符"
                
                result = eval(expression)
                return str(result)
            except Exception as e:
                return f"计算错误：{str(e)}"
        
        # 天气查询（模拟）
        def get_weather(city: str) -> str:
            """获取城市天气信息（模拟）"""
            # 实际应用中应该调用真实的天气API
            weather_data = {
                "北京": "晴天，25°C",
                "上海": "多云，22°C", 
                "广州": "雨天，28°C",
                "深圳": "晴天，30°C"
            }
            return weather_data.get(city, f"{city}的天气信息暂不可用")
        
        # 注册函数
        self.register_function(
            get_current_time,
            "获取当前日期和时间",
            {"type": "object", "properties": {}}
        )
        
        self.register_function(
            calculate,
            "执行数学计算",
            {
                "type": "object",
                "properties": {
                    "expression": {
                        "type": "string",
                        "description": "要计算的数学表达式"
                    }
                },
                "required": ["expression"]
            }
        )
        
        self.register_function(
            get_weather,
            "获取指定城市的天气信息",
            {
                "type": "object",
                "properties": {
                    "city": {
                        "type": "string",
                        "description": "城市名称"
                    }
                },
                "required": ["city"]
            }
        )
    
    def _get_function_definitions(self) -> List[Dict[str, Any]]:
        """获取函数定义列表"""
        definitions = []
        for name, info in self.functions.items():
            definitions.append({
                "type": "function",
                "function": {
                    "name": name,
                    "description": info['description'],
                    "parameters": info['parameters']
                }
            })
        return definitions
    
    def chat_with_functions(self, messages: List[Dict[str, str]], 
                          model: str = "gpt-3.5-turbo") -> str:
        """支持函数调用的聊天"""
        
        # 第一次API调用
        response = self.client.chat.completions.create(
            model=model,
            messages=messages,
            tools=self._get_function_definitions(),
            tool_choice="auto"
        )
        
        response_message = response.choices[0].message
        
        # 检查是否需要调用函数
        if response_message.tool_calls:
            # 添加助手的响应到消息历史
            messages.append({
                "role": "assistant",
                "content": response_message.content,
                "tool_calls": response_message.tool_calls
            })
            
            # 处理每个函数调用
            for tool_call in response_message.tool_calls:
                function_name = tool_call.function.name
                function_args = json.loads(tool_call.function.arguments)
                
                # 调用函数
                if function_name in self.functions:
                    function_response = self.functions[function_name]['function'](**function_args)
                else:
                    function_response = f"错误：未知函数 {function_name}"
                
                # 添加函数响应到消息历史
                messages.append({
                    "role": "tool",
                    "tool_call_id": tool_call.id,
                    "content": function_response
                })
            
            # 第二次API调用，获取最终响应
            second_response = self.client.chat.completions.create(
                model=model,
                messages=messages
            )
            
            return second_response.choices[0].message.content
        
        return response_message.content

# 使用示例
def demo_function_calling():
    client = FunctionCallingClient("your-api-key")
    
    test_queries = [
        "现在几点了？",
        "帮我计算 25 * 4 + 10",
        "北京今天天气怎么样？",
        "先告诉我现在时间，然后计算 100 / 5"
    ]
    
    for query in test_queries:
        print(f"\n用户: {query}")
        messages = [{"role": "user", "content": query}]
        response = client.chat_with_functions(messages)
        print(f"助手: {response}")

if __name__ == "__main__":
    demo_function_calling()
```

### 对话历史管理

```python
# src/utils/conversation_manager.py
from typing import List, Dict, Any, Optional
import json
from datetime import datetime

class ConversationManager:
    """对话历史管理器"""
    
    def __init__(self, max_history: int = 20, max_tokens: int = 4000):
        self.max_history = max_history
        self.max_tokens = max_tokens
        self.conversations = {}
    
    def add_message(self, session_id: str, role: str, content: str, 
                   metadata: Optional[Dict] = None):
        """添加消息到对话历史"""
        if session_id not in self.conversations:
            self.conversations[session_id] = {
                'messages': [],
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            }
        
        message = {
            'role': role,
            'content': content,
            'timestamp': datetime.now().isoformat(),
            'metadata': metadata or {}
        }
        
        self.conversations[session_id]['messages'].append(message)
        self.conversations[session_id]['updated_at'] = datetime.now().isoformat()
        
        # 清理过长的历史
        self._cleanup_history(session_id)
    
    def get_messages(self, session_id: str, 
                    format_for_api: bool = True) -> List[Dict[str, str]]:
        """获取对话消息"""
        if session_id not in self.conversations:
            return []
        
        messages = self.conversations[session_id]['messages']
        
        if format_for_api:
            # 转换为API格式
            return [{'role': msg['role'], 'content': msg['content']} 
                   for msg in messages]
        
        return messages
    
    def _cleanup_history(self, session_id: str):
        """清理过长的对话历史"""
        messages = self.conversations[session_id]['messages']
        
        # 限制消息数量
        if len(messages) > self.max_history:
            # 保留系统消息和最近的消息
            system_messages = [msg for msg in messages if msg['role'] == 'system']
            recent_messages = [msg for msg in messages if msg['role'] != 'system'][-self.max_history:]
            self.conversations[session_id]['messages'] = system_messages + recent_messages
        
        # 估算token数量并清理（简化实现）
        total_content = ' '.join([msg['content'] for msg in messages])
        estimated_tokens = len(total_content) // 4  # 粗略估算
        
        if estimated_tokens > self.max_tokens:
            # 移除最旧的非系统消息
            messages = self.conversations[session_id]['messages']
            system_messages = [msg for msg in messages if msg['role'] == 'system']
            other_messages = [msg for msg in messages if msg['role'] != 'system']
            
            # 保留最近的一半消息
            keep_count = len(other_messages) // 2
            self.conversations[session_id]['messages'] = system_messages + other_messages[-keep_count:]
    
    def clear_session(self, session_id: str):
        """清除会话"""
        if session_id in self.conversations:
            del self.conversations[session_id]
    
    def get_session_info(self, session_id: str) -> Optional[Dict]:
        """获取会话信息"""
        if session_id not in self.conversations:
            return None
        
        conv = self.conversations[session_id]
        return {
            'session_id': session_id,
            'message_count': len(conv['messages']),
            'created_at': conv['created_at'],
            'updated_at': conv['updated_at']
        }
    
    def export_conversation(self, session_id: str) -> Optional[str]:
        """导出对话为JSON格式"""
        if session_id not in self.conversations:
            return None
        
        return json.dumps(self.conversations[session_id], 
                         ensure_ascii=False, indent=2)

# 集成的OpenAI聊天客户端
class AdvancedOpenAIClient:
    """高级OpenAI客户端，集成对话管理"""
    
    def __init__(self, api_key: str):
        self.client = openai.OpenAI(api_key=api_key)
        self.conversation_manager = ConversationManager()
        self.function_client = FunctionCallingClient(api_key)
    
    def chat(self, session_id: str, user_message: str, 
             model: str = "gpt-3.5-turbo", 
             system_prompt: Optional[str] = None,
             use_functions: bool = False,
             **kwargs) -> str:
        """高级聊天功能"""
        
        # 添加系统提示（如果是新会话）
        if (system_prompt and 
            len(self.conversation_manager.get_messages(session_id)) == 0):
            self.conversation_manager.add_message(
                session_id, "system", system_prompt
            )
        
        # 添加用户消息
        self.conversation_manager.add_message(
            session_id, "user", user_message
        )
        
        # 获取对话历史
        messages = self.conversation_manager.get_messages(session_id)
        
        try:
            if use_functions:
                # 使用函数调用
                response = self.function_client.chat_with_functions(messages, model)
            else:
                # 普通聊天
                completion = self.client.chat.completions.create(
                    model=model,
                    messages=messages,
                    **kwargs
                )
                response = completion.choices[0].message.content
            
            # 添加助手响应
            self.conversation_manager.add_message(
                session_id, "assistant", response
            )
            
            return response
            
        except Exception as e:
            error_msg = f"抱歉，发生了错误：{str(e)}"
            self.conversation_manager.add_message(
                session_id, "assistant", error_msg
            )
            return error_msg
    
    def get_conversation_summary(self, session_id: str) -> str:
        """获取对话摘要"""
        messages = self.conversation_manager.get_messages(session_id, format_for_api=False)
        
        if not messages:
            return "暂无对话记录"
        
        # 构建摘要请求
        conversation_text = "\n".join([
            f"{msg['role']}: {msg['content']}" for msg in messages[-10:]  # 最近10条消息
        ])
        
        summary_prompt = f"""请为以下对话生成简洁的摘要：

{conversation_text}

摘要应该包括：
1. 主要讨论的话题
2. 关键信息点
3. 对话的结论或结果

摘要："""
        
        try:
            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": summary_prompt}],
                max_tokens=200
            )
            return response.choices[0].message.content
        except Exception as e:
            return f"生成摘要失败：{str(e)}"

# 使用示例
def demo_advanced_client():
    client = AdvancedOpenAIClient("your-api-key")
    session_id = "demo_session_001"
    
    # 设置系统提示
    system_prompt = "你是一个专业的AI助手，擅长回答技术问题。请用简洁明了的方式回答。"
    
    # 模拟对话
    queries = [
        "什么是机器学习？",
        "深度学习和机器学习有什么区别？",
        "现在几点了？",  # 需要函数调用
        "帮我计算 15 * 8"  # 需要函数调用
    ]
    
    for i, query in enumerate(queries):
        print(f"\n=== 对话 {i+1} ===")
        print(f"用户: {query}")
        
        # 前两个问题不使用函数，后两个使用函数
        use_functions = i >= 2
        
        response = client.chat(
            session_id=session_id,
            user_message=query,
            system_prompt=system_prompt if i == 0 else None,
            use_functions=use_functions
        )
        
        print(f"助手: {response}")
    
    # 获取对话摘要
    print(f"\n=== 对话摘要 ===")
    summary = client.get_conversation_summary(session_id)
    print(summary)
    
    # 显示会话信息
    session_info = client.conversation_manager.get_session_info(session_id)
    print(f"\n=== 会话信息 ===")
    print(f"消息数量: {session_info['message_count']}")
    print(f"创建时间: {session_info['created_at']}")

if __name__ == "__main__":
    demo_advanced_client()
```

## 性能优化与成本控制

### Token使用优化

```python
# src/utils/token_optimizer.py
import tiktoken
from typing import List, Dict, Tuple

class TokenOptimizer:
    """Token使用优化器"""
    
    def __init__(self, model: str = "gpt-3.5-turbo"):
        self.model = model
        self.encoding = tiktoken.encoding_for_model(model)
    
    def count_tokens(self, text: str) -> int:
        """计算文本的token数量"""
        return len(self.encoding.encode(text))
    
    def count_messages_tokens(self, messages: List[Dict[str, str]]) -> int:
        """计算消息列表的token数量"""
        total_tokens = 0
        
        for message in messages:
            # 每条消息的基础开销
            total_tokens += 4  # 每条消息的固定开销
            
            for key, value in message.items():
                total_tokens += self.count_tokens(str(value))
                if key == "name":  # 如果有name字段
                    total_tokens += 1
        
        total_tokens += 2  # 对话的结束标记
        return total_tokens
    
    def optimize_messages(self, messages: List[Dict[str, str]], 
                         max_tokens: int = 3000) -> List[Dict[str, str]]:
        """优化消息列表以控制token数量"""
        current_tokens = self.count_messages_tokens(messages)
        
        if current_tokens <= max_tokens:
            return messages
        
        # 保留系统消息
        system_messages = [msg for msg in messages if msg.get('role') == 'system']
        other_messages = [msg for msg in messages if msg.get('role') != 'system']
        
        # 从最新消息开始保留
        optimized_messages = system_messages.copy()
        
        for message in reversed(other_messages):
            temp_messages = optimized_messages + [message]
            if self.count_messages_tokens(temp_messages) <= max_tokens:
                optimized_messages.insert(-len([m for m in optimized_messages if m.get('role') != 'system']), message)
            else:
                break
        
        return optimized_messages
    
    def estimate_cost(self, input_tokens: int, output_tokens: int) -> float:
        """估算API调用成本（美元）"""
        # 价格表（2024年价格，可能需要更新）
        pricing = {
            "gpt-4": {"input": 0.03, "output": 0.06},
            "gpt-4-turbo": {"input": 0.01, "output": 0.03},
            "gpt-3.5-turbo": {"input": 0.0015, "output": 0.002}
        }
        
        model_pricing = pricing.get(self.model, pricing["gpt-3.5-turbo"])
        
        input_cost = (input_tokens / 1000) * model_pricing["input"]
        output_cost = (output_tokens / 1000) * model_pricing["output"]
        
        return input_cost + output_cost

# 使用示例
def demo_token_optimization():
    optimizer = TokenOptimizer("gpt-3.5-turbo")
    
    # 示例消息
    messages = [
        {"role": "system", "content": "你是一个有用的AI助手。"},
        {"role": "user", "content": "什么是机器学习？"},
        {"role": "assistant", "content": "机器学习是人工智能的一个分支..."},
        {"role": "user", "content": "能详细解释一下深度学习吗？"},
        {"role": "assistant", "content": "深度学习是机器学习的一个子集..."}
    ]
    
    # 计算token数量
    total_tokens = optimizer.count_messages_tokens(messages)
    print(f"总token数量: {total_tokens}")
    
    # 优化消息
    optimized = optimizer.optimize_messages(messages, max_tokens=100)
    optimized_tokens = optimizer.count_messages_tokens(optimized)
    print(f"优化后token数量: {optimized_tokens}")
    
    # 估算成本
    estimated_cost = optimizer.estimate_cost(total_tokens, 150)  # 假设输出150个token
    print(f"估算成本: ${estimated_cost:.4f}")

if __name__ == "__main__":
    demo_token_optimization()
```

## 实践练习

### 练习1：智能客服机器人

**任务描述**：构建一个具备以下功能的智能客服机器人：
- 支持多轮对话
- 能够调用外部函数查询订单状态
- 具备情感分析能力
- 支持对话摘要

**实现要求**：
```python
# exercises/customer_service_bot.py
class CustomerServiceBot:
    def __init__(self, api_key: str):
        # 初始化OpenAI客户端和其他组件
        pass
    
    def handle_customer_query(self, session_id: str, query: str) -> str:
        """处理客户查询"""
        # 实现客服逻辑
        pass
    
    def analyze_sentiment(self, text: str) -> str:
        """分析客户情感"""
        # 实现情感分析
        pass
    
    def query_order_status(self, order_id: str) -> str:
        """查询订单状态（模拟）"""
        # 模拟订单查询
        pass
```

### 练习2：内容生成助手

**任务描述**：开发一个内容生成助手，支持：
- 不同风格的文章生成
- 内容质量评估
- 多版本生成和比较
- 成本控制

## 评估标准

### 技术实现（50%）
- [ ] 正确使用OpenAI API的各种功能
- [ ] 实现错误处理和重试机制
- [ ] 优化token使用和成本控制
- [ ] 代码结构清晰，遵循最佳实践

### 功能完整性（30%）
- [ ] 实现所有要求的功能
- [ ] 用户体验良好
- [ ] 性能表现优秀
- [ ] 具备扩展性

### 创新性（20%）
- [ ] 有独特的功能设计
- [ ] 解决实际问题
- [ ] 技术应用巧妙
- [ ] 用户价值明确

## 常见问题解答

### Q1: 如何选择合适的GPT模型？

**A1**: 根据以下因素选择：
- **任务复杂度**：简单任务用gpt-3.5-turbo，复杂推理用gpt-4
- **成本预算**：gpt-3.5-turbo成本更低
- **响应时间**：gpt-3.5-turbo响应更快
- **上下文长度**：需要长上下文时选择支持更长上下文的模型

### Q2: 如何优化API调用的成本？

**A2**: 
1. **选择合适的模型**：不要过度使用高级模型
2. **优化prompt**：简洁明了的提示词
3. **控制输出长度**：设置合理的max_tokens
4. **使用缓存**：缓存常见问题的答案
5. **批量处理**：合并多个小请求

### Q3: 流式响应什么时候使用？

**A3**: 
- **长文本生成**：用户需要看到实时进度
- **交互式应用**：提升用户体验
- **实时对话**：模拟真实对话感觉
- **注意**：会增加请求复杂度，需要前端支持

## 下一步学习

完成本节后，建议：
1. 深入学习Function Calling的高级用法
2. 探索OpenAI的其他API（如DALL-E、Whisper）
3. 学习如何集成OpenAI API到Web应用
4. 准备学习其他厂商的API对比
