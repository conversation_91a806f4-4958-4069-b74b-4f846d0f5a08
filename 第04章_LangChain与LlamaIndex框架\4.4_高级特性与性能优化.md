# 4.4 高级特性与性能优化

## 学习目标

完成本节学习后，学员将能够：
1. 掌握LangChain和LlamaIndex的高级特性和扩展功能
2. 理解性能优化的原理和最佳实践
3. 能够开发自定义组件和插件
4. 掌握生产环境部署和监控技术
5. 具备大规模应用的架构设计能力

## 高级特性深度解析

### LangChain高级特性

#### 1. 自定义代理开发

```python
# advanced_features/custom_agents.py
from langchain.agents import Tool, AgentExecutor, BaseSingleActionAgent
from langchain.schema import AgentAction, AgentFinish
from langchain.prompts import StringPromptTemplate
from langchain.llms import OpenAI
from langchain.chains import LLMChain
from typing import List, Union, Dict, Any, Optional
import re
import asyncio

class CustomPromptTemplate(StringPromptTemplate):
    """自定义提示模板"""
    
    template: str
    tools: List[Tool]
    
    def format(self, **kwargs) -> str:
        # 获取中间步骤（如果有的话）
        intermediate_steps = kwargs.pop("intermediate_steps")
        thoughts = ""
        
        for action, observation in intermediate_steps:
            thoughts += action.log
            thoughts += f"\nObservation: {observation}\nThought: "
        
        # 设置代理可以停止的变量
        kwargs["agent_scratchpad"] = thoughts
        
        # 创建工具变量
        kwargs["tools"] = "\n".join([f"{tool.name}: {tool.description}" for tool in self.tools])
        kwargs["tool_names"] = ", ".join([tool.name for tool in self.tools])
        
        return self.template.format(**kwargs)

class CustomAgent(BaseSingleActionAgent):
    """自定义代理"""
    
    def __init__(self, llm_chain: LLMChain, tools: List[Tool], **kwargs):
        super().__init__(**kwargs)
        self.llm_chain = llm_chain
        self.tools = tools
        self.tool_names = [tool.name for tool in tools]
    
    @property
    def input_keys(self):
        return ["input"]
    
    def plan(self, intermediate_steps: List[tuple], **kwargs) -> Union[AgentAction, AgentFinish]:
        """规划下一步行动"""
        # 调用LLM获取响应
        response = self.llm_chain.run(
            intermediate_steps=intermediate_steps,
            **kwargs
        )
        
        # 解析响应
        if "Final Answer:" in response:
            return AgentFinish(
                return_values={"output": response.split("Final Answer:")[-1].strip()},
                log=response
            )
        
        # 提取行动和行动输入
        regex = r"Action: (.*?)[\n]*Action Input: (.*)"
        match = re.search(regex, response, re.DOTALL)
        
        if not match:
            raise ValueError(f"Could not parse LLM output: `{response}`")
        
        action = match.group(1).strip()
        action_input = match.group(2)
        
        return AgentAction(
            tool=action,
            tool_input=action_input.strip(" ").strip('"'),
            log=response
        )
    
    async def aplan(self, intermediate_steps: List[tuple], **kwargs) -> Union[AgentAction, AgentFinish]:
        """异步规划"""
        # 异步调用LLM
        response = await self.llm_chain.arun(
            intermediate_steps=intermediate_steps,
            **kwargs
        )
        
        # 使用相同的解析逻辑
        return self.plan(intermediate_steps, **kwargs)

class AdvancedAgentFactory:
    """高级代理工厂"""
    
    def __init__(self, llm):
        self.llm = llm
    
    def create_research_agent(self, tools: List[Tool]) -> AgentExecutor:
        """创建研究代理"""
        template = """You are a research assistant. Your goal is to find accurate information and provide comprehensive answers.

You have access to the following tools:
{tools}

Use the following format:
Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

Begin!

Question: {input}
Thought: {agent_scratchpad}"""
        
        prompt = CustomPromptTemplate(
            template=template,
            tools=tools,
            input_variables=["input", "intermediate_steps"]
        )
        
        llm_chain = LLMChain(llm=self.llm, prompt=prompt)
        agent = CustomAgent(llm_chain=llm_chain, tools=tools)
        
        return AgentExecutor.from_agent_and_tools(
            agent=agent,
            tools=tools,
            verbose=True,
            max_iterations=5
        )
    
    def create_planning_agent(self, tools: List[Tool]) -> AgentExecutor:
        """创建规划代理"""
        template = """You are a planning assistant. Break down complex tasks into manageable steps.

Available tools:
{tools}

Planning format:
Task: the main task to accomplish
Analysis: analyze the task complexity and requirements
Plan: create a step-by-step plan
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (repeat as needed)
Summary: summarize the results and next steps

Begin!

Task: {input}
Analysis: {agent_scratchpad}"""
        
        prompt = CustomPromptTemplate(
            template=template,
            tools=tools,
            input_variables=["input", "intermediate_steps"]
        )
        
        llm_chain = LLMChain(llm=self.llm, prompt=prompt)
        agent = CustomAgent(llm_chain=llm_chain, tools=tools)
        
        return AgentExecutor.from_agent_and_tools(
            agent=agent,
            tools=tools,
            verbose=True,
            max_iterations=10
        )

# 自定义工具开发
class CustomToolBuilder:
    """自定义工具构建器"""
    
    @staticmethod
    def create_database_tool(connection_string: str) -> Tool:
        """创建数据库查询工具"""
        def query_database(query: str) -> str:
            """查询数据库"""
            try:
                import sqlite3
                conn = sqlite3.connect(connection_string)
                cursor = conn.cursor()
                cursor.execute(query)
                results = cursor.fetchall()
                conn.close()
                
                if results:
                    return f"查询结果: {results[:10]}"  # 限制返回结果数量
                else:
                    return "查询无结果"
            except Exception as e:
                return f"查询错误: {str(e)}"
        
        return Tool(
            name="database_query",
            description="查询数据库。输入SQL查询语句，返回查询结果。",
            func=query_database
        )
    
    @staticmethod
    def create_file_tool() -> Tool:
        """创建文件操作工具"""
        def file_operations(operation: str) -> str:
            """文件操作"""
            try:
                parts = operation.split("|")
                if len(parts) < 2:
                    return "操作格式错误，应为: 操作类型|文件路径|内容(可选)"
                
                op_type = parts[0].strip()
                file_path = parts[1].strip()
                
                if op_type == "read":
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    return f"文件内容: {content[:500]}..."  # 限制返回长度
                
                elif op_type == "write":
                    if len(parts) < 3:
                        return "写入操作需要内容参数"
                    content = parts[2].strip()
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    return f"已写入文件: {file_path}"
                
                elif op_type == "list":
                    import os
                    files = os.listdir(file_path)
                    return f"目录内容: {files[:20]}"  # 限制返回数量
                
                else:
                    return f"不支持的操作类型: {op_type}"
                    
            except Exception as e:
                return f"文件操作错误: {str(e)}"
        
        return Tool(
            name="file_operations",
            description="文件操作工具。格式: 操作类型|文件路径|内容。支持read、write、list操作。",
            func=file_operations
        )
    
    @staticmethod
    def create_web_search_tool(api_key: str) -> Tool:
        """创建网络搜索工具"""
        def web_search(query: str) -> str:
            """网络搜索"""
            try:
                # 这里使用模拟搜索，实际应用中可以集成真实的搜索API
                import requests
                import json
                
                # 模拟搜索结果
                mock_results = [
                    {"title": f"关于{query}的搜索结果1", "snippet": f"{query}的详细信息..."},
                    {"title": f"关于{query}的搜索结果2", "snippet": f"{query}的相关内容..."}
                ]
                
                results_text = "\n".join([
                    f"标题: {result['title']}\n摘要: {result['snippet']}"
                    for result in mock_results[:3]
                ])
                
                return f"搜索结果:\n{results_text}"
                
            except Exception as e:
                return f"搜索错误: {str(e)}"
        
        return Tool(
            name="web_search",
            description="网络搜索工具。输入搜索关键词，返回相关搜索结果。",
            func=web_search
        )

# 使用示例
def demo_custom_agents():
    """演示自定义代理的使用"""
    # 初始化LLM
    llm = OpenAI(temperature=0)
    
    # 创建自定义工具
    tool_builder = CustomToolBuilder()
    tools = [
        tool_builder.create_file_tool(),
        tool_builder.create_web_search_tool("mock_api_key")
    ]
    
    # 创建代理工厂
    agent_factory = AdvancedAgentFactory(llm)
    
    # 创建研究代理
    research_agent = agent_factory.create_research_agent(tools)
    
    # 测试代理
    test_query = "请帮我研究人工智能的最新发展趋势"
    
    try:
        result = research_agent.run(test_query)
        print(f"研究结果: {result}")
    except Exception as e:
        print(f"代理执行错误: {e}")
    
    return research_agent

if __name__ == "__main__":
    demo_custom_agents()
```

#### 2. 高级记忆管理

```python
# advanced_features/memory_management.py
from langchain.memory import ConversationBufferMemory, ConversationSummaryMemory
from langchain.memory.chat_message_histories import RedisChatMessageHistory
from langchain.schema import BaseMemory, BaseChatMessageHistory
from typing import Dict, List, Any, Optional
import json
import hashlib
from datetime import datetime, timedelta
import asyncio

class HierarchicalMemory(BaseMemory):
    """分层记忆系统"""
    
    def __init__(self, 
                 short_term_limit: int = 10,
                 medium_term_limit: int = 50,
                 long_term_limit: int = 200):
        self.short_term_limit = short_term_limit
        self.medium_term_limit = medium_term_limit
        self.long_term_limit = long_term_limit
        
        self.short_term_memory = []  # 最近的对话
        self.medium_term_memory = []  # 重要的对话片段
        self.long_term_memory = []   # 总结和关键信息
        
        self.importance_threshold = 0.7
    
    @property
    def memory_variables(self) -> List[str]:
        return ["chat_history", "important_context", "background_knowledge"]
    
    def load_memory_variables(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """加载记忆变量"""
        return {
            "chat_history": self._format_short_term(),
            "important_context": self._format_medium_term(),
            "background_knowledge": self._format_long_term()
        }
    
    def save_context(self, inputs: Dict[str, Any], outputs: Dict[str, str]) -> None:
        """保存上下文"""
        # 创建记忆条目
        memory_entry = {
            "timestamp": datetime.now().isoformat(),
            "input": inputs,
            "output": outputs,
            "importance": self._calculate_importance(inputs, outputs)
        }
        
        # 添加到短期记忆
        self.short_term_memory.append(memory_entry)
        
        # 管理记忆层次
        self._manage_memory_hierarchy()
    
    def _calculate_importance(self, inputs: Dict[str, Any], outputs: Dict[str, str]) -> float:
        """计算记忆重要性"""
        # 简化的重要性计算
        importance_keywords = [
            "重要", "关键", "记住", "保存", "重点", 
            "important", "key", "remember", "save", "critical"
        ]
        
        text = str(inputs) + str(outputs)
        keyword_count = sum(1 for keyword in importance_keywords if keyword in text.lower())
        
        # 基于关键词数量和文本长度计算重要性
        base_importance = min(keyword_count * 0.2, 0.8)
        length_factor = min(len(text) / 1000, 0.2)
        
        return base_importance + length_factor
    
    def _manage_memory_hierarchy(self):
        """管理记忆层次"""
        # 短期记忆溢出处理
        if len(self.short_term_memory) > self.short_term_limit:
            # 将重要的记忆移到中期记忆
            overflow = self.short_term_memory[:-self.short_term_limit]
            important_memories = [m for m in overflow if m["importance"] > self.importance_threshold]
            
            self.medium_term_memory.extend(important_memories)
            self.short_term_memory = self.short_term_memory[-self.short_term_limit:]
        
        # 中期记忆溢出处理
        if len(self.medium_term_memory) > self.medium_term_limit:
            # 总结并移到长期记忆
            overflow = self.medium_term_memory[:-self.medium_term_limit]
            summary = self._summarize_memories(overflow)
            
            self.long_term_memory.append({
                "timestamp": datetime.now().isoformat(),
                "type": "summary",
                "content": summary,
                "source_count": len(overflow)
            })
            
            self.medium_term_memory = self.medium_term_memory[-self.medium_term_limit:]
        
        # 长期记忆限制
        if len(self.long_term_memory) > self.long_term_limit:
            self.long_term_memory = self.long_term_memory[-self.long_term_limit:]
    
    def _summarize_memories(self, memories: List[Dict]) -> str:
        """总结记忆"""
        # 简化的总结逻辑
        topics = {}
        for memory in memories:
            # 提取主题（简化实现）
            text = str(memory["input"]) + str(memory["output"])
            words = text.split()
            for word in words:
                if len(word) > 3:  # 忽略短词
                    topics[word] = topics.get(word, 0) + 1
        
        # 获取最频繁的主题
        top_topics = sorted(topics.items(), key=lambda x: x[1], reverse=True)[:5]
        
        summary = f"讨论了以下主题: {', '.join([topic for topic, count in top_topics])}"
        summary += f"。共包含{len(memories)}条对话记录。"
        
        return summary
    
    def _format_short_term(self) -> str:
        """格式化短期记忆"""
        if not self.short_term_memory:
            return "无最近对话记录"
        
        formatted = []
        for memory in self.short_term_memory[-5:]:  # 只显示最近5条
            formatted.append(f"用户: {memory['input']}")
            formatted.append(f"助手: {memory['output']}")
        
        return "\n".join(formatted)
    
    def _format_medium_term(self) -> str:
        """格式化中期记忆"""
        if not self.medium_term_memory:
            return "无重要上下文"
        
        important_items = [m for m in self.medium_term_memory if m["importance"] > 0.5]
        if not important_items:
            return "无重要上下文"
        
        formatted = []
        for memory in important_items[-3:]:  # 显示最近3条重要记忆
            formatted.append(f"重要对话: {memory['input']} -> {memory['output']}")
        
        return "\n".join(formatted)
    
    def _format_long_term(self) -> str:
        """格式化长期记忆"""
        if not self.long_term_memory:
            return "无背景知识"
        
        formatted = []
        for memory in self.long_term_memory[-3:]:  # 显示最近3条长期记忆
            if memory["type"] == "summary":
                formatted.append(f"历史总结: {memory['content']}")
        
        return "\n".join(formatted) if formatted else "无背景知识"
    
    def clear(self) -> None:
        """清空记忆"""
        self.short_term_memory.clear()
        self.medium_term_memory.clear()
        self.long_term_memory.clear()

class DistributedMemory:
    """分布式记忆系统"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_url = redis_url
        self.local_cache = {}
        self.cache_ttl = 300  # 5分钟本地缓存
    
    def get_session_memory(self, session_id: str) -> BaseChatMessageHistory:
        """获取会话记忆"""
        return RedisChatMessageHistory(
            session_id=session_id,
            url=self.redis_url
        )
    
    def store_global_context(self, key: str, context: Dict[str, Any]):
        """存储全局上下文"""
        try:
            import redis
            r = redis.from_url(self.redis_url)
            
            # 序列化上下文
            serialized = json.dumps(context, ensure_ascii=False)
            
            # 存储到Redis
            r.setex(f"global_context:{key}", 3600, serialized)  # 1小时过期
            
            # 更新本地缓存
            self.local_cache[key] = {
                "data": context,
                "timestamp": datetime.now()
            }
            
        except Exception as e:
            print(f"存储全局上下文失败: {e}")
    
    def get_global_context(self, key: str) -> Optional[Dict[str, Any]]:
        """获取全局上下文"""
        # 检查本地缓存
        if key in self.local_cache:
            cache_entry = self.local_cache[key]
            if datetime.now() - cache_entry["timestamp"] < timedelta(seconds=self.cache_ttl):
                return cache_entry["data"]
        
        # 从Redis获取
        try:
            import redis
            r = redis.from_url(self.redis_url)
            
            serialized = r.get(f"global_context:{key}")
            if serialized:
                context = json.loads(serialized)
                
                # 更新本地缓存
                self.local_cache[key] = {
                    "data": context,
                    "timestamp": datetime.now()
                }
                
                return context
        except Exception as e:
            print(f"获取全局上下文失败: {e}")
        
        return None

# 智能记忆管理器
class IntelligentMemoryManager:
    """智能记忆管理器"""
    
    def __init__(self, llm):
        self.llm = llm
        self.hierarchical_memory = HierarchicalMemory()
        self.distributed_memory = DistributedMemory()
        self.memory_strategies = self._initialize_strategies()
    
    def _initialize_strategies(self) -> Dict[str, Any]:
        """初始化记忆策略"""
        return {
            "conversation": {
                "type": "hierarchical",
                "retention_policy": "importance_based",
                "max_context_length": 4000
            },
            "facts": {
                "type": "distributed",
                "retention_policy": "permanent",
                "indexing": "semantic"
            },
            "preferences": {
                "type": "distributed",
                "retention_policy": "user_controlled",
                "privacy_level": "high"
            }
        }
    
    def store_memory(self, memory_type: str, content: Dict[str, Any], 
                    session_id: Optional[str] = None):
        """存储记忆"""
        strategy = self.memory_strategies.get(memory_type, self.memory_strategies["conversation"])
        
        if strategy["type"] == "hierarchical":
            self.hierarchical_memory.save_context(
                content.get("inputs", {}),
                content.get("outputs", {})
            )
        elif strategy["type"] == "distributed":
            key = f"{memory_type}:{session_id}" if session_id else memory_type
            self.distributed_memory.store_global_context(key, content)
    
    def retrieve_memory(self, memory_type: str, query: str, 
                       session_id: Optional[str] = None) -> Dict[str, Any]:
        """检索记忆"""
        strategy = self.memory_strategies.get(memory_type, self.memory_strategies["conversation"])
        
        if strategy["type"] == "hierarchical":
            return self.hierarchical_memory.load_memory_variables({})
        elif strategy["type"] == "distributed":
            key = f"{memory_type}:{session_id}" if session_id else memory_type
            context = self.distributed_memory.get_global_context(key)
            return context or {}
    
    def optimize_memory(self, session_id: str):
        """优化记忆"""
        # 分析记忆使用模式
        memory_stats = self._analyze_memory_usage(session_id)
        
        # 根据分析结果调整策略
        if memory_stats["redundancy"] > 0.3:
            self._deduplicate_memories(session_id)
        
        if memory_stats["importance_distribution"]["low"] > 0.7:
            self._compress_low_importance_memories(session_id)
    
    def _analyze_memory_usage(self, session_id: str) -> Dict[str, Any]:
        """分析记忆使用情况"""
        # 简化的分析逻辑
        return {
            "total_memories": len(self.hierarchical_memory.short_term_memory),
            "redundancy": 0.2,  # 模拟冗余度
            "importance_distribution": {
                "high": 0.2,
                "medium": 0.3,
                "low": 0.5
            }
        }
    
    def _deduplicate_memories(self, session_id: str):
        """去重记忆"""
        # 实现记忆去重逻辑
        pass
    
    def _compress_low_importance_memories(self, session_id: str):
        """压缩低重要性记忆"""
        # 实现记忆压缩逻辑
        pass

# 使用示例
def demo_advanced_memory():
    """演示高级记忆管理"""
    from langchain.llms import OpenAI
    
    # 初始化LLM
    llm = OpenAI(temperature=0)
    
    # 创建智能记忆管理器
    memory_manager = IntelligentMemoryManager(llm)
    
    # 模拟对话
    conversations = [
        {"inputs": {"user": "我叫张三"}, "outputs": {"assistant": "你好张三，很高兴认识你！"}},
        {"inputs": {"user": "我喜欢编程"}, "outputs": {"assistant": "编程是很有趣的技能！"}},
        {"inputs": {"user": "请记住我的生日是1月1日"}, "outputs": {"assistant": "好的，我会记住你的生日是1月1日。"}}
    ]
    
    # 存储对话记忆
    for conv in conversations:
        memory_manager.store_memory("conversation", conv, "user_123")
    
    # 检索记忆
    retrieved = memory_manager.retrieve_memory("conversation", "用户信息", "user_123")
    print("检索到的记忆:")
    for key, value in retrieved.items():
        print(f"{key}: {value}")
    
    # 优化记忆
    memory_manager.optimize_memory("user_123")
    
    return memory_manager

if __name__ == "__main__":
    demo_advanced_memory()
```

### LlamaIndex高级特性

#### 1. 自定义索引类型

```python
# advanced_features/custom_indexes.py
from llama_index.core import BaseIndex, Document
from llama_index.core.base.base_retriever import BaseRetriever
from llama_index.core.schema import NodeWithScore, QueryBundle
from typing import List, Dict, Any, Optional
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import pickle
import os

class HybridIndex(BaseIndex):
    """混合索引 - 结合向量和关键词检索"""
    
    def __init__(self, documents: List[Document], **kwargs):
        super().__init__(**kwargs)
        self.documents = documents
        self.vector_index = None
        self.keyword_index = None
        self.tfidf_vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')
        self._build_index()
    
    def _build_index(self):
        """构建混合索引"""
        # 提取文档文本
        texts = [doc.text for doc in self.documents]
        
        # 构建TF-IDF向量
        self.tfidf_matrix = self.tfidf_vectorizer.fit_transform(texts)
        
        # 构建关键词索引
        self.keyword_index = self._build_keyword_index(texts)
        
        print(f"混合索引构建完成，包含 {len(self.documents)} 个文档")
    
    def _build_keyword_index(self, texts: List[str]) -> Dict[str, List[int]]:
        """构建关键词索引"""
        keyword_index = {}
        
        for i, text in enumerate(texts):
            words = text.lower().split()
            for word in words:
                if len(word) > 3:  # 忽略短词
                    if word not in keyword_index:
                        keyword_index[word] = []
                    keyword_index[word].append(i)
        
        return keyword_index
    
    def as_retriever(self, **kwargs) -> 'HybridRetriever':
        """返回检索器"""
        return HybridRetriever(self, **kwargs)
    
    def _insert(self, nodes, **kwargs):
        """插入新节点"""
        # 实现节点插入逻辑
        pass
    
    def _delete_node(self, node_id: str, **kwargs):
        """删除节点"""
        # 实现节点删除逻辑
        pass
    
    def ref_doc_info(self) -> Dict[str, Any]:
        """返回文档信息"""
        return {"num_docs": len(self.documents)}

class HybridRetriever(BaseRetriever):
    """混合检索器"""
    
    def __init__(self, index: HybridIndex, 
                 similarity_top_k: int = 5,
                 keyword_weight: float = 0.3,
                 vector_weight: float = 0.7):
        super().__init__()
        self.index = index
        self.similarity_top_k = similarity_top_k
        self.keyword_weight = keyword_weight
        self.vector_weight = vector_weight
    
    def _retrieve(self, query_bundle: QueryBundle) -> List[NodeWithScore]:
        """执行混合检索"""
        query_text = query_bundle.query_str
        
        # 向量检索
        vector_scores = self._vector_retrieve(query_text)
        
        # 关键词检索
        keyword_scores = self._keyword_retrieve(query_text)
        
        # 合并分数
        combined_scores = self._combine_scores(vector_scores, keyword_scores)
        
        # 排序并返回top-k结果
        sorted_results = sorted(combined_scores.items(), key=lambda x: x[1], reverse=True)
        top_results = sorted_results[:self.similarity_top_k]
        
        # 转换为NodeWithScore格式
        nodes_with_scores = []
        for doc_idx, score in top_results:
            if doc_idx < len(self.index.documents):
                doc = self.index.documents[doc_idx]
                # 创建简化的节点
                node = type('Node', (), {
                    'text': doc.text,
                    'metadata': doc.metadata,
                    'node_id': f"doc_{doc_idx}"
                })()
                
                nodes_with_scores.append(NodeWithScore(node=node, score=score))
        
        return nodes_with_scores
    
    def _vector_retrieve(self, query: str) -> Dict[int, float]:
        """向量检索"""
        # 将查询转换为TF-IDF向量
        query_vector = self.index.tfidf_vectorizer.transform([query])
        
        # 计算余弦相似度
        similarities = cosine_similarity(query_vector, self.index.tfidf_matrix).flatten()
        
        # 返回文档索引和相似度分数
        return {i: float(sim) for i, sim in enumerate(similarities)}
    
    def _keyword_retrieve(self, query: str) -> Dict[int, float]:
        """关键词检索"""
        query_words = query.lower().split()
        keyword_scores = {}
        
        for word in query_words:
            if word in self.index.keyword_index:
                doc_indices = self.index.keyword_index[word]
                for doc_idx in doc_indices:
                    keyword_scores[doc_idx] = keyword_scores.get(doc_idx, 0) + 1
        
        # 归一化分数
        if keyword_scores:
            max_score = max(keyword_scores.values())
            keyword_scores = {k: v / max_score for k, v in keyword_scores.items()}
        
        return keyword_scores
    
    def _combine_scores(self, vector_scores: Dict[int, float], 
                       keyword_scores: Dict[int, float]) -> Dict[int, float]:
        """合并检索分数"""
        combined_scores = {}
        
        # 获取所有文档索引
        all_indices = set(vector_scores.keys()) | set(keyword_scores.keys())
        
        for idx in all_indices:
            vector_score = vector_scores.get(idx, 0.0)
            keyword_score = keyword_scores.get(idx, 0.0)
            
            combined_score = (
                self.vector_weight * vector_score + 
                self.keyword_weight * keyword_score
            )
            
            combined_scores[idx] = combined_score
        
        return combined_scores

class GraphIndex(BaseIndex):
    """图索引 - 基于实体关系的索引"""
    
    def __init__(self, documents: List[Document], **kwargs):
        super().__init__(**kwargs)
        self.documents = documents
        self.entity_graph = {}
        self.entity_documents = {}
        self._build_graph()
    
    def _build_graph(self):
        """构建实体关系图"""
        for i, doc in enumerate(self.documents):
            entities = self._extract_entities(doc.text)
            
            # 记录实体所在文档
            for entity in entities:
                if entity not in self.entity_documents:
                    self.entity_documents[entity] = []
                self.entity_documents[entity].append(i)
            
            # 构建实体关系
            for j, entity1 in enumerate(entities):
                for entity2 in entities[j+1:]:
                    self._add_relation(entity1, entity2)
        
        print(f"图索引构建完成，包含 {len(self.entity_graph)} 个实体")
    
    def _extract_entities(self, text: str) -> List[str]:
        """提取实体（简化实现）"""
        # 这里使用简单的规则提取，实际应用中可以使用NER模型
        import re
        
        # 提取大写开头的词作为实体
        entities = re.findall(r'\b[A-Z][a-z]+\b', text)
        
        # 去重并过滤
        entities = list(set(entities))
        entities = [e for e in entities if len(e) > 2]
        
        return entities
    
    def _add_relation(self, entity1: str, entity2: str):
        """添加实体关系"""
        if entity1 not in self.entity_graph:
            self.entity_graph[entity1] = set()
        if entity2 not in self.entity_graph:
            self.entity_graph[entity2] = set()
        
        self.entity_graph[entity1].add(entity2)
        self.entity_graph[entity2].add(entity1)
    
    def as_retriever(self, **kwargs) -> 'GraphRetriever':
        """返回图检索器"""
        return GraphRetriever(self, **kwargs)
    
    def _insert(self, nodes, **kwargs):
        """插入新节点"""
        pass
    
    def _delete_node(self, node_id: str, **kwargs):
        """删除节点"""
        pass
    
    def ref_doc_info(self) -> Dict[str, Any]:
        """返回文档信息"""
        return {
            "num_docs": len(self.documents),
            "num_entities": len(self.entity_graph)
        }

class GraphRetriever(BaseRetriever):
    """图检索器"""
    
    def __init__(self, index: GraphIndex, similarity_top_k: int = 5):
        super().__init__()
        self.index = index
        self.similarity_top_k = similarity_top_k
    
    def _retrieve(self, query_bundle: QueryBundle) -> List[NodeWithScore]:
        """基于图的检索"""
        query_text = query_bundle.query_str
        query_entities = self.index._extract_entities(query_text)
        
        # 计算文档相关性
        doc_scores = {}
        
        for entity in query_entities:
            if entity in self.index.entity_documents:
                # 直接匹配的文档
                for doc_idx in self.index.entity_documents[entity]:
                    doc_scores[doc_idx] = doc_scores.get(doc_idx, 0) + 1.0
                
                # 相关实体的文档
                if entity in self.index.entity_graph:
                    for related_entity in self.index.entity_graph[entity]:
                        if related_entity in self.index.entity_documents:
                            for doc_idx in self.index.entity_documents[related_entity]:
                                doc_scores[doc_idx] = doc_scores.get(doc_idx, 0) + 0.5
        
        # 排序并返回结果
        sorted_docs = sorted(doc_scores.items(), key=lambda x: x[1], reverse=True)
        top_docs = sorted_docs[:self.similarity_top_k]
        
        # 转换为NodeWithScore格式
        nodes_with_scores = []
        for doc_idx, score in top_docs:
            if doc_idx < len(self.index.documents):
                doc = self.index.documents[doc_idx]
                node = type('Node', (), {
                    'text': doc.text,
                    'metadata': doc.metadata,
                    'node_id': f"doc_{doc_idx}"
                })()
                
                nodes_with_scores.append(NodeWithScore(node=node, score=score))
        
        return nodes_with_scores

# 使用示例
def demo_custom_indexes():
    """演示自定义索引的使用"""
    # 创建示例文档
    documents = [
        Document(text="Python is a programming language. It is widely used for data science and machine learning."),
        Document(text="Machine learning is a subset of artificial intelligence. Python and R are popular languages for ML."),
        Document(text="Data science involves statistics, programming, and domain expertise. Python is the most popular language."),
        Document(text="Artificial intelligence includes machine learning, natural language processing, and computer vision.")
    ]
    
    # 创建混合索引
    print("=== 混合索引演示 ===")
    hybrid_index = HybridIndex(documents)
    hybrid_retriever = hybrid_index.as_retriever(similarity_top_k=3)
    
    # 测试检索
    query = QueryBundle(query_str="Python programming machine learning")
    results = hybrid_retriever.retrieve(query)
    
    print(f"查询: {query.query_str}")
    for i, result in enumerate(results):
        print(f"结果 {i+1} (分数: {result.score:.3f}): {result.node.text[:100]}...")
    
    # 创建图索引
    print("\n=== 图索引演示 ===")
    graph_index = GraphIndex(documents)
    graph_retriever = graph_index.as_retriever(similarity_top_k=3)
    
    # 测试图检索
    graph_results = graph_retriever.retrieve(query)
    
    print(f"查询: {query.query_str}")
    for i, result in enumerate(graph_results):
        print(f"结果 {i+1} (分数: {result.score:.3f}): {result.node.text[:100]}...")
    
    return hybrid_index, graph_index

if __name__ == "__main__":
    demo_custom_indexes()
```

## 性能优化策略

### 1. 缓存优化

```python
# performance_optimization/caching_strategies.py
import hashlib
import pickle
import time
from typing import Any, Dict, Optional, Callable
from functools import wraps
import redis
import asyncio

class MultiLevelCache:
    """多级缓存系统"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.memory_cache = {}  # L1: 内存缓存
        self.redis_client = redis.from_url(redis_url)  # L2: Redis缓存
        self.cache_stats = {
            "hits": {"memory": 0, "redis": 0},
            "misses": 0,
            "total_requests": 0
        }
    
    def _generate_key(self, func_name: str, args: tuple, kwargs: dict) -> str:
        """生成缓存键"""
        key_data = f"{func_name}:{str(args)}:{str(sorted(kwargs.items()))}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        self.cache_stats["total_requests"] += 1
        
        # L1: 检查内存缓存
        if key in self.memory_cache:
            entry = self.memory_cache[key]
            if time.time() < entry["expires_at"]:
                self.cache_stats["hits"]["memory"] += 1
                return entry["value"]
            else:
                del self.memory_cache[key]
        
        # L2: 检查Redis缓存
        try:
            cached_data = self.redis_client.get(key)
            if cached_data:
                value = pickle.loads(cached_data)
                self.cache_stats["hits"]["redis"] += 1
                
                # 回填到内存缓存
                self.memory_cache[key] = {
                    "value": value,
                    "expires_at": time.time() + 300  # 5分钟内存缓存
                }
                
                return value
        except Exception as e:
            print(f"Redis缓存读取失败: {e}")
        
        # 缓存未命中
        self.cache_stats["misses"] += 1
        return None
    
    def set(self, key: str, value: Any, ttl: int = 3600):
        """设置缓存值"""
        # L1: 设置内存缓存
        self.memory_cache[key] = {
            "value": value,
            "expires_at": time.time() + min(ttl, 300)  # 内存缓存最多5分钟
        }
        
        # L2: 设置Redis缓存
        try:
            serialized_value = pickle.dumps(value)
            self.redis_client.setex(key, ttl, serialized_value)
        except Exception as e:
            print(f"Redis缓存写入失败: {e}")
    
    def invalidate(self, pattern: str = None):
        """失效缓存"""
        if pattern:
            # 清除匹配模式的缓存
            keys_to_remove = [k for k in self.memory_cache.keys() if pattern in k]
            for key in keys_to_remove:
                del self.memory_cache[key]
            
            try:
                redis_keys = self.redis_client.keys(f"*{pattern}*")
                if redis_keys:
                    self.redis_client.delete(*redis_keys)
            except Exception as e:
                print(f"Redis缓存清除失败: {e}")
        else:
            # 清除所有缓存
            self.memory_cache.clear()
            try:
                self.redis_client.flushdb()
            except Exception as e:
                print(f"Redis缓存清除失败: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        total_hits = sum(self.cache_stats["hits"].values())
        hit_rate = total_hits / max(1, self.cache_stats["total_requests"])
        
        return {
            "hit_rate": hit_rate,
            "memory_hits": self.cache_stats["hits"]["memory"],
            "redis_hits": self.cache_stats["hits"]["redis"],
            "misses": self.cache_stats["misses"],
            "total_requests": self.cache_stats["total_requests"]
        }

def cached(ttl: int = 3600, cache_instance: Optional[MultiLevelCache] = None):
    """缓存装饰器"""
    if cache_instance is None:
        cache_instance = MultiLevelCache()
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = cache_instance._generate_key(func.__name__, args, kwargs)
            
            # 尝试从缓存获取
            cached_result = cache_instance.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache_instance.set(cache_key, result, ttl)
            
            return result
        
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # 异步版本的缓存装饰器
            cache_key = cache_instance._generate_key(func.__name__, args, kwargs)
            
            cached_result = cache_instance.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            result = await func(*args, **kwargs)
            cache_instance.set(cache_key, result, ttl)
            
            return result
        
        # 根据函数类型返回相应的包装器
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return wrapper
    
    return decorator

# 使用示例
cache = MultiLevelCache()

@cached(ttl=1800, cache_instance=cache)
def expensive_llm_call(prompt: str, model: str = "gpt-3.5-turbo") -> str:
    """模拟昂贵的LLM调用"""
    print(f"执行LLM调用: {prompt[:50]}...")
    time.sleep(2)  # 模拟网络延迟
    return f"LLM响应: {prompt} (模型: {model})"

@cached(ttl=3600, cache_instance=cache)
def complex_data_processing(data: str) -> Dict[str, Any]:
    """模拟复杂的数据处理"""
    print(f"执行数据处理: {len(data)} 字符")
    time.sleep(1)  # 模拟处理时间
    return {
        "processed_data": data.upper(),
        "length": len(data),
        "timestamp": time.time()
    }

def demo_caching():
    """演示缓存系统"""
    print("=== 缓存系统演示 ===")
    
    # 第一次调用（缓存未命中）
    start_time = time.time()
    result1 = expensive_llm_call("什么是人工智能？")
    first_call_time = time.time() - start_time
    print(f"第一次调用耗时: {first_call_time:.2f}秒")
    
    # 第二次调用（缓存命中）
    start_time = time.time()
    result2 = expensive_llm_call("什么是人工智能？")
    second_call_time = time.time() - start_time
    print(f"第二次调用耗时: {second_call_time:.2f}秒")
    
    # 显示缓存统计
    stats = cache.get_stats()
    print(f"缓存统计: {stats}")
    
    return cache

if __name__ == "__main__":
    demo_caching()
```

## 实践练习

### 练习1：构建高性能RAG系统

**任务描述**：结合LangChain和LlamaIndex的优势，构建一个高性能的RAG系统，包含缓存、异步处理和性能监控。

**实现要求**：
```python
class HighPerformanceRAG:
    def __init__(self, langchain_components, llamaindex_components):
        # 初始化混合RAG系统
        pass
    
    async def query_async(self, query: str) -> Dict[str, Any]:
        # 异步查询处理
        pass
    
    def optimize_performance(self) -> Dict[str, Any]:
        # 性能优化
        pass
```

### 练习2：开发智能组件选择器

**任务描述**：创建一个智能系统，根据查询类型和性能要求自动选择最优的框架组件组合。

## 评估标准

### 技术深度（40%）
- [ ] 高级特性使用正确
- [ ] 性能优化效果明显
- [ ] 自定义组件设计合理
- [ ] 代码质量高

### 系统性能（40%）
- [ ] 响应时间优化
- [ ] 内存使用效率
- [ ] 并发处理能力
- [ ] 可扩展性好

### 创新性（20%）
- [ ] 有独特的优化策略
- [ ] 组件设计创新
- [ ] 解决实际问题
- [ ] 技术应用巧妙

## 常见问题解答

### Q1: 如何选择合适的缓存策略？

**A1**: 根据数据特征选择：
- **频繁访问的小数据**：内存缓存
- **大数据或持久化需求**：Redis缓存
- **复杂查询结果**：多级缓存
- **实时性要求高**：短TTL缓存

### Q2: 如何优化大规模数据的索引性能？

**A2**:
1. **分片策略**：将大索引分割为多个小索引
2. **异步处理**：使用异步I/O和并行处理
3. **增量更新**：支持增量索引更新
4. **硬件优化**：使用SSD和充足内存

### Q3: 如何监控和调试框架性能？

**A3**:
1. **性能指标**：监控响应时间、内存使用、错误率
2. **日志记录**：详细记录关键操作
3. **分布式追踪**：使用APM工具追踪请求
4. **A/B测试**：对比不同配置的性能

## 下一步学习

完成本节后，建议：
1. 深入学习分布式系统架构
2. 探索更多的性能优化技术
3. 学习生产环境部署和运维
4. 关注框架的最新发展和最佳实践
