# 各种模型接口详细使用方法

## 概述

本章将详细介绍主流AI模型接口的使用方法，包括商业模型（OpenAI、<PERSON>、Gemini等）和开源模型的接口调用。我们将从基础的API调用开始，逐步深入到高级功能和最佳实践。

## OpenAI接口详解

### 基础配置和认证

```python
# openai_client.py
import openai
from openai import OpenAI
import os
from typing import Dict, List, Optional, Any
import asyncio
import aiohttp
import json
from datetime import datetime

class OpenAIClient:
    """OpenAI API客户端封装"""
    
    def __init__(self, api_key: str = None, base_url: str = None):
        """
        初始化OpenAI客户端
        
        Args:
            api_key: OpenAI API密钥
            base_url: API基础URL（用于代理或自定义端点）
        """
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        self.base_url = base_url or "https://api.openai.com/v1"
        
        if not self.api_key:
            raise ValueError("OpenAI API密钥未设置")
        
        # 初始化同步客户端
        self.client = OpenAI(
            api_key=self.api_key,
            base_url=self.base_url
        )
        
        # 支持的模型列表
        self.supported_models = {
            "gpt-4": {"max_tokens": 8192, "cost_per_1k": 0.03},
            "gpt-4-turbo": {"max_tokens": 128000, "cost_per_1k": 0.01},
            "gpt-3.5-turbo": {"max_tokens": 4096, "cost_per_1k": 0.002},
            "gpt-3.5-turbo-16k": {"max_tokens": 16384, "cost_per_1k": 0.004}
        }
    
    def chat_completion(self, 
                       messages: List[Dict[str, str]], 
                       model: str = "gpt-3.5-turbo",
                       temperature: float = 0.7,
                       max_tokens: Optional[int] = None,
                       stream: bool = False,
                       **kwargs) -> Dict[str, Any]:
        """
        聊天完成API调用
        
        Args:
            messages: 对话消息列表
            model: 使用的模型名称
            temperature: 温度参数（0-2）
            max_tokens: 最大token数
            stream: 是否使用流式输出
            **kwargs: 其他参数
        
        Returns:
            API响应结果
        """
        try:
            # 验证模型
            if model not in self.supported_models:
                raise ValueError(f"不支持的模型: {model}")
            
            # 设置默认max_tokens
            if max_tokens is None:
                max_tokens = min(1000, self.supported_models[model]["max_tokens"] // 2)
            
            # 调用API
            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=stream,
                **kwargs
            )
            
            if stream:
                return self._handle_stream_response(response)
            else:
                return self._format_response(response)
                
        except Exception as e:
            return self._handle_error(e)
    
    def _handle_stream_response(self, response):
        """处理流式响应"""
        def stream_generator():
            for chunk in response:
                if chunk.choices[0].delta.content is not None:
                    yield {
                        "content": chunk.choices[0].delta.content,
                        "finish_reason": chunk.choices[0].finish_reason
                    }
        
        return stream_generator()
    
    def _format_response(self, response) -> Dict[str, Any]:
        """格式化API响应"""
        return {
            "content": response.choices[0].message.content,
            "model": response.model,
            "usage": {
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.total_tokens
            },
            "finish_reason": response.choices[0].finish_reason,
            "timestamp": datetime.now().isoformat()
        }
    
    def _handle_error(self, error) -> Dict[str, Any]:
        """处理API错误"""
        error_info = {
            "error": True,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "timestamp": datetime.now().isoformat()
        }
        
        # 根据错误类型提供具体信息
        if "rate limit" in str(error).lower():
            error_info["suggestion"] = "请求频率过高，建议降低请求频率或升级API计划"
        elif "insufficient_quota" in str(error).lower():
            error_info["suggestion"] = "API配额不足，请检查账户余额"
        elif "invalid_api_key" in str(error).lower():
            error_info["suggestion"] = "API密钥无效，请检查密钥设置"
        
        return error_info
    
    async def async_chat_completion(self, 
                                  messages: List[Dict[str, str]], 
                                  model: str = "gpt-3.5-turbo",
                                  **kwargs) -> Dict[str, Any]:
        """异步聊天完成"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": model,
            "messages": messages,
            **kwargs
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=data
            ) as response:
                result = await response.json()
                
                if response.status == 200:
                    return self._format_async_response(result)
                else:
                    return self._handle_async_error(result)
    
    def _format_async_response(self, response: Dict) -> Dict[str, Any]:
        """格式化异步响应"""
        return {
            "content": response["choices"][0]["message"]["content"],
            "model": response["model"],
            "usage": response["usage"],
            "finish_reason": response["choices"][0]["finish_reason"],
            "timestamp": datetime.now().isoformat()
        }
    
    def _handle_async_error(self, error_response: Dict) -> Dict[str, Any]:
        """处理异步错误"""
        return {
            "error": True,
            "error_code": error_response.get("error", {}).get("code"),
            "error_message": error_response.get("error", {}).get("message"),
            "timestamp": datetime.now().isoformat()
        }
    
    def get_models(self) -> List[Dict[str, Any]]:
        """获取可用模型列表"""
        try:
            models = self.client.models.list()
            return [
                {
                    "id": model.id,
                    "created": model.created,
                    "owned_by": model.owned_by
                }
                for model in models.data
                if model.id.startswith(("gpt-", "text-", "code-"))
            ]
        except Exception as e:
            return [{"error": str(e)}]
    
    def estimate_cost(self, prompt_tokens: int, completion_tokens: int, 
                     model: str = "gpt-3.5-turbo") -> float:
        """估算API调用成本"""
        if model not in self.supported_models:
            return 0.0
        
        cost_per_1k = self.supported_models[model]["cost_per_1k"]
        total_tokens = prompt_tokens + completion_tokens
        
        return (total_tokens / 1000) * cost_per_1k

# 使用示例
def demo_openai_usage():
    """OpenAI使用示例"""
    # 初始化客户端
    client = OpenAIClient()
    
    # 简单对话
    messages = [
        {"role": "system", "content": "你是一个有帮助的AI助手。"},
        {"role": "user", "content": "请解释什么是机器学习？"}
    ]
    
    # 同步调用
    response = client.chat_completion(messages)
    print("同步响应:", response)
    
    # 流式调用
    stream_response = client.chat_completion(messages, stream=True)
    print("流式响应:")
    for chunk in stream_response:
        print(chunk["content"], end="")
    
    # 获取模型列表
    models = client.get_models()
    print(f"可用模型数量: {len(models)}")
    
    return client
```

### 高级功能使用

```python
# openai_advanced.py
from typing import Dict, List, Any, Optional
import json
import base64
from PIL import Image
import io

class OpenAIAdvanced(OpenAIClient):
    """OpenAI高级功能封装"""
    
    def function_calling(self, 
                        messages: List[Dict[str, str]],
                        functions: List[Dict[str, Any]],
                        function_call: str = "auto",
                        model: str = "gpt-3.5-turbo") -> Dict[str, Any]:
        """
        函数调用功能
        
        Args:
            messages: 对话消息
            functions: 可调用的函数定义
            function_call: 函数调用策略
            model: 使用的模型
        """
        try:
            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                functions=functions,
                function_call=function_call
            )
            
            result = self._format_response(response)
            
            # 检查是否有函数调用
            if response.choices[0].message.function_call:
                function_call_info = response.choices[0].message.function_call
                result["function_call"] = {
                    "name": function_call_info.name,
                    "arguments": json.loads(function_call_info.arguments)
                }
            
            return result
            
        except Exception as e:
            return self._handle_error(e)
    
    def vision_analysis(self, 
                       image_path: str, 
                       prompt: str = "请描述这张图片",
                       model: str = "gpt-4-vision-preview") -> Dict[str, Any]:
        """
        图像分析功能
        
        Args:
            image_path: 图片路径
            prompt: 分析提示
            model: 视觉模型
        """
        try:
            # 读取并编码图片
            with open(image_path, "rb") as image_file:
                image_data = base64.b64encode(image_file.read()).decode('utf-8')
            
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_data}"
                            }
                        }
                    ]
                }
            ]
            
            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                max_tokens=1000
            )
            
            return self._format_response(response)
            
        except Exception as e:
            return self._handle_error(e)
    
    def text_to_speech(self, 
                      text: str, 
                      voice: str = "alloy",
                      model: str = "tts-1") -> bytes:
        """
        文本转语音
        
        Args:
            text: 要转换的文本
            voice: 语音类型
            model: TTS模型
        """
        try:
            response = self.client.audio.speech.create(
                model=model,
                voice=voice,
                input=text
            )
            
            return response.content
            
        except Exception as e:
            print(f"TTS错误: {e}")
            return b""
    
    def speech_to_text(self, 
                      audio_file_path: str,
                      model: str = "whisper-1") -> Dict[str, Any]:
        """
        语音转文本
        
        Args:
            audio_file_path: 音频文件路径
            model: STT模型
        """
        try:
            with open(audio_file_path, "rb") as audio_file:
                response = self.client.audio.transcriptions.create(
                    model=model,
                    file=audio_file
                )
            
            return {
                "text": response.text,
                "model": model,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return self._handle_error(e)
    
    def create_embedding(self, 
                        text: str, 
                        model: str = "text-embedding-ada-002") -> Dict[str, Any]:
        """
        创建文本嵌入
        
        Args:
            text: 输入文本
            model: 嵌入模型
        """
        try:
            response = self.client.embeddings.create(
                model=model,
                input=text
            )
            
            return {
                "embedding": response.data[0].embedding,
                "model": response.model,
                "usage": response.usage.dict(),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return self._handle_error(e)

# 函数调用示例
def get_weather(location: str, unit: str = "celsius") -> str:
    """获取天气信息的示例函数"""
    return f"{location}的天气是晴天，温度25°{unit}"

def demo_advanced_features():
    """高级功能演示"""
    client = OpenAIAdvanced()
    
    # 函数调用示例
    functions = [
        {
            "name": "get_weather",
            "description": "获取指定地点的天气信息",
            "parameters": {
                "type": "object",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": "城市名称"
                    },
                    "unit": {
                        "type": "string",
                        "enum": ["celsius", "fahrenheit"],
                        "description": "温度单位"
                    }
                },
                "required": ["location"]
            }
        }
    ]
    
    messages = [
        {"role": "user", "content": "北京今天天气怎么样？"}
    ]
    
    response = client.function_calling(messages, functions)
    print("函数调用响应:", response)
    
    # 嵌入创建示例
    embedding_response = client.create_embedding("这是一个测试文本")
    print(f"嵌入维度: {len(embedding_response.get('embedding', []))}")
    
    return client
```

## Anthropic Claude接口

### Claude API基础使用

```python
# claude_client.py
import anthropic
import os
from typing import Dict, List, Optional, Any, Generator
import asyncio
import aiohttp
import json
from datetime import datetime

class ClaudeClient:
    """Anthropic Claude API客户端"""
    
    def __init__(self, api_key: str = None):
        """
        初始化Claude客户端
        
        Args:
            api_key: Anthropic API密钥
        """
        self.api_key = api_key or os.getenv("ANTHROPIC_API_KEY")
        
        if not self.api_key:
            raise ValueError("Anthropic API密钥未设置")
        
        self.client = anthropic.Anthropic(api_key=self.api_key)
        
        # 支持的模型
        self.supported_models = {
            "claude-3-opus-20240229": {"max_tokens": 200000, "cost_per_1k": 0.015},
            "claude-3-sonnet-20240229": {"max_tokens": 200000, "cost_per_1k": 0.003},
            "claude-3-haiku-20240307": {"max_tokens": 200000, "cost_per_1k": 0.00025},
            "claude-2.1": {"max_tokens": 200000, "cost_per_1k": 0.008},
            "claude-2.0": {"max_tokens": 100000, "cost_per_1k": 0.008}
        }
    
    def chat_completion(self, 
                       messages: List[Dict[str, str]], 
                       model: str = "claude-3-sonnet-20240229",
                       max_tokens: int = 1000,
                       temperature: float = 0.7,
                       system: Optional[str] = None,
                       stream: bool = False) -> Dict[str, Any]:
        """
        Claude聊天完成
        
        Args:
            messages: 对话消息列表
            model: 使用的模型
            max_tokens: 最大token数
            temperature: 温度参数
            system: 系统提示
            stream: 是否流式输出
        """
        try:
            # 验证模型
            if model not in self.supported_models:
                raise ValueError(f"不支持的模型: {model}")
            
            # 构建请求参数
            request_params = {
                "model": model,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "messages": messages
            }
            
            if system:
                request_params["system"] = system
            
            if stream:
                request_params["stream"] = True
                response = self.client.messages.create(**request_params)
                return self._handle_stream_response(response)
            else:
                response = self.client.messages.create(**request_params)
                return self._format_response(response)
                
        except Exception as e:
            return self._handle_error(e)
    
    def _handle_stream_response(self, response) -> Generator[Dict[str, Any], None, None]:
        """处理流式响应"""
        for chunk in response:
            if chunk.type == "content_block_delta":
                yield {
                    "content": chunk.delta.text,
                    "type": "content_delta"
                }
            elif chunk.type == "message_stop":
                yield {
                    "content": "",
                    "type": "stop",
                    "stop_reason": chunk.stop_reason if hasattr(chunk, 'stop_reason') else None
                }
    
    def _format_response(self, response) -> Dict[str, Any]:
        """格式化响应"""
        return {
            "content": response.content[0].text if response.content else "",
            "model": response.model,
            "usage": {
                "input_tokens": response.usage.input_tokens,
                "output_tokens": response.usage.output_tokens,
                "total_tokens": response.usage.input_tokens + response.usage.output_tokens
            },
            "stop_reason": response.stop_reason,
            "timestamp": datetime.now().isoformat()
        }
    
    def _handle_error(self, error) -> Dict[str, Any]:
        """处理错误"""
        error_info = {
            "error": True,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "timestamp": datetime.now().isoformat()
        }
        
        # Claude特定错误处理
        if "rate_limit" in str(error).lower():
            error_info["suggestion"] = "请求频率过高，请稍后重试"
        elif "invalid_api_key" in str(error).lower():
            error_info["suggestion"] = "API密钥无效，请检查设置"
        elif "context_length_exceeded" in str(error).lower():
            error_info["suggestion"] = "上下文长度超限，请减少输入内容"
        
        return error_info
    
    def count_tokens(self, text: str) -> int:
        """
        估算token数量（简化实现）
        Claude使用不同的tokenizer，这里提供近似计算
        """
        # 简化的token计算：英文约4字符/token，中文约1.5字符/token
        chinese_chars = len([c for c in text if '\u4e00' <= c <= '\u9fff'])
        other_chars = len(text) - chinese_chars
        
        estimated_tokens = chinese_chars / 1.5 + other_chars / 4
        return int(estimated_tokens)
    
    def estimate_cost(self, input_tokens: int, output_tokens: int, 
                     model: str = "claude-3-sonnet-20240229") -> float:
        """估算API调用成本"""
        if model not in self.supported_models:
            return 0.0
        
        cost_per_1k = self.supported_models[model]["cost_per_1k"]
        total_tokens = input_tokens + output_tokens
        
        return (total_tokens / 1000) * cost_per_1k
    
    async def async_chat_completion(self, 
                                  messages: List[Dict[str, str]], 
                                  model: str = "claude-3-sonnet-20240229",
                                  **kwargs) -> Dict[str, Any]:
        """异步聊天完成"""
        headers = {
            "x-api-key": self.api_key,
            "Content-Type": "application/json",
            "anthropic-version": "2023-06-01"
        }
        
        data = {
            "model": model,
            "messages": messages,
            "max_tokens": kwargs.get("max_tokens", 1000),
            **kwargs
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                "https://api.anthropic.com/v1/messages",
                headers=headers,
                json=data
            ) as response:
                result = await response.json()
                
                if response.status == 200:
                    return self._format_async_response(result)
                else:
                    return self._handle_async_error(result)
    
    def _format_async_response(self, response: Dict) -> Dict[str, Any]:
        """格式化异步响应"""
        return {
            "content": response["content"][0]["text"] if response.get("content") else "",
            "model": response.get("model"),
            "usage": response.get("usage", {}),
            "stop_reason": response.get("stop_reason"),
            "timestamp": datetime.now().isoformat()
        }
    
    def _handle_async_error(self, error_response: Dict) -> Dict[str, Any]:
        """处理异步错误"""
        return {
            "error": True,
            "error_type": error_response.get("error", {}).get("type"),
            "error_message": error_response.get("error", {}).get("message"),
            "timestamp": datetime.now().isoformat()
        }

# 使用示例
def demo_claude_usage():
    """Claude使用示例"""
    client = ClaudeClient()
    
    # 基础对话
    messages = [
        {"role": "user", "content": "请解释量子计算的基本原理"}
    ]
    
    # 带系统提示的对话
    system_prompt = "你是一个专业的科学解释者，善于用简单易懂的语言解释复杂概念。"
    
    response = client.chat_completion(
        messages=messages,
        system=system_prompt,
        model="claude-3-sonnet-20240229",
        max_tokens=1500
    )
    
    print("Claude响应:", response)
    
    # 流式输出示例
    print("\n流式输出:")
    stream_response = client.chat_completion(
        messages=messages,
        system=system_prompt,
        stream=True
    )
    
    for chunk in stream_response:
        if chunk["type"] == "content_delta":
            print(chunk["content"], end="")
        elif chunk["type"] == "stop":
            print(f"\n停止原因: {chunk['stop_reason']}")
    
    return client
```

## Google Gemini接口

### Gemini API使用

```python
# gemini_client.py
import google.generativeai as genai
import os
from typing import Dict, List, Optional, Any, Generator
import asyncio
from datetime import datetime
import json

class GeminiClient:
    """Google Gemini API客户端"""
    
    def __init__(self, api_key: str = None):
        """
        初始化Gemini客户端
        
        Args:
            api_key: Google AI API密钥
        """
        self.api_key = api_key or os.getenv("GOOGLE_AI_API_KEY")
        
        if not self.api_key:
            raise ValueError("Google AI API密钥未设置")
        
        genai.configure(api_key=self.api_key)
        
        # 支持的模型
        self.supported_models = {
            "gemini-pro": {"max_tokens": 30720, "multimodal": False},
            "gemini-pro-vision": {"max_tokens": 12288, "multimodal": True},
            "gemini-1.5-pro": {"max_tokens": 1048576, "multimodal": True},
            "gemini-1.5-flash": {"max_tokens": 1048576, "multimodal": True}
        }
    
    def chat_completion(self, 
                       messages: List[Dict[str, str]], 
                       model: str = "gemini-pro",
                       temperature: float = 0.7,
                       max_output_tokens: Optional[int] = None,
                       stream: bool = False) -> Dict[str, Any]:
        """
        Gemini聊天完成
        
        Args:
            messages: 对话消息列表
            model: 使用的模型
            temperature: 温度参数
            max_output_tokens: 最大输出token数
            stream: 是否流式输出
        """
        try:
            # 验证模型
            if model not in self.supported_models:
                raise ValueError(f"不支持的模型: {model}")
            
            # 初始化模型
            generation_config = genai.types.GenerationConfig(
                temperature=temperature,
                max_output_tokens=max_output_tokens
            )
            
            model_instance = genai.GenerativeModel(
                model_name=model,
                generation_config=generation_config
            )
            
            # 转换消息格式
            prompt = self._convert_messages_to_prompt(messages)
            
            if stream:
                response = model_instance.generate_content(
                    prompt,
                    stream=True
                )
                return self._handle_stream_response(response)
            else:
                response = model_instance.generate_content(prompt)
                return self._format_response(response, model)
                
        except Exception as e:
            return self._handle_error(e)
    
    def _convert_messages_to_prompt(self, messages: List[Dict[str, str]]) -> str:
        """将消息列表转换为Gemini格式的提示"""
        prompt_parts = []
        
        for message in messages:
            role = message.get("role", "user")
            content = message.get("content", "")
            
            if role == "system":
                prompt_parts.append(f"System: {content}")
            elif role == "user":
                prompt_parts.append(f"Human: {content}")
            elif role == "assistant":
                prompt_parts.append(f"Assistant: {content}")
        
        return "\n\n".join(prompt_parts)
    
    def _handle_stream_response(self, response) -> Generator[Dict[str, Any], None, None]:
        """处理流式响应"""
        for chunk in response:
            if chunk.text:
                yield {
                    "content": chunk.text,
                    "type": "content_delta"
                }
        
        yield {
            "content": "",
            "type": "stop"
        }
    
    def _format_response(self, response, model: str) -> Dict[str, Any]:
        """格式化响应"""
        try:
            content = response.text if hasattr(response, 'text') else ""
            
            # 获取使用统计（如果可用）
            usage_metadata = {}
            if hasattr(response, 'usage_metadata'):
                usage_metadata = {
                    "prompt_token_count": getattr(response.usage_metadata, 'prompt_token_count', 0),
                    "candidates_token_count": getattr(response.usage_metadata, 'candidates_token_count', 0),
                    "total_token_count": getattr(response.usage_metadata, 'total_token_count', 0)
                }
            
            return {
                "content": content,
                "model": model,
                "usage": usage_metadata,
                "finish_reason": getattr(response, 'finish_reason', None),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "content": "",
                "error": f"响应格式化失败: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
    
    def _handle_error(self, error) -> Dict[str, Any]:
        """处理错误"""
        error_info = {
            "error": True,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "timestamp": datetime.now().isoformat()
        }
        
        # Gemini特定错误处理
        if "quota" in str(error).lower():
            error_info["suggestion"] = "API配额不足，请检查使用限制"
        elif "invalid" in str(error).lower() and "key" in str(error).lower():
            error_info["suggestion"] = "API密钥无效，请检查设置"
        elif "safety" in str(error).lower():
            error_info["suggestion"] = "内容被安全过滤器拦截，请调整输入内容"
        
        return error_info
    
    def multimodal_chat(self, 
                       text_prompt: str,
                       image_path: Optional[str] = None,
                       model: str = "gemini-pro-vision") -> Dict[str, Any]:
        """
        多模态聊天（文本+图像）
        
        Args:
            text_prompt: 文本提示
            image_path: 图像文件路径
            model: 使用的多模态模型
        """
        try:
            if not self.supported_models[model]["multimodal"]:
                raise ValueError(f"模型 {model} 不支持多模态输入")
            
            model_instance = genai.GenerativeModel(model_name=model)
            
            # 准备输入内容
            content_parts = [text_prompt]
            
            if image_path:
                # 读取图像
                import PIL.Image
                image = PIL.Image.open(image_path)
                content_parts.append(image)
            
            response = model_instance.generate_content(content_parts)
            return self._format_response(response, model)
            
        except Exception as e:
            return self._handle_error(e)
    
    def get_available_models(self) -> List[Dict[str, Any]]:
        """获取可用模型列表"""
        try:
            models = []
            for model in genai.list_models():
                if 'generateContent' in model.supported_generation_methods:
                    models.append({
                        "name": model.name,
                        "display_name": model.display_name,
                        "description": model.description,
                        "input_token_limit": model.input_token_limit,
                        "output_token_limit": model.output_token_limit
                    })
            return models
        except Exception as e:
            return [{"error": str(e)}]

# 使用示例
def demo_gemini_usage():
    """Gemini使用示例"""
    client = GeminiClient()
    
    # 基础文本对话
    messages = [
        {"role": "user", "content": "请解释深度学习和机器学习的区别"}
    ]
    
    response = client.chat_completion(
        messages=messages,
        model="gemini-pro",
        temperature=0.7
    )
    
    print("Gemini响应:", response)
    
    # 流式输出示例
    print("\n流式输出:")
    stream_response = client.chat_completion(
        messages=messages,
        stream=True
    )
    
    for chunk in stream_response:
        if chunk["type"] == "content_delta":
            print(chunk["content"], end="")
        elif chunk["type"] == "stop":
            print("\n[流式输出结束]")
    
    # 获取可用模型
    models = client.get_available_models()
    print(f"\n可用模型数量: {len(models)}")
    
    return client
```

## 本地模型部署与接口

### Ollama本地模型接口

```python
# ollama_client.py
import requests
import json
from typing import Dict, List, Optional, Any, Generator
import asyncio
import aiohttp
from datetime import datetime

class OllamaClient:
    """Ollama本地模型客户端"""
    
    def __init__(self, base_url: str = "http://localhost:11434"):
        """
        初始化Ollama客户端
        
        Args:
            base_url: Ollama服务地址
        """
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        
        # 检查服务可用性
        self._check_service()
    
    def _check_service(self):
        """检查Ollama服务是否可用"""
        try:
            response = self.session.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code != 200:
                raise ConnectionError("Ollama服务不可用")
        except requests.exceptions.RequestException:
            raise ConnectionError("无法连接到Ollama服务，请确保服务已启动")
    
    def chat_completion(self, 
                       messages: List[Dict[str, str]], 
                       model: str = "llama2",
                       temperature: float = 0.7,
                       stream: bool = False,
                       **kwargs) -> Dict[str, Any]:
        """
        聊天完成
        
        Args:
            messages: 对话消息列表
            model: 使用的模型名称
            temperature: 温度参数
            stream: 是否流式输出
            **kwargs: 其他参数
        """
        try:
            # 构建请求数据
            data = {
                "model": model,
                "messages": messages,
                "stream": stream,
                "options": {
                    "temperature": temperature,
                    **kwargs
                }
            }
            
            if stream:
                return self._handle_stream_request(data)
            else:
                response = self.session.post(
                    f"{self.base_url}/api/chat",
                    json=data,
                    timeout=60
                )
                
                if response.status_code == 200:
                    result = response.json()
                    return self._format_response(result, model)
                else:
                    return self._handle_error_response(response)
                    
        except Exception as e:
            return self._handle_error(e)
    
    def _handle_stream_request(self, data: Dict) -> Generator[Dict[str, Any], None, None]:
        """处理流式请求"""
        try:
            response = self.session.post(
                f"{self.base_url}/api/chat",
                json=data,
                stream=True,
                timeout=60
            )
            
            if response.status_code == 200:
                for line in response.iter_lines():
                    if line:
                        try:
                            chunk_data = json.loads(line.decode('utf-8'))
                            
                            if chunk_data.get("message", {}).get("content"):
                                yield {
                                    "content": chunk_data["message"]["content"],
                                    "type": "content_delta",
                                    "done": chunk_data.get("done", False)
                                }
                            
                            if chunk_data.get("done", False):
                                yield {
                                    "content": "",
                                    "type": "stop",
                                    "total_duration": chunk_data.get("total_duration"),
                                    "load_duration": chunk_data.get("load_duration"),
                                    "prompt_eval_count": chunk_data.get("prompt_eval_count"),
                                    "eval_count": chunk_data.get("eval_count")
                                }
                                break
                                
                        except json.JSONDecodeError:
                            continue
            else:
                yield {"error": f"HTTP {response.status_code}: {response.text}"}
                
        except Exception as e:
            yield {"error": str(e)}
    
    def _format_response(self, response: Dict, model: str) -> Dict[str, Any]:
        """格式化响应"""
        message = response.get("message", {})
        
        return {
            "content": message.get("content", ""),
            "model": model,
            "usage": {
                "prompt_eval_count": response.get("prompt_eval_count", 0),
                "eval_count": response.get("eval_count", 0),
                "total_duration": response.get("total_duration", 0),
                "load_duration": response.get("load_duration", 0)
            },
            "done": response.get("done", True),
            "timestamp": datetime.now().isoformat()
        }
    
    def _handle_error_response(self, response) -> Dict[str, Any]:
        """处理错误响应"""
        try:
            error_data = response.json()
            error_message = error_data.get("error", response.text)
        except:
            error_message = response.text
        
        return {
            "error": True,
            "error_code": response.status_code,
            "error_message": error_message,
            "timestamp": datetime.now().isoformat()
        }
    
    def _handle_error(self, error) -> Dict[str, Any]:
        """处理异常"""
        return {
            "error": True,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "timestamp": datetime.now().isoformat()
        }
    
    def get_models(self) -> List[Dict[str, Any]]:
        """获取可用模型列表"""
        try:
            response = self.session.get(f"{self.base_url}/api/tags")
            
            if response.status_code == 200:
                data = response.json()
                return data.get("models", [])
            else:
                return [{"error": f"HTTP {response.status_code}: {response.text}"}]
                
        except Exception as e:
            return [{"error": str(e)}]
    
    def pull_model(self, model_name: str) -> Generator[Dict[str, Any], None, None]:
        """
        拉取模型
        
        Args:
            model_name: 要拉取的模型名称
        """
        try:
            data = {"name": model_name}
            
            response = self.session.post(
                f"{self.base_url}/api/pull",
                json=data,
                stream=True,
                timeout=300
            )
            
            if response.status_code == 200:
                for line in response.iter_lines():
                    if line:
                        try:
                            chunk_data = json.loads(line.decode('utf-8'))
                            yield chunk_data
                        except json.JSONDecodeError:
                            continue
            else:
                yield {"error": f"HTTP {response.status_code}: {response.text}"}
                
        except Exception as e:
            yield {"error": str(e)}
    
    def generate_embedding(self, 
                          text: str, 
                          model: str = "llama2") -> Dict[str, Any]:
        """
        生成文本嵌入
        
        Args:
            text: 输入文本
            model: 使用的模型
        """
        try:
            data = {
                "model": model,
                "prompt": text
            }
            
            response = self.session.post(
                f"{self.base_url}/api/embeddings",
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    "embedding": result.get("embedding", []),
                    "model": model,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return self._handle_error_response(response)
                
        except Exception as e:
            return self._handle_error(e)

# 使用示例
def demo_ollama_usage():
    """Ollama使用示例"""
    try:
        client = OllamaClient()
        
        # 获取可用模型
        models = client.get_models()
        print(f"可用模型: {[model.get('name', 'unknown') for model in models]}")
        
        if not models or models[0].get("error"):
            print("没有可用模型，请先拉取模型")
            return
        
        # 使用第一个可用模型进行对话
        model_name = models[0]["name"]
        
        messages = [
            {"role": "user", "content": "请简单介绍一下人工智能"}
        ]
        
        # 同步调用
        response = client.chat_completion(
            messages=messages,
            model=model_name,
            temperature=0.7
        )
        
        print(f"模型 {model_name} 响应:", response)
        
        # 流式调用
        print("\n流式输出:")
        stream_response = client.chat_completion(
            messages=messages,
            model=model_name,
            stream=True
        )
        
        for chunk in stream_response:
            if chunk.get("type") == "content_delta":
                print(chunk["content"], end="")
            elif chunk.get("type") == "stop":
                print(f"\n[完成] 用时: {chunk.get('total_duration', 0) / 1e9:.2f}秒")
        
        return client
        
    except ConnectionError as e:
        print(f"连接错误: {e}")
        print("请确保Ollama服务已启动：ollama serve")
        return None
```

## 其他重要模型接口

### Hugging Face Transformers

```python
# huggingface_client.py
from transformers import pipeline, AutoTokenizer, AutoModelForCausalLM
import torch
from typing import Dict, List, Optional, Any
import asyncio
from datetime import datetime

class HuggingFaceClient:
    """Hugging Face模型客户端"""

    def __init__(self, model_name: str = "microsoft/DialoGPT-medium"):
        """
        初始化HuggingFace客户端

        Args:
            model_name: 模型名称
        """
        self.model_name = model_name
        self.device = "cuda" if torch.cuda.is_available() else "cpu"

        # 初始化模型和tokenizer
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModelForCausalLM.from_pretrained(model_name)
        self.model.to(self.device)

        # 设置pad_token
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token

    def generate_text(self,
                     prompt: str,
                     max_length: int = 100,
                     temperature: float = 0.7,
                     num_return_sequences: int = 1) -> Dict[str, Any]:
        """
        生成文本

        Args:
            prompt: 输入提示
            max_length: 最大长度
            temperature: 温度参数
            num_return_sequences: 返回序列数量
        """
        try:
            # 编码输入
            inputs = self.tokenizer.encode(prompt, return_tensors="pt").to(self.device)

            # 生成文本
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_length=max_length,
                    temperature=temperature,
                    num_return_sequences=num_return_sequences,
                    pad_token_id=self.tokenizer.eos_token_id,
                    do_sample=True
                )

            # 解码输出
            generated_texts = []
            for output in outputs:
                text = self.tokenizer.decode(output, skip_special_tokens=True)
                # 移除原始提示
                if text.startswith(prompt):
                    text = text[len(prompt):].strip()
                generated_texts.append(text)

            return {
                "generated_texts": generated_texts,
                "model": self.model_name,
                "prompt": prompt,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            return {
                "error": True,
                "error_message": str(e),
                "timestamp": datetime.now().isoformat()
            }

# 使用示例
def demo_huggingface_usage():
    """HuggingFace使用示例"""
    try:
        client = HuggingFaceClient("gpt2")

        response = client.generate_text(
            prompt="人工智能的未来发展",
            max_length=150,
            temperature=0.8
        )

        print("HuggingFace响应:", response)
        return client

    except Exception as e:
        print(f"HuggingFace客户端初始化失败: {e}")
        return None
```

### 百度文心一言API

```python
# ernie_client.py
import requests
import json
from typing import Dict, List, Optional, Any
import time
from datetime import datetime

class ErnieClient:
    """百度文心一言API客户端"""

    def __init__(self, api_key: str, secret_key: str):
        """
        初始化文心一言客户端

        Args:
            api_key: API Key
            secret_key: Secret Key
        """
        self.api_key = api_key
        self.secret_key = secret_key
        self.access_token = None
        self.token_expires_at = 0

        # 获取访问令牌
        self._get_access_token()

    def _get_access_token(self):
        """获取访问令牌"""
        url = "https://aip.baidubce.com/oauth/2.0/token"

        params = {
            "grant_type": "client_credentials",
            "client_id": self.api_key,
            "client_secret": self.secret_key
        }

        response = requests.post(url, params=params)

        if response.status_code == 200:
            data = response.json()
            self.access_token = data.get("access_token")
            self.token_expires_at = time.time() + data.get("expires_in", 3600) - 300
        else:
            raise Exception(f"获取访问令牌失败: {response.text}")

    def _ensure_valid_token(self):
        """确保令牌有效"""
        if time.time() >= self.token_expires_at:
            self._get_access_token()

    def chat_completion(self,
                       messages: List[Dict[str, str]],
                       model: str = "ernie-bot",
                       temperature: float = 0.7,
                       stream: bool = False) -> Dict[str, Any]:
        """
        聊天完成

        Args:
            messages: 对话消息列表
            model: 模型名称
            temperature: 温度参数
            stream: 是否流式输出
        """
        try:
            self._ensure_valid_token()

            # 模型端点映射
            model_endpoints = {
                "ernie-bot": "rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions",
                "ernie-bot-turbo": "rpc/2.0/ai_custom/v1/wenxinworkshop/chat/eb-instant",
                "ernie-bot-4": "rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions_pro"
            }

            endpoint = model_endpoints.get(model, model_endpoints["ernie-bot"])
            url = f"https://aip.baidubce.com/{endpoint}?access_token={self.access_token}"

            data = {
                "messages": messages,
                "temperature": temperature,
                "stream": stream
            }

            response = requests.post(url, json=data, timeout=60)

            if response.status_code == 200:
                result = response.json()
                return self._format_response(result, model)
            else:
                return self._handle_error_response(response)

        except Exception as e:
            return self._handle_error(e)

    def _format_response(self, response: Dict, model: str) -> Dict[str, Any]:
        """格式化响应"""
        return {
            "content": response.get("result", ""),
            "model": model,
            "usage": response.get("usage", {}),
            "timestamp": datetime.now().isoformat()
        }

    def _handle_error_response(self, response) -> Dict[str, Any]:
        """处理错误响应"""
        try:
            error_data = response.json()
            return {
                "error": True,
                "error_code": error_data.get("error_code"),
                "error_message": error_data.get("error_msg"),
                "timestamp": datetime.now().isoformat()
            }
        except:
            return {
                "error": True,
                "error_message": response.text,
                "timestamp": datetime.now().isoformat()
            }

    def _handle_error(self, error) -> Dict[str, Any]:
        """处理异常"""
        return {
            "error": True,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "timestamp": datetime.now().isoformat()
        }
```

## 接口使用最佳实践

### 1. 统一错误处理
- 实现标准化的错误响应格式
- 提供详细的错误信息和建议
- 区分不同类型的错误（网络、API、业务）

### 2. 性能优化
- 使用连接池减少连接开销
- 实现请求重试机制
- 合理设置超时时间

### 3. 安全考虑
- 安全存储API密钥
- 实现请求签名验证
- 防止API密钥泄露

### 4. 监控和日志
- 记录详细的API调用日志
- 监控API响应时间和成功率
- 实现告警机制

这个文档提供了主流AI模型接口的详细使用方法，包括OpenAI、Claude、Gemini和本地模型Ollama的完整实现。每个客户端都包含了错误处理、流式输出、异步调用等高级功能，为后续的接口整合奠定了基础。
