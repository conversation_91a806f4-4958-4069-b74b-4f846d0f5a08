# LLM API 串接应用完全攻略 - 完整课程目录

## 📚 核心课程（8个主要章节）

### 📖 第01章：人工智能起手式 - 淺談當代 AI 發展現況
- **时长**：4-5 小时
- **状态**：✅ 已完成详细内容
- **子课程**：
  - 1.1 人工智能简史：从图灵测试到 ChatGPT（90分钟）
  - 1.2 当代 AI 技术生态全景（120分钟）
- **重点内容**：AI 发展历程、技术生态、趋势分析
- **实践项目**：AI 发展报告撰写、产品竞品分析

### 🔌 第02章：大型语言模型（LLM）API 串接入门
- **时长**：6-7 小时
- **状态**：✅ 已完成详细内容
- **子课程**：
  - 2.1 API 基础知识与环境搭建（90分钟）
  - 2.2 OpenAI API 深度实践（120分钟）
  - 2.3 多厂商 API 对比与整合（150分钟）
- **重点内容**：API 调用、多厂商对比、最佳实践
- **实践项目**：API 性能测试工具、智能翻译服务

### 🤖 第03章：从 NLP 到 GPT - 打造记忆功能的聊天机器人
- **时长**：5-6 小时
- **状态**：✅ 已完成详细内容
- **子课程**：
  - 3.1 自然语言处理技术演进（90分钟）
  - 3.2 GPT 模型原理与应用（120分钟）
  - 3.3 构建记忆功能的聊天机器人（150分钟）
- **重点内容**：NLP 发展、GPT 原理、记忆管理、对话系统
- **实践项目**：个人助理机器人、智能学习伙伴

### 🔧 第04章：大型语言模型开发框架 - LangChain 与 LlamaIndex
- **时长**：6-7 小时
- **状态**：✅ 已完成详细内容
- **子课程**：
  - 4.1 LangChain 框架深度解析（120分钟）
  - 4.2 LlamaIndex 数据处理与检索（120分钟）
  - 4.3 框架对比与选择策略（90分钟）
  - 4.4 高级特性与性能优化（90分钟）
- **重点内容**：框架使用、组件开发、性能优化
- **实践项目**：个人知识助手、智能研究助手

### 🧮 第05章：自然语言模型的 Embeddings 与向量数据库
- **时长**：5-6 小时
- **状态**：📝 待创建详细内容
- **子课程**：
  - 5.1 Embeddings 原理与实现（120分钟）
  - 5.2 向量数据库技术详解（120分钟）
  - 5.3 语义搜索系统构建（120分钟）
- **重点内容**：向量化技术、相似度计算、语义搜索
- **实践项目**：语义搜索引擎、文档相似度分析系统

### 📚 第06章：为 AI 赋能知识库 - RAG 与 GraphRAG 技巧
- **时长**：6-7 小时
- **状态**：📝 待创建详细内容
- **子课程**：
  - 6.1 RAG 系统原理与架构（120分钟）
  - 6.2 高级 RAG 技术与优化（120分钟）
  - 6.3 GraphRAG 知识图谱增强（120分钟）
  - 6.4 RAG 系统评估与调优（60分钟）
- **重点内容**：检索增强生成、知识图谱、系统优化
- **实践项目**：企业知识问答系统、智能文档助手

### 🛠️ 第07章：从思考到行动：Function Calling 与 AI Agent
- **时长**：6-7 小时
- **状态**：📝 待创建详细内容
- **子课程**：
  - 7.1 Function Calling 原理与实现（120分钟）
  - 7.2 AI Agent 架构设计（120分钟）
  - 7.3 工具集成与外部 API 调用（120分钟）
  - 7.4 复杂任务的自动化执行（60分钟）
- **重点内容**：函数调用、代理系统、任务自动化
- **实践项目**：智能助手代理、自动化工作流系统

### 🎯 第08章：用 LLM 微调与 Ollama 让模型持续进化
- **时长**：5-6 小时
- **状态**：📝 待创建详细内容
- **子课程**：
  - 8.1 模型微调理论与实践（150分钟）
  - 8.2 Ollama 本地部署详解（120分钟）
  - 8.3 模型性能评估与优化（90分钟）
- **重点内容**：Fine-tuning、本地部署、性能优化
- **实践项目**：定制化模型训练、本地 LLM 服务部署

---

## 🚀 额外进阶课程（5个专题）

### 🔗 额外课程 #01：模型准备好了怎么用？整合接口一把抓
- **时长**：4-5 小时
- **状态**：📝 待创建详细内容
- **重点内容**：
  - Linebot 聊天机器人开发
  - Streamlit 快速原型构建
  - Gradio 交互界面设计
  - 多平台集成部署
- **实践项目**：多平台聊天机器人、Web 应用开发

### 🎨 额外课程 #02：从 LLM 到多模态模型应用
- **时长**：5-6 小时
- **状态**：📝 待创建详细内容
- **重点内容**：
  - 多模态模型原理
  - 图像+文本处理
  - 语音+文本集成
  - 跨模态应用开发
- **实践项目**：多模态内容生成器、智能图像分析系统

### 🔍 额外课程 #03：让电脑看得懂文字 - 向量数据库与嵌入模型深度应用
- **时长**：4-5 小时
- **状态**：📝 待创建详细内容
- **重点内容**：
  - 高级嵌入技术
  - 向量数据库优化
  - 大规模语义搜索
  - 实时推荐系统
- **实践项目**：大规模文档检索系统、智能推荐引擎

### 🔄 额外课程 #04：训练到部署一条龙 - Ollama ➟ HuggingFace 整合
- **时长**：4-5 小时
- **状态**：📝 待创建详细内容
- **重点内容**：
  - HuggingFace 生态系统
  - 模型训练与上传
  - Ollama 集成部署
  - 完整 MLOps 流程
- **实践项目**：端到端模型开发流程、模型版本管理系统

### 🤝 额外课程 #05：用 Agent 让 AI 动起来 - Multi-Agent 实战指南
- **时长**：4-5 小时
- **状态**：📝 待创建详细内容
- **重点内容**：
  - Multi-Agent 系统设计
  - 代理间通信协议
  - 任务分配与协调
  - 复杂场景应用
- **实践项目**：协作式 AI 团队、智能客服集群

---

## 📁 课程文件结构

```
LLM API 串接应用完全攻略/
├── 课程介绍.txt
├── 课程规划文档.md
├── 课程目录结构.md
│
├── 第01章_人工智能起手式/
│   ├── README.md                    ✅ 已完成
│   ├── 理论基础/
│   ├── 实践操作/
│   ├── 案例研究/
│   ├── 练习项目/
│   └── 参考资料/
│
├── 第02章_LLM_API串接入门/
│   ├── README.md                    ✅ 已完成
│   ├── 理论基础/
│   ├── 实践操作/
│   ├── 案例研究/
│   ├── 练习项目/
│   └── 参考资料/
│
├── 第03章_从NLP到GPT聊天机器人/
│   ├── README.md                    ✅ 已完成
│   ├── 理论基础/
│   ├── 实践操作/
│   ├── 案例研究/
│   ├── 练习项目/
│   └── 参考资料/
│
├── 第04章_LangChain与LlamaIndex框架/
│   ├── README.md                    ✅ 已完成
│   ├── 理论基础/
│   ├── 实践操作/
│   ├── 案例研究/
│   ├── 练习项目/
│   └── 参考资料/
│
├── 第05章_Embeddings与向量数据库/
│   ├── README.md                    📝 待完成
│   └── [其他子目录]
│
├── 第06章_RAG与GraphRAG技巧/
│   ├── README.md                    📝 待完成
│   └── [其他子目录]
│
├── 第07章_Function_Calling与AI_Agent/
│   ├── README.md                    📝 待完成
│   └── [其他子目录]
│
├── 第08章_LLM微调与Ollama部署/
│   ├── README.md                    📝 待完成
│   └── [其他子目录]
│
├── 额外课程01_模型整合接口应用/
│   ├── README.md                    📝 待完成
│   └── [其他子目录]
│
├── 额外课程02_多模态模型应用/
│   ├── README.md                    📝 待完成
│   └── [其他子目录]
│
├── 额外课程03_向量数据库深度应用/
│   ├── README.md                    📝 待完成
│   └── [其他子目录]
│
├── 额外课程04_Ollama_HuggingFace整合/
│   ├── README.md                    📝 待完成
│   └── [其他子目录]
│
├── 额外课程05_Multi_Agent实战指南/
│   ├── README.md                    📝 待完成
│   └── [其他子目录]
│
├── 共享资源/
│   ├── 代码模板/
│   ├── 数据集/
│   ├── 工具脚本/
│   └── 参考文档/
│
└── 项目实战/
    ├── 综合项目01_智能客服系统/
    ├── 综合项目02_知识管理平台/
    ├── 综合项目03_内容创作助手/
    └── 综合项目04_多模态应用/
```

## 📊 学习路径建议

### 🟢 初级路径（适合初学者）
**预计时长**：15-18 小时
- 第01章 → 第02章 → 第03章 → 额外课程 #01

### 🟡 中级路径（适合有基础的开发者）
**预计时长**：30-35 小时
- 第01-04章 → 第05-06章 → 额外课程 #02、#03

### 🔴 高级路径（适合专业开发者）
**预计时长**：60-75 小时
- 完整核心课程 + 所有额外课程

## 📈 课程进度追踪

- ✅ **已完成**：第01-04章详细内容
- 📝 **进行中**：第05-08章大纲规划
- ⏳ **待开始**：额外课程内容创建
- 🎯 **最终目标**：完整的课程体系与实践项目

---

**注意**：本目录结构展示了完整的课程规划，已完成的章节包含详细的教学内容、代码示例和实践项目。其余章节将按照相同的标准和结构进行开发。
