"""
Lang<PERSON>hain高级功能示例代码
包含自定义代理、复杂链编排、记忆管理等高级特性的完整示例
"""

from langchain.agents import Tool, AgentExecutor, BaseSingleActionAgent
from langchain.schema import AgentAction, AgentFinish
from langchain.prompts import StringPromptTemplate
from langchain.llms import OpenAI
from langchain.chains import <PERSON><PERSON>hain, SequentialChain
from langchain.memory import ConversationBufferMemory, ConversationSummaryMemory
from langchain.callbacks import BaseCallbackHandler
from typing import List, Union, Dict, Any, Optional
import re
import asyncio
import json
from datetime import datetime

# ============================================================================
# 1. 自定义代理示例
# ============================================================================

class CustomPromptTemplate(StringPromptTemplate):
    """自定义提示模板，支持动态工具列表和思维链"""
    
    template: str
    tools: List[Tool]
    
    def format(self, **kwargs) -> str:
        # 获取中间步骤
        intermediate_steps = kwargs.pop("intermediate_steps")
        thoughts = ""
        
        for action, observation in intermediate_steps:
            thoughts += action.log
            thoughts += f"\nObservation: {observation}\nThought: "
        
        # 设置代理可以停止的变量
        kwargs["agent_scratchpad"] = thoughts
        
        # 创建工具变量
        kwargs["tools"] = "\n".join([f"{tool.name}: {tool.description}" for tool in self.tools])
        kwargs["tool_names"] = ", ".join([tool.name for tool in self.tools])
        
        return self.template.format(**kwargs)

class AdvancedReasoningAgent(BaseSingleActionAgent):
    """高级推理代理，支持复杂的推理和决策"""
    
    def __init__(self, llm_chain: LLMChain, tools: List[Tool], **kwargs):
        super().__init__(**kwargs)
        self.llm_chain = llm_chain
        self.tools = tools
        self.tool_names = [tool.name for tool in tools]
        self.reasoning_history = []
    
    @property
    def input_keys(self):
        return ["input"]
    
    def plan(self, intermediate_steps: List[tuple], **kwargs) -> Union[AgentAction, AgentFinish]:
        """高级规划方法，包含推理验证"""
        # 调用LLM获取响应
        response = self.llm_chain.run(
            intermediate_steps=intermediate_steps,
            **kwargs
        )
        
        # 记录推理过程
        self.reasoning_history.append({
            "timestamp": datetime.now().isoformat(),
            "input": kwargs.get("input", ""),
            "response": response,
            "intermediate_steps": len(intermediate_steps)
        })
        
        # 检查是否需要最终答案
        if "Final Answer:" in response:
            return AgentFinish(
                return_values={"output": response.split("Final Answer:")[-1].strip()},
                log=response
            )
        
        # 解析行动
        action_match = re.search(r"Action: (.*?)[\n]*Action Input: (.*)", response, re.DOTALL)
        
        if not action_match:
            # 如果无法解析行动，尝试自我修正
            correction_response = self._attempt_self_correction(response, kwargs)
            if correction_response:
                return correction_response
            
            raise ValueError(f"Could not parse LLM output: `{response}`")
        
        action = action_match.group(1).strip()
        action_input = action_match.group(2)
        
        # 验证行动的合理性
        if not self._validate_action(action, action_input, kwargs):
            return self._generate_alternative_action(kwargs)
        
        return AgentAction(
            tool=action,
            tool_input=action_input.strip(" ").strip('"'),
            log=response
        )
    
    def _attempt_self_correction(self, response: str, kwargs: Dict) -> Optional[AgentAction]:
        """尝试自我修正错误的响应"""
        correction_prompt = f"""
        The previous response was not in the correct format:
        {response}
        
        Please provide a corrected response in the format:
        Action: [tool_name]
        Action Input: [input_for_tool]
        
        Available tools: {', '.join(self.tool_names)}
        Original question: {kwargs.get('input', '')}
        """
        
        try:
            corrected_response = self.llm_chain.llm(correction_prompt)
            action_match = re.search(r"Action: (.*?)[\n]*Action Input: (.*)", corrected_response, re.DOTALL)
            
            if action_match:
                action = action_match.group(1).strip()
                action_input = action_match.group(2)
                
                return AgentAction(
                    tool=action,
                    tool_input=action_input.strip(" ").strip('"'),
                    log=corrected_response
                )
        except Exception:
            pass
        
        return None
    
    def _validate_action(self, action: str, action_input: str, kwargs: Dict) -> bool:
        """验证行动的合理性"""
        # 检查工具是否存在
        if action not in self.tool_names:
            return False
        
        # 检查输入是否为空
        if not action_input.strip():
            return False
        
        # 可以添加更多验证逻辑
        return True
    
    def _generate_alternative_action(self, kwargs: Dict) -> AgentAction:
        """生成替代行动"""
        # 选择一个默认工具
        default_tool = self.tools[0].name if self.tools else "search"
        default_input = kwargs.get("input", "help")
        
        return AgentAction(
            tool=default_tool,
            tool_input=default_input,
            log=f"Generated alternative action: {default_tool}"
        )
    
    def get_reasoning_history(self) -> List[Dict]:
        """获取推理历史"""
        return self.reasoning_history

# ============================================================================
# 2. 复杂链编排示例
# ============================================================================

class DataAnalysisChain:
    """数据分析链 - 多步骤数据分析工作流"""
    
    def __init__(self, llm):
        self.llm = llm
        self.setup_chains()
    
    def setup_chains(self):
        """设置分析链"""
        # 数据理解链
        self.data_understanding_chain = LLMChain(
            llm=self.llm,
            prompt=self._create_data_understanding_prompt()
        )
        
        # 假设生成链
        self.hypothesis_generation_chain = LLMChain(
            llm=self.llm,
            prompt=self._create_hypothesis_prompt()
        )
        
        # 分析方法选择链
        self.method_selection_chain = LLMChain(
            llm=self.llm,
            prompt=self._create_method_selection_prompt()
        )
        
        # 结果解释链
        self.interpretation_chain = LLMChain(
            llm=self.llm,
            prompt=self._create_interpretation_prompt()
        )
        
        # 建议生成链
        self.recommendation_chain = LLMChain(
            llm=self.llm,
            prompt=self._create_recommendation_prompt()
        )
    
    def _create_data_understanding_prompt(self):
        """创建数据理解提示"""
        from langchain.prompts import PromptTemplate
        
        return PromptTemplate(
            template="""
            作为数据分析专家，请分析以下数据描述：
            
            数据描述：{data_description}
            
            请提供以下分析：
            1. 数据类型和结构
            2. 潜在的数据质量问题
            3. 关键变量识别
            4. 数据的业务含义
            
            分析结果：
            """,
            input_variables=["data_description"]
        )
    
    def _create_hypothesis_prompt(self):
        """创建假设生成提示"""
        from langchain.prompts import PromptTemplate
        
        return PromptTemplate(
            template="""
            基于数据理解：{data_understanding}
            和业务目标：{business_objective}
            
            请生成3-5个可验证的假设：
            1. 每个假设应该明确、可测试
            2. 假设应该与业务目标相关
            3. 考虑数据的限制和可能性
            
            假设列表：
            """,
            input_variables=["data_understanding", "business_objective"]
        )
    
    def _create_method_selection_prompt(self):
        """创建方法选择提示"""
        from langchain.prompts import PromptTemplate
        
        return PromptTemplate(
            template="""
            基于以下信息选择最适合的分析方法：
            
            数据理解：{data_understanding}
            假设：{hypotheses}
            业务目标：{business_objective}
            
            请推荐：
            1. 主要分析方法（统计方法、机器学习等）
            2. 具体的技术和工具
            3. 分析步骤和顺序
            4. 预期的输出和指标
            
            方法推荐：
            """,
            input_variables=["data_understanding", "hypotheses", "business_objective"]
        )
    
    def _create_interpretation_prompt(self):
        """创建结果解释提示"""
        from langchain.prompts import PromptTemplate
        
        return PromptTemplate(
            template="""
            请解释以下分析结果：
            
            分析方法：{analysis_method}
            分析结果：{analysis_results}
            原始假设：{hypotheses}
            
            请提供：
            1. 结果的统计意义
            2. 业务含义和影响
            3. 假设验证情况
            4. 结果的可信度和局限性
            
            结果解释：
            """,
            input_variables=["analysis_method", "analysis_results", "hypotheses"]
        )
    
    def _create_recommendation_prompt(self):
        """创建建议生成提示"""
        from langchain.prompts import PromptTemplate
        
        return PromptTemplate(
            template="""
            基于完整的分析过程，请提供行动建议：
            
            数据理解：{data_understanding}
            分析结果：{interpretation}
            业务目标：{business_objective}
            
            请提供：
            1. 具体的行动建议
            2. 实施优先级
            3. 预期效果和风险
            4. 后续监控指标
            
            行动建议：
            """,
            input_variables=["data_understanding", "interpretation", "business_objective"]
        )
    
    async def analyze(self, 
                     data_description: str, 
                     business_objective: str,
                     analysis_results: str = None) -> Dict[str, str]:
        """执行完整的数据分析流程"""
        results = {}
        
        # 步骤1：数据理解
        data_understanding = await asyncio.to_thread(
            self.data_understanding_chain.run,
            data_description=data_description
        )
        results["data_understanding"] = data_understanding
        
        # 步骤2：假设生成
        hypotheses = await asyncio.to_thread(
            self.hypothesis_generation_chain.run,
            data_understanding=data_understanding,
            business_objective=business_objective
        )
        results["hypotheses"] = hypotheses
        
        # 步骤3：方法选择
        analysis_method = await asyncio.to_thread(
            self.method_selection_chain.run,
            data_understanding=data_understanding,
            hypotheses=hypotheses,
            business_objective=business_objective
        )
        results["analysis_method"] = analysis_method
        
        # 步骤4：结果解释（如果提供了分析结果）
        if analysis_results:
            interpretation = await asyncio.to_thread(
                self.interpretation_chain.run,
                analysis_method=analysis_method,
                analysis_results=analysis_results,
                hypotheses=hypotheses
            )
            results["interpretation"] = interpretation
            
            # 步骤5：建议生成
            recommendations = await asyncio.to_thread(
                self.recommendation_chain.run,
                data_understanding=data_understanding,
                interpretation=interpretation,
                business_objective=business_objective
            )
            results["recommendations"] = recommendations
        
        return results

# ============================================================================
# 3. 高级记忆管理示例
# ============================================================================

class HierarchicalMemory:
    """分层记忆系统"""
    
    def __init__(self, llm, max_short_term: int = 10, max_long_term: int = 50):
        self.llm = llm
        self.max_short_term = max_short_term
        self.max_long_term = max_long_term
        
        # 短期记忆：最近的对话
        self.short_term_memory = ConversationBufferMemory(
            memory_key="short_term_history",
            return_messages=True
        )
        
        # 长期记忆：重要信息的摘要
        self.long_term_memory = ConversationSummaryMemory(
            llm=llm,
            memory_key="long_term_summary",
            return_messages=False
        )
        
        # 重要事实存储
        self.fact_memory = {}
        
        # 用户偏好存储
        self.preference_memory = {}
    
    def add_conversation(self, human_input: str, ai_output: str):
        """添加对话到记忆"""
        # 添加到短期记忆
        self.short_term_memory.save_context(
            {"input": human_input},
            {"output": ai_output}
        )
        
        # 检查是否需要转移到长期记忆
        if len(self.short_term_memory.chat_memory.messages) > self.max_short_term:
            self._transfer_to_long_term()
        
        # 提取重要事实
        self._extract_facts(human_input, ai_output)
        
        # 更新用户偏好
        self._update_preferences(human_input, ai_output)
    
    def _transfer_to_long_term(self):
        """将短期记忆转移到长期记忆"""
        # 获取最早的几条消息
        messages_to_transfer = self.short_term_memory.chat_memory.messages[:5]
        
        # 生成摘要并添加到长期记忆
        for i in range(0, len(messages_to_transfer), 2):
            if i + 1 < len(messages_to_transfer):
                human_msg = messages_to_transfer[i]
                ai_msg = messages_to_transfer[i + 1]
                
                self.long_term_memory.save_context(
                    {"input": human_msg.content},
                    {"output": ai_msg.content}
                )
        
        # 从短期记忆中移除已转移的消息
        self.short_term_memory.chat_memory.messages = \
            self.short_term_memory.chat_memory.messages[5:]
    
    def _extract_facts(self, human_input: str, ai_output: str):
        """提取重要事实"""
        # 简化的事实提取逻辑
        fact_keywords = ["我叫", "我是", "我的", "记住", "重要"]
        
        for keyword in fact_keywords:
            if keyword in human_input:
                # 提取包含关键词的句子作为事实
                sentences = human_input.split('。')
                for sentence in sentences:
                    if keyword in sentence:
                        fact_key = f"fact_{len(self.fact_memory)}"
                        self.fact_memory[fact_key] = {
                            "content": sentence.strip(),
                            "timestamp": datetime.now().isoformat(),
                            "importance": 1.0
                        }
    
    def _update_preferences(self, human_input: str, ai_output: str):
        """更新用户偏好"""
        # 简化的偏好学习逻辑
        preference_indicators = {
            "喜欢": 1,
            "不喜欢": -1,
            "偏好": 1,
            "讨厌": -1
        }
        
        for indicator, weight in preference_indicators.items():
            if indicator in human_input:
                # 提取偏好内容
                parts = human_input.split(indicator)
                if len(parts) > 1:
                    preference_content = parts[1].split('，')[0].split('。')[0].strip()
                    
                    if preference_content in self.preference_memory:
                        self.preference_memory[preference_content] += weight
                    else:
                        self.preference_memory[preference_content] = weight
    
    def get_relevant_memory(self, current_input: str) -> Dict[str, str]:
        """获取相关记忆"""
        memory_context = {}
        
        # 获取短期记忆
        short_term = self.short_term_memory.load_memory_variables({})
        memory_context.update(short_term)
        
        # 获取长期记忆摘要
        long_term = self.long_term_memory.load_memory_variables({})
        memory_context.update(long_term)
        
        # 获取相关事实
        relevant_facts = self._get_relevant_facts(current_input)
        if relevant_facts:
            memory_context["relevant_facts"] = "\n".join(relevant_facts)
        
        # 获取相关偏好
        relevant_preferences = self._get_relevant_preferences(current_input)
        if relevant_preferences:
            memory_context["user_preferences"] = relevant_preferences
        
        return memory_context
    
    def _get_relevant_facts(self, current_input: str) -> List[str]:
        """获取相关事实"""
        relevant_facts = []
        
        for fact_key, fact_data in self.fact_memory.items():
            fact_content = fact_data["content"]
            # 简单的相关性检查
            if any(word in current_input for word in fact_content.split()):
                relevant_facts.append(fact_content)
        
        return relevant_facts[:3]  # 最多返回3个相关事实
    
    def _get_relevant_preferences(self, current_input: str) -> str:
        """获取相关偏好"""
        relevant_prefs = []
        
        for pref_content, weight in self.preference_memory.items():
            if any(word in current_input for word in pref_content.split()):
                sentiment = "喜欢" if weight > 0 else "不喜欢"
                relevant_prefs.append(f"{sentiment}: {pref_content}")
        
        return "; ".join(relevant_prefs[:3])  # 最多返回3个相关偏好

# ============================================================================
# 4. 回调和监控示例
# ============================================================================

class AdvancedCallbackHandler(BaseCallbackHandler):
    """高级回调处理器，用于监控和日志记录"""
    
    def __init__(self):
        self.logs = []
        self.metrics = {
            "total_tokens": 0,
            "total_cost": 0.0,
            "chain_runs": 0,
            "tool_calls": 0,
            "errors": 0
        }
    
    def on_llm_start(self, serialized: Dict[str, Any], prompts: List[str], **kwargs):
        """LLM开始时的回调"""
        self.logs.append({
            "event": "llm_start",
            "timestamp": datetime.now().isoformat(),
            "prompts": prompts,
            "model": serialized.get("name", "unknown")
        })
    
    def on_llm_end(self, response, **kwargs):
        """LLM结束时的回调"""
        # 计算token使用量
        if hasattr(response, 'llm_output') and response.llm_output:
            token_usage = response.llm_output.get('token_usage', {})
            total_tokens = token_usage.get('total_tokens', 0)
            self.metrics["total_tokens"] += total_tokens
            
            # 估算成本（基于OpenAI定价）
            cost = total_tokens * 0.002 / 1000  # 简化的成本计算
            self.metrics["total_cost"] += cost
        
        self.logs.append({
            "event": "llm_end",
            "timestamp": datetime.now().isoformat(),
            "response": str(response)
        })
    
    def on_chain_start(self, serialized: Dict[str, Any], inputs: Dict[str, Any], **kwargs):
        """链开始时的回调"""
        self.metrics["chain_runs"] += 1
        self.logs.append({
            "event": "chain_start",
            "timestamp": datetime.now().isoformat(),
            "chain": serialized.get("name", "unknown"),
            "inputs": inputs
        })
    
    def on_tool_start(self, serialized: Dict[str, Any], input_str: str, **kwargs):
        """工具开始时的回调"""
        self.metrics["tool_calls"] += 1
        self.logs.append({
            "event": "tool_start",
            "timestamp": datetime.now().isoformat(),
            "tool": serialized.get("name", "unknown"),
            "input": input_str
        })
    
    def on_llm_error(self, error: Exception, **kwargs):
        """LLM错误时的回调"""
        self.metrics["errors"] += 1
        self.logs.append({
            "event": "llm_error",
            "timestamp": datetime.now().isoformat(),
            "error": str(error)
        })
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取指标"""
        return self.metrics.copy()
    
    def get_logs(self, event_type: str = None) -> List[Dict]:
        """获取日志"""
        if event_type:
            return [log for log in self.logs if log["event"] == event_type]
        return self.logs.copy()

# ============================================================================
# 5. 使用示例
# ============================================================================

async def demo_advanced_langchain():
    """演示LangChain高级功能"""
    print("=== LangChain高级功能演示 ===")
    
    # 初始化LLM（需要配置API密钥）
    # llm = OpenAI(temperature=0.7)
    
    # 使用模拟LLM进行演示
    class MockLLM:
        def __call__(self, prompt):
            return "这是模拟的LLM响应"
        
        def __call__(self, prompt):
            if "Action:" in prompt:
                return "Action: search\nAction Input: artificial intelligence"
            elif "数据分析" in prompt:
                return "基于数据分析的专业回答"
            else:
                return "这是模拟的LLM响应"
    
    llm = MockLLM()
    
    # 1. 演示数据分析链
    print("\n1. 数据分析链演示")
    analysis_chain = DataAnalysisChain(llm)
    
    try:
        results = await analysis_chain.analyze(
            data_description="客户购买行为数据，包含100万条记录",
            business_objective="提高客户留存率",
            analysis_results="发现了3个主要客户群体"
        )
        
        print("分析结果:")
        for step, result in results.items():
            print(f"- {step}: {result[:100]}...")
    
    except Exception as e:
        print(f"分析链演示失败: {e}")
    
    # 2. 演示分层记忆
    print("\n2. 分层记忆演示")
    memory_system = HierarchicalMemory(llm)
    
    # 模拟对话
    conversations = [
        ("我叫张三，是一名软件工程师", "你好张三！很高兴认识你。"),
        ("我喜欢Python编程", "Python是很棒的编程语言！"),
        ("请记住我的生日是1月1日", "好的，我会记住你的生日是1月1日。")
    ]
    
    for human_input, ai_output in conversations:
        memory_system.add_conversation(human_input, ai_output)
    
    # 获取相关记忆
    relevant_memory = memory_system.get_relevant_memory("我的信息")
    print("相关记忆:")
    for key, value in relevant_memory.items():
        print(f"- {key}: {str(value)[:100]}...")
    
    # 3. 演示回调处理
    print("\n3. 回调处理演示")
    callback_handler = AdvancedCallbackHandler()
    
    # 模拟一些事件
    callback_handler.on_chain_start({"name": "test_chain"}, {"input": "test"})
    callback_handler.on_llm_start({"name": "openai"}, ["test prompt"])
    callback_handler.on_tool_start({"name": "search"}, "test query")
    
    metrics = callback_handler.get_metrics()
    print("系统指标:")
    for metric, value in metrics.items():
        print(f"- {metric}: {value}")
    
    print("\n演示完成！")

if __name__ == "__main__":
    asyncio.run(demo_advanced_langchain())
