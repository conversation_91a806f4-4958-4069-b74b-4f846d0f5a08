# 错误处理和性能优化

## 概述

在生产环境中，AI模型接口的稳定性和性能直接影响用户体验和业务成功。本章将深入探讨错误处理策略、性能优化技术、监控体系建设等关键技术，帮助构建高可用、高性能的AI服务系统。

## 错误处理策略

### 错误分类体系

```python
# error_management.py
from enum import Enum
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
import logging
import time
import asyncio
from datetime import datetime, timedelta
import json

class ErrorType(Enum):
    """错误类型"""
    NETWORK_ERROR = "network_error"
    AUTHENTICATION_ERROR = "authentication_error"
    RATE_LIMIT_ERROR = "rate_limit_error"
    QUOTA_EXCEEDED = "quota_exceeded"
    MODEL_ERROR = "model_error"
    VALIDATION_ERROR = "validation_error"
    TIMEOUT_ERROR = "timeout_error"
    SERVER_ERROR = "server_error"
    UNKNOWN_ERROR = "unknown_error"

class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class ErrorContext:
    """错误上下文"""
    error_type: ErrorType
    severity: ErrorSeverity
    message: str
    details: Dict[str, Any]
    timestamp: str
    request_id: Optional[str] = None
    user_id: Optional[str] = None
    model_id: Optional[str] = None
    retry_count: int = 0
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.now().isoformat()

class ErrorHandler:
    """统一错误处理器"""
    
    def __init__(self):
        self.error_handlers: Dict[ErrorType, Callable] = {}
        self.error_stats: Dict[ErrorType, int] = {}
        self.error_history: List[ErrorContext] = []
        self.logger = logging.getLogger(__name__)
        
        # 注册默认处理器
        self._register_default_handlers()
    
    def _register_default_handlers(self):
        """注册默认错误处理器"""
        self.error_handlers[ErrorType.NETWORK_ERROR] = self._handle_network_error
        self.error_handlers[ErrorType.AUTHENTICATION_ERROR] = self._handle_auth_error
        self.error_handlers[ErrorType.RATE_LIMIT_ERROR] = self._handle_rate_limit_error
        self.error_handlers[ErrorType.QUOTA_EXCEEDED] = self._handle_quota_error
        self.error_handlers[ErrorType.MODEL_ERROR] = self._handle_model_error
        self.error_handlers[ErrorType.VALIDATION_ERROR] = self._handle_validation_error
        self.error_handlers[ErrorType.TIMEOUT_ERROR] = self._handle_timeout_error
        self.error_handlers[ErrorType.SERVER_ERROR] = self._handle_server_error
    
    def register_handler(self, error_type: ErrorType, handler: Callable):
        """注册自定义错误处理器"""
        self.error_handlers[error_type] = handler
    
    async def handle_error(self, error_context: ErrorContext) -> Dict[str, Any]:
        """处理错误"""
        # 记录错误
        self._record_error(error_context)
        
        # 获取处理器
        handler = self.error_handlers.get(
            error_context.error_type, 
            self._handle_unknown_error
        )
        
        # 执行处理
        try:
            result = await handler(error_context)
            return result
        except Exception as e:
            self.logger.error(f"错误处理器执行失败: {e}")
            return self._create_error_response(
                "错误处理失败",
                {"original_error": error_context.message, "handler_error": str(e)}
            )
    
    def _record_error(self, error_context: ErrorContext):
        """记录错误"""
        # 更新统计
        self.error_stats[error_context.error_type] = \
            self.error_stats.get(error_context.error_type, 0) + 1
        
        # 添加到历史记录
        self.error_history.append(error_context)
        
        # 限制历史记录数量
        if len(self.error_history) > 1000:
            self.error_history = self.error_history[-1000:]
        
        # 记录日志
        log_level = {
            ErrorSeverity.LOW: logging.INFO,
            ErrorSeverity.MEDIUM: logging.WARNING,
            ErrorSeverity.HIGH: logging.ERROR,
            ErrorSeverity.CRITICAL: logging.CRITICAL
        }.get(error_context.severity, logging.ERROR)
        
        self.logger.log(
            log_level,
            f"错误发生 [{error_context.error_type.value}]: {error_context.message}",
            extra={
                "error_context": error_context.__dict__
            }
        )
    
    async def _handle_network_error(self, context: ErrorContext) -> Dict[str, Any]:
        """处理网络错误"""
        return self._create_error_response(
            "网络连接失败，请检查网络连接后重试",
            {
                "error_type": "network",
                "retry_suggested": True,
                "retry_delay": 5
            }
        )
    
    async def _handle_auth_error(self, context: ErrorContext) -> Dict[str, Any]:
        """处理认证错误"""
        return self._create_error_response(
            "认证失败，请检查API密钥配置",
            {
                "error_type": "authentication",
                "retry_suggested": False,
                "action_required": "check_api_key"
            }
        )
    
    async def _handle_rate_limit_error(self, context: ErrorContext) -> Dict[str, Any]:
        """处理速率限制错误"""
        # 从错误详情中提取重试时间
        retry_after = context.details.get("retry_after", 60)
        
        return self._create_error_response(
            f"请求频率过高，请在{retry_after}秒后重试",
            {
                "error_type": "rate_limit",
                "retry_suggested": True,
                "retry_delay": retry_after,
                "suggestion": "考虑降低请求频率或升级API计划"
            }
        )
    
    async def _handle_quota_error(self, context: ErrorContext) -> Dict[str, Any]:
        """处理配额错误"""
        return self._create_error_response(
            "API配额已用完，请检查账户余额或升级服务计划",
            {
                "error_type": "quota",
                "retry_suggested": False,
                "action_required": "check_quota"
            }
        )
    
    async def _handle_model_error(self, context: ErrorContext) -> Dict[str, Any]:
        """处理模型错误"""
        return self._create_error_response(
            "模型处理出现问题，请稍后重试",
            {
                "error_type": "model",
                "retry_suggested": True,
                "retry_delay": 10,
                "model_id": context.model_id
            }
        )
    
    async def _handle_validation_error(self, context: ErrorContext) -> Dict[str, Any]:
        """处理验证错误"""
        return self._create_error_response(
            "请求参数验证失败，请检查输入参数",
            {
                "error_type": "validation",
                "retry_suggested": False,
                "validation_details": context.details
            }
        )
    
    async def _handle_timeout_error(self, context: ErrorContext) -> Dict[str, Any]:
        """处理超时错误"""
        return self._create_error_response(
            "请求超时，请稍后重试",
            {
                "error_type": "timeout",
                "retry_suggested": True,
                "retry_delay": 5,
                "suggestion": "考虑减少请求复杂度或增加超时时间"
            }
        )
    
    async def _handle_server_error(self, context: ErrorContext) -> Dict[str, Any]:
        """处理服务器错误"""
        return self._create_error_response(
            "服务器内部错误，请稍后重试",
            {
                "error_type": "server",
                "retry_suggested": True,
                "retry_delay": 30
            }
        )
    
    async def _handle_unknown_error(self, context: ErrorContext) -> Dict[str, Any]:
        """处理未知错误"""
        return self._create_error_response(
            "发生未知错误，请联系技术支持",
            {
                "error_type": "unknown",
                "retry_suggested": False,
                "original_message": context.message
            }
        )
    
    def _create_error_response(self, message: str, details: Dict[str, Any]) -> Dict[str, Any]:
        """创建错误响应"""
        return {
            "success": False,
            "error": {
                "message": message,
                "timestamp": datetime.now().isoformat(),
                **details
            }
        }
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计"""
        total_errors = sum(self.error_stats.values())
        
        if total_errors == 0:
            return {"message": "暂无错误记录"}
        
        # 计算错误分布
        error_distribution = {
            error_type.value: count / total_errors
            for error_type, count in self.error_stats.items()
        }
        
        # 最近24小时错误趋势
        recent_errors = [
            error for error in self.error_history
            if datetime.now() - datetime.fromisoformat(error.timestamp) < timedelta(hours=24)
        ]
        
        return {
            "total_errors": total_errors,
            "error_distribution": error_distribution,
            "recent_24h_errors": len(recent_errors),
            "most_common_error": max(self.error_stats.items(), key=lambda x: x[1])[0].value if self.error_stats else None
        }

class CircuitBreaker:
    """熔断器"""
    
    def __init__(self, 
                 failure_threshold: int = 5,
                 recovery_timeout: int = 60,
                 expected_exception: type = Exception):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
    
    async def call(self, func, *args, **kwargs):
        """执行函数调用"""
        if self.state == "OPEN":
            if self._should_attempt_reset():
                self.state = "HALF_OPEN"
            else:
                raise Exception("熔断器开启，服务不可用")
        
        try:
            result = await func(*args, **kwargs)
            self._on_success()
            return result
        except self.expected_exception as e:
            self._on_failure()
            raise e
    
    def _should_attempt_reset(self) -> bool:
        """是否应该尝试重置"""
        return (
            self.last_failure_time and
            time.time() - self.last_failure_time >= self.recovery_timeout
        )
    
    def _on_success(self):
        """成功时的处理"""
        self.failure_count = 0
        self.state = "CLOSED"
    
    def _on_failure(self):
        """失败时的处理"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "OPEN"

class RetryManager:
    """重试管理器"""
    
    def __init__(self):
        self.retry_strategies = {
            ErrorType.NETWORK_ERROR: {"max_retries": 3, "backoff": "exponential"},
            ErrorType.RATE_LIMIT_ERROR: {"max_retries": 2, "backoff": "fixed"},
            ErrorType.TIMEOUT_ERROR: {"max_retries": 2, "backoff": "linear"},
            ErrorType.SERVER_ERROR: {"max_retries": 3, "backoff": "exponential"},
            ErrorType.MODEL_ERROR: {"max_retries": 1, "backoff": "fixed"}
        }
    
    async def execute_with_retry(self, 
                               func: Callable,
                               error_type: ErrorType,
                               *args, **kwargs) -> Any:
        """带重试的执行"""
        strategy = self.retry_strategies.get(error_type, {"max_retries": 0, "backoff": "fixed"})
        max_retries = strategy["max_retries"]
        backoff_type = strategy["backoff"]
        
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                last_exception = e
                
                if attempt == max_retries:
                    break
                
                # 计算等待时间
                wait_time = self._calculate_wait_time(attempt, backoff_type)
                await asyncio.sleep(wait_time)
        
        raise last_exception
    
    def _calculate_wait_time(self, attempt: int, backoff_type: str) -> float:
        """计算等待时间"""
        if backoff_type == "exponential":
            return min(2 ** attempt, 60)  # 最大60秒
        elif backoff_type == "linear":
            return (attempt + 1) * 2
        else:  # fixed
            return 5

# 性能优化组件
class PerformanceOptimizer:
    """性能优化器"""
    
    def __init__(self):
        self.cache_manager = CacheManager()
        self.connection_pool = ConnectionPoolManager()
        self.request_batcher = RequestBatcher()
        self.performance_monitor = PerformanceMonitor()
    
    async def optimize_request(self, request_func: Callable, *args, **kwargs) -> Any:
        """优化请求执行"""
        # 检查缓存
        cache_key = self._generate_cache_key(request_func, args, kwargs)
        cached_result = await self.cache_manager.get(cache_key)
        
        if cached_result:
            self.performance_monitor.record_cache_hit()
            return cached_result
        
        # 执行请求
        start_time = time.time()
        
        try:
            result = await request_func(*args, **kwargs)
            
            # 缓存结果
            await self.cache_manager.set(cache_key, result)
            
            # 记录性能指标
            execution_time = time.time() - start_time
            self.performance_monitor.record_request(execution_time, True)
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.performance_monitor.record_request(execution_time, False)
            raise e
    
    def _generate_cache_key(self, func: Callable, args: tuple, kwargs: dict) -> str:
        """生成缓存键"""
        import hashlib
        
        key_data = f"{func.__name__}:{str(args)}:{str(sorted(kwargs.items()))}"
        return hashlib.md5(key_data.encode()).hexdigest()

class CacheManager:
    """缓存管理器"""
    
    def __init__(self, default_ttl: int = 300):
        self.cache: Dict[str, tuple] = {}  # key -> (value, expire_time)
        self.default_ttl = default_ttl
        self.hit_count = 0
        self.miss_count = 0
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存"""
        if key in self.cache:
            value, expire_time = self.cache[key]
            
            if time.time() < expire_time:
                self.hit_count += 1
                return value
            else:
                del self.cache[key]
        
        self.miss_count += 1
        return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存"""
        ttl = ttl or self.default_ttl
        expire_time = time.time() + ttl
        self.cache[key] = (value, expire_time)
        
        # 清理过期缓存
        await self._cleanup_expired()
    
    async def _cleanup_expired(self):
        """清理过期缓存"""
        current_time = time.time()
        expired_keys = [
            key for key, (_, expire_time) in self.cache.items()
            if current_time >= expire_time
        ]
        
        for key in expired_keys:
            del self.cache[key]
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        total_requests = self.hit_count + self.miss_count
        hit_rate = self.hit_count / total_requests if total_requests > 0 else 0
        
        return {
            "hit_count": self.hit_count,
            "miss_count": self.miss_count,
            "hit_rate": hit_rate,
            "cache_size": len(self.cache)
        }

class ConnectionPoolManager:
    """连接池管理器"""
    
    def __init__(self, max_connections: int = 100):
        self.max_connections = max_connections
        self.active_connections = 0
        self.connection_queue = asyncio.Queue(maxsize=max_connections)
        
        # 预创建连接
        asyncio.create_task(self._initialize_pool())
    
    async def _initialize_pool(self):
        """初始化连接池"""
        for _ in range(self.max_connections):
            connection = await self._create_connection()
            await self.connection_queue.put(connection)
    
    async def _create_connection(self):
        """创建连接"""
        # 这里应该创建实际的连接对象
        return {"id": f"conn_{self.active_connections}", "created_at": time.time()}
    
    async def get_connection(self):
        """获取连接"""
        try:
            connection = await asyncio.wait_for(
                self.connection_queue.get(), 
                timeout=5.0
            )
            self.active_connections += 1
            return connection
        except asyncio.TimeoutError:
            raise Exception("获取连接超时")
    
    async def return_connection(self, connection):
        """归还连接"""
        await self.connection_queue.put(connection)
        self.active_connections -= 1

class RequestBatcher:
    """请求批处理器"""
    
    def __init__(self, batch_size: int = 10, batch_timeout: float = 1.0):
        self.batch_size = batch_size
        self.batch_timeout = batch_timeout
        self.pending_requests = []
        self.batch_timer = None
    
    async def add_request(self, request_data: Dict[str, Any]) -> Any:
        """添加请求到批处理"""
        future = asyncio.Future()
        
        self.pending_requests.append({
            "data": request_data,
            "future": future
        })
        
        # 检查是否需要立即处理
        if len(self.pending_requests) >= self.batch_size:
            await self._process_batch()
        elif self.batch_timer is None:
            self.batch_timer = asyncio.create_task(self._wait_and_process())
        
        return await future
    
    async def _wait_and_process(self):
        """等待并处理批次"""
        await asyncio.sleep(self.batch_timeout)
        await self._process_batch()
    
    async def _process_batch(self):
        """处理批次"""
        if not self.pending_requests:
            return
        
        batch = self.pending_requests.copy()
        self.pending_requests.clear()
        
        if self.batch_timer:
            self.batch_timer.cancel()
            self.batch_timer = None
        
        try:
            # 批量处理请求
            results = await self._execute_batch([req["data"] for req in batch])
            
            # 返回结果
            for request, result in zip(batch, results):
                request["future"].set_result(result)
                
        except Exception as e:
            # 处理错误
            for request in batch:
                request["future"].set_exception(e)
    
    async def _execute_batch(self, requests: List[Dict[str, Any]]) -> List[Any]:
        """执行批量请求"""
        # 这里应该实现实际的批量处理逻辑
        return [f"批量处理结果_{i}" for i in range(len(requests))]

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.request_times = []
        self.success_count = 0
        self.failure_count = 0
        self.cache_hits = 0
        self.start_time = time.time()
    
    def record_request(self, execution_time: float, success: bool):
        """记录请求"""
        self.request_times.append(execution_time)
        
        if success:
            self.success_count += 1
        else:
            self.failure_count += 1
        
        # 保持最近1000个请求的记录
        if len(self.request_times) > 1000:
            self.request_times = self.request_times[-1000:]
    
    def record_cache_hit(self):
        """记录缓存命中"""
        self.cache_hits += 1
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        if not self.request_times:
            return {"message": "暂无性能数据"}
        
        total_requests = self.success_count + self.failure_count
        avg_response_time = sum(self.request_times) / len(self.request_times)
        
        # 计算百分位数
        sorted_times = sorted(self.request_times)
        p50 = sorted_times[len(sorted_times) // 2]
        p95 = sorted_times[int(len(sorted_times) * 0.95)]
        p99 = sorted_times[int(len(sorted_times) * 0.99)]
        
        uptime = time.time() - self.start_time
        
        return {
            "total_requests": total_requests,
            "success_rate": self.success_count / total_requests if total_requests > 0 else 0,
            "avg_response_time": avg_response_time,
            "response_time_percentiles": {
                "p50": p50,
                "p95": p95,
                "p99": p99
            },
            "cache_hits": self.cache_hits,
            "uptime_seconds": uptime,
            "requests_per_second": total_requests / uptime if uptime > 0 else 0
        }

# 使用示例
async def demo_error_handling_and_optimization():
    """错误处理和性能优化演示"""
    print("=== 错误处理和性能优化演示 ===")
    
    # 创建错误处理器
    error_handler = ErrorHandler()
    
    # 模拟不同类型的错误
    error_contexts = [
        ErrorContext(
            error_type=ErrorType.NETWORK_ERROR,
            severity=ErrorSeverity.MEDIUM,
            message="网络连接超时",
            details={"timeout": 30}
        ),
        ErrorContext(
            error_type=ErrorType.RATE_LIMIT_ERROR,
            severity=ErrorSeverity.HIGH,
            message="请求频率过高",
            details={"retry_after": 60}
        ),
        ErrorContext(
            error_type=ErrorType.QUOTA_EXCEEDED,
            severity=ErrorSeverity.CRITICAL,
            message="API配额已用完",
            details={"quota_limit": 1000}
        )
    ]
    
    # 处理错误
    for context in error_contexts:
        print(f"\n处理错误: {context.error_type.value}")
        result = await error_handler.handle_error(context)
        print(f"处理结果: {result}")
    
    # 获取错误统计
    stats = error_handler.get_error_statistics()
    print(f"\n错误统计: {stats}")
    
    # 演示性能优化
    optimizer = PerformanceOptimizer()
    
    # 模拟优化请求
    async def mock_request(data):
        await asyncio.sleep(0.1)  # 模拟网络延迟
        return f"处理结果: {data}"
    
    print(f"\n=== 性能优化演示 ===")
    
    # 执行优化请求
    for i in range(5):
        result = await optimizer.optimize_request(mock_request, f"请求{i}")
        print(f"请求{i}结果: {result}")
    
    # 获取性能指标
    metrics = optimizer.performance_monitor.get_performance_metrics()
    print(f"\n性能指标: {metrics}")
    
    # 获取缓存统计
    cache_stats = optimizer.cache_manager.get_cache_stats()
    print(f"缓存统计: {cache_stats}")

if __name__ == "__main__":
    asyncio.run(demo_error_handling_and_optimization())
```

这个文档提供了完整的错误处理和性能优化解决方案，包括分层错误处理、熔断器、重试机制、缓存管理、连接池、批处理等核心技术，为构建高可用、高性能的AI服务系统提供了全面的技术支撑。
