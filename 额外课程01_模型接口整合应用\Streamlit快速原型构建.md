# Streamlit快速原型构建

## 概述

Streamlit是一个强大的Python库，专门用于快速构建数据科学和机器学习应用的Web界面。在AI模型接口整合项目中，Streamlit可以帮助我们快速构建原型，展示功能，进行用户测试。本章将详细介绍如何使用Streamlit构建AI应用原型。

## Streamlit基础

### 安装和配置

```bash
# 安装Streamlit
pip install streamlit

# 安装相关依赖
pip install pandas plotly requests asyncio aiohttp

# 运行Streamlit应用
streamlit run app.py
```

### 基本组件介绍

```python
# streamlit_basics.py
import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
from datetime import datetime
import time

def streamlit_basics_demo():
    """Streamlit基础组件演示"""
    
    # 页面配置
    st.set_page_config(
        page_title="AI模型接口演示",
        page_icon="🤖",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # 标题和描述
    st.title("🤖 AI模型接口整合演示")
    st.markdown("这是一个使用Streamlit构建的AI模型接口演示应用")
    
    # 侧边栏
    with st.sidebar:
        st.header("⚙️ 配置选项")
        
        # 选择框
        model_choice = st.selectbox(
            "选择AI模型",
            ["GPT-3.5", "GPT-4", "Claude-3", "Gemini"]
        )
        
        # 滑块
        temperature = st.slider(
            "温度参数",
            min_value=0.0,
            max_value=2.0,
            value=0.7,
            step=0.1
        )
        
        # 数字输入
        max_tokens = st.number_input(
            "最大Token数",
            min_value=1,
            max_value=4000,
            value=1000
        )
        
        # 复选框
        enable_streaming = st.checkbox("启用流式输出", value=True)
        
        # 文件上传
        uploaded_file = st.file_uploader(
            "上传配置文件",
            type=['json', 'yaml', 'txt']
        )
    
    # 主要内容区域
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.subheader("💬 对话界面")
        
        # 文本输入
        user_input = st.text_area(
            "输入您的问题",
            height=100,
            placeholder="请输入您想要询问的问题..."
        )
        
        # 按钮
        if st.button("发送", type="primary"):
            if user_input:
                with st.spinner("AI正在思考中..."):
                    # 模拟API调用
                    time.sleep(2)
                    response = f"使用{model_choice}模型回答：这是对'{user_input}'的回答"
                
                # 显示响应
                st.success("回答生成完成！")
                st.write("**AI回答：**")
                st.write(response)
            else:
                st.warning("请输入问题")
    
    with col2:
        st.subheader("📊 实时统计")
        
        # 指标显示
        st.metric(
            label="今日请求数",
            value="1,234",
            delta="123"
        )
        
        st.metric(
            label="平均响应时间",
            value="1.2s",
            delta="-0.3s"
        )
        
        st.metric(
            label="成功率",
            value="99.5%",
            delta="0.2%"
        )
    
    # 数据展示
    st.subheader("📈 使用趋势")
    
    # 生成示例数据
    dates = pd.date_range(start='2024-01-01', end='2024-01-31', freq='D')
    data = pd.DataFrame({
        'date': dates,
        'requests': np.random.randint(100, 1000, len(dates)),
        'success_rate': np.random.uniform(0.95, 1.0, len(dates))
    })
    
    # 绘制图表
    fig = px.line(data, x='date', y='requests', title='每日请求量趋势')
    st.plotly_chart(fig, use_container_width=True)
    
    # 数据表格
    st.subheader("📋 详细数据")
    st.dataframe(data, use_container_width=True)

if __name__ == "__main__":
    streamlit_basics_demo()
```

## AI聊天应用原型

### 多模型聊天界面

```python
# ai_chat_prototype.py
import streamlit as st
import asyncio
import json
from typing import Dict, List, Any
from datetime import datetime
import requests

class StreamlitChatApp:
    """Streamlit聊天应用"""
    
    def __init__(self):
        self.initialize_session_state()
        self.setup_page_config()
    
    def initialize_session_state(self):
        """初始化会话状态"""
        if 'messages' not in st.session_state:
            st.session_state.messages = []
        
        if 'conversation_id' not in st.session_state:
            st.session_state.conversation_id = None
        
        if 'model_stats' not in st.session_state:
            st.session_state.model_stats = {
                'total_requests': 0,
                'successful_requests': 0,
                'total_tokens': 0,
                'total_cost': 0.0
            }
    
    def setup_page_config(self):
        """设置页面配置"""
        st.set_page_config(
            page_title="AI多模型聊天",
            page_icon="💬",
            layout="wide",
            initial_sidebar_state="expanded"
        )
    
    def render_sidebar(self):
        """渲染侧边栏"""
        with st.sidebar:
            st.header("🔧 模型配置")
            
            # 模型选择
            model_provider = st.selectbox(
                "选择模型提供商",
                ["OpenAI", "Anthropic", "Google", "本地模型"]
            )
            
            if model_provider == "OpenAI":
                model_name = st.selectbox(
                    "选择模型",
                    ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo"]
                )
            elif model_provider == "Anthropic":
                model_name = st.selectbox(
                    "选择模型",
                    ["claude-3-haiku", "claude-3-sonnet", "claude-3-opus"]
                )
            elif model_provider == "Google":
                model_name = st.selectbox(
                    "选择模型",
                    ["gemini-pro", "gemini-pro-vision"]
                )
            else:
                model_name = st.selectbox(
                    "选择模型",
                    ["llama2", "mistral", "codellama"]
                )
            
            st.divider()
            
            # 参数配置
            st.subheader("⚙️ 参数设置")
            
            temperature = st.slider(
                "温度 (Temperature)",
                min_value=0.0,
                max_value=2.0,
                value=0.7,
                step=0.1,
                help="控制输出的随机性，值越高越随机"
            )
            
            max_tokens = st.number_input(
                "最大Token数",
                min_value=1,
                max_value=4000,
                value=1000,
                help="限制回答的最大长度"
            )
            
            enable_streaming = st.checkbox(
                "启用流式输出",
                value=True,
                help="实时显示AI回答过程"
            )
            
            st.divider()
            
            # 系统提示
            st.subheader("📝 系统提示")
            system_prompt = st.text_area(
                "系统提示词",
                value="你是一个有帮助的AI助手。",
                height=100,
                help="设置AI的角色和行为"
            )
            
            st.divider()
            
            # 统计信息
            st.subheader("📊 使用统计")
            stats = st.session_state.model_stats
            
            st.metric("总请求数", stats['total_requests'])
            st.metric("成功率", f"{stats['successful_requests']/max(stats['total_requests'], 1)*100:.1f}%")
            st.metric("总Token数", stats['total_tokens'])
            st.metric("预估费用", f"${stats['total_cost']:.4f}")
            
            # 清除按钮
            if st.button("🗑️ 清除对话", type="secondary"):
                st.session_state.messages = []
                st.session_state.conversation_id = None
                st.rerun()
            
            return {
                'model_provider': model_provider,
                'model_name': model_name,
                'temperature': temperature,
                'max_tokens': max_tokens,
                'enable_streaming': enable_streaming,
                'system_prompt': system_prompt
            }
    
    def render_chat_interface(self, config: Dict[str, Any]):
        """渲染聊天界面"""
        st.header("💬 AI聊天助手")
        
        # 显示对话历史
        chat_container = st.container()
        
        with chat_container:
            for message in st.session_state.messages:
                with st.chat_message(message["role"]):
                    st.write(message["content"])
                    
                    # 显示元数据
                    if "metadata" in message:
                        with st.expander("详细信息"):
                            st.json(message["metadata"])
        
        # 用户输入
        if prompt := st.chat_input("输入您的问题..."):
            # 添加用户消息
            st.session_state.messages.append({
                "role": "user",
                "content": prompt,
                "timestamp": datetime.now().isoformat()
            })
            
            # 显示用户消息
            with st.chat_message("user"):
                st.write(prompt)
            
            # 生成AI回答
            with st.chat_message("assistant"):
                response_placeholder = st.empty()
                
                if config['enable_streaming']:
                    # 流式输出
                    full_response = ""
                    
                    for chunk in self.stream_ai_response(prompt, config):
                        full_response += chunk
                        response_placeholder.write(full_response + "▌")
                    
                    response_placeholder.write(full_response)
                else:
                    # 非流式输出
                    with st.spinner("AI正在思考..."):
                        response = self.get_ai_response(prompt, config)
                        response_placeholder.write(response["content"])
                        full_response = response["content"]
                
                # 添加AI消息
                st.session_state.messages.append({
                    "role": "assistant",
                    "content": full_response,
                    "timestamp": datetime.now().isoformat(),
                    "metadata": {
                        "model": f"{config['model_provider']}/{config['model_name']}",
                        "temperature": config['temperature'],
                        "tokens": len(full_response.split()) * 1.3  # 估算
                    }
                })
                
                # 更新统计
                self.update_stats(full_response, config)
    
    def stream_ai_response(self, prompt: str, config: Dict[str, Any]):
        """流式AI响应（模拟）"""
        # 这里应该调用实际的AI API
        # 为了演示，我们模拟流式输出
        
        response_text = f"这是使用{config['model_provider']}的{config['model_name']}模型对'{prompt}'的回答。"
        
        import time
        for word in response_text.split():
            yield word + " "
            time.sleep(0.1)
    
    def get_ai_response(self, prompt: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """获取AI响应（模拟）"""
        # 这里应该调用实际的AI API
        # 为了演示，我们返回模拟响应
        
        return {
            "content": f"这是使用{config['model_provider']}的{config['model_name']}模型的回答：{prompt}",
            "model": config['model_name'],
            "usage": {
                "prompt_tokens": len(prompt.split()) * 1.3,
                "completion_tokens": 50,
                "total_tokens": len(prompt.split()) * 1.3 + 50
            }
        }
    
    def update_stats(self, response: str, config: Dict[str, Any]):
        """更新统计信息"""
        st.session_state.model_stats['total_requests'] += 1
        st.session_state.model_stats['successful_requests'] += 1
        st.session_state.model_stats['total_tokens'] += len(response.split()) * 1.3
        st.session_state.model_stats['total_cost'] += 0.002  # 模拟费用
    
    def render_analytics_tab(self):
        """渲染分析标签页"""
        st.header("📊 对话分析")
        
        if not st.session_state.messages:
            st.info("暂无对话数据")
            return
        
        # 对话统计
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("对话轮数", len(st.session_state.messages) // 2)
        
        with col2:
            total_words = sum(len(msg["content"].split()) for msg in st.session_state.messages)
            st.metric("总词数", total_words)
        
        with col3:
            avg_length = total_words / len(st.session_state.messages) if st.session_state.messages else 0
            st.metric("平均长度", f"{avg_length:.1f}")
        
        # 对话时间线
        st.subheader("⏰ 对话时间线")
        
        timeline_data = []
        for i, msg in enumerate(st.session_state.messages):
            timeline_data.append({
                "序号": i + 1,
                "角色": "用户" if msg["role"] == "user" else "AI",
                "内容预览": msg["content"][:50] + "..." if len(msg["content"]) > 50 else msg["content"],
                "时间": msg.get("timestamp", "未知"),
                "字数": len(msg["content"])
            })
        
        st.dataframe(timeline_data, use_container_width=True)
        
        # 导出功能
        st.subheader("💾 导出对话")
        
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("导出为JSON"):
                json_data = json.dumps(st.session_state.messages, ensure_ascii=False, indent=2)
                st.download_button(
                    label="下载JSON文件",
                    data=json_data,
                    file_name=f"conversation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                    mime="application/json"
                )
        
        with col2:
            if st.button("导出为Markdown"):
                md_content = self.export_to_markdown()
                st.download_button(
                    label="下载Markdown文件",
                    data=md_content,
                    file_name=f"conversation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md",
                    mime="text/markdown"
                )
    
    def export_to_markdown(self) -> str:
        """导出为Markdown格式"""
        md_lines = ["# AI对话记录", ""]
        md_lines.append(f"**导出时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        md_lines.append("")
        
        for i, msg in enumerate(st.session_state.messages, 1):
            role = "用户" if msg["role"] == "user" else "AI助手"
            md_lines.append(f"## {i}. {role}")
            md_lines.append("")
            md_lines.append(msg["content"])
            md_lines.append("")
            
            if "timestamp" in msg:
                md_lines.append(f"*时间: {msg['timestamp']}*")
                md_lines.append("")
        
        return "\n".join(md_lines)
    
    def run(self):
        """运行应用"""
        # 渲染侧边栏并获取配置
        config = self.render_sidebar()
        
        # 主要内容标签页
        tab1, tab2 = st.tabs(["💬 聊天", "📊 分析"])
        
        with tab1:
            self.render_chat_interface(config)
        
        with tab2:
            self.render_analytics_tab()

# 运行应用
if __name__ == "__main__":
    app = StreamlitChatApp()
    app.run()
```

## API监控仪表板

### 实时监控界面

```python
# api_monitoring_dashboard.py
import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import numpy as np
import time

class APIMonitoringDashboard:
    """API监控仪表板"""
    
    def __init__(self):
        self.setup_page()
        self.initialize_data()
    
    def setup_page(self):
        """设置页面"""
        st.set_page_config(
            page_title="API监控仪表板",
            page_icon="📊",
            layout="wide",
            initial_sidebar_state="collapsed"
        )
    
    def initialize_data(self):
        """初始化数据"""
        if 'monitoring_data' not in st.session_state:
            st.session_state.monitoring_data = self.generate_sample_data()
    
    def generate_sample_data(self):
        """生成示例数据"""
        # 生成过去24小时的数据
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=24)
        
        time_range = pd.date_range(start=start_time, end=end_time, freq='5min')
        
        data = []
        for timestamp in time_range:
            # 模拟不同API提供商的数据
            for provider in ['OpenAI', 'Anthropic', 'Google', 'Local']:
                data.append({
                    'timestamp': timestamp,
                    'provider': provider,
                    'requests': np.random.poisson(50),
                    'success_rate': np.random.uniform(0.95, 1.0),
                    'avg_response_time': np.random.uniform(0.5, 3.0),
                    'error_count': np.random.poisson(2),
                    'tokens_used': np.random.randint(1000, 10000),
                    'cost': np.random.uniform(0.01, 0.5)
                })
        
        return pd.DataFrame(data)
    
    def render_header(self):
        """渲染页面头部"""
        st.title("📊 AI API监控仪表板")
        st.markdown("实时监控AI模型API的性能和使用情况")
        
        # 刷新按钮
        col1, col2, col3 = st.columns([1, 1, 8])
        
        with col1:
            if st.button("🔄 刷新数据"):
                st.session_state.monitoring_data = self.generate_sample_data()
                st.rerun()
        
        with col2:
            auto_refresh = st.checkbox("自动刷新", value=False)
        
        if auto_refresh:
            time.sleep(5)
            st.rerun()
    
    def render_overview_metrics(self):
        """渲染概览指标"""
        st.subheader("📈 实时概览")
        
        # 计算总体指标
        latest_data = st.session_state.monitoring_data.groupby('provider').tail(1)
        
        total_requests = latest_data['requests'].sum()
        avg_success_rate = latest_data['success_rate'].mean()
        avg_response_time = latest_data['avg_response_time'].mean()
        total_errors = latest_data['error_count'].sum()
        total_cost = latest_data['cost'].sum()
        
        # 显示指标卡片
        col1, col2, col3, col4, col5 = st.columns(5)
        
        with col1:
            st.metric(
                "总请求数",
                f"{total_requests:,}",
                delta=f"+{np.random.randint(10, 100)}"
            )
        
        with col2:
            st.metric(
                "平均成功率",
                f"{avg_success_rate:.1%}",
                delta=f"+{np.random.uniform(0, 0.01):.2%}"
            )
        
        with col3:
            st.metric(
                "平均响应时间",
                f"{avg_response_time:.2f}s",
                delta=f"-{np.random.uniform(0, 0.1):.2f}s"
            )
        
        with col4:
            st.metric(
                "错误数",
                f"{total_errors}",
                delta=f"-{np.random.randint(1, 5)}"
            )
        
        with col5:
            st.metric(
                "总费用",
                f"${total_cost:.2f}",
                delta=f"+${np.random.uniform(0.01, 0.1):.2f}"
            )
    
    def render_provider_comparison(self):
        """渲染提供商对比"""
        st.subheader("🔄 提供商对比")
        
        # 按提供商聚合数据
        provider_stats = st.session_state.monitoring_data.groupby('provider').agg({
            'requests': 'sum',
            'success_rate': 'mean',
            'avg_response_time': 'mean',
            'error_count': 'sum',
            'cost': 'sum'
        }).reset_index()
        
        col1, col2 = st.columns(2)
        
        with col1:
            # 请求量对比
            fig_requests = px.bar(
                provider_stats,
                x='provider',
                y='requests',
                title='各提供商请求量对比',
                color='provider'
            )
            st.plotly_chart(fig_requests, use_container_width=True)
        
        with col2:
            # 成功率对比
            fig_success = px.bar(
                provider_stats,
                x='provider',
                y='success_rate',
                title='各提供商成功率对比',
                color='provider'
            )
            fig_success.update_yaxis(range=[0.9, 1.0])
            st.plotly_chart(fig_success, use_container_width=True)
    
    def render_time_series_charts(self):
        """渲染时间序列图表"""
        st.subheader("📊 趋势分析")
        
        # 选择时间范围
        col1, col2 = st.columns([1, 3])
        
        with col1:
            time_range = st.selectbox(
                "选择时间范围",
                ["最近1小时", "最近6小时", "最近24小时"]
            )
            
            hours_map = {"最近1小时": 1, "最近6小时": 6, "最近24小时": 24}
            hours = hours_map[time_range]
            
            # 过滤数据
            cutoff_time = datetime.now() - timedelta(hours=hours)
            filtered_data = st.session_state.monitoring_data[
                st.session_state.monitoring_data['timestamp'] >= cutoff_time
            ]
        
        # 响应时间趋势
        fig_response_time = px.line(
            filtered_data,
            x='timestamp',
            y='avg_response_time',
            color='provider',
            title='响应时间趋势'
        )
        st.plotly_chart(fig_response_time, use_container_width=True)
        
        # 请求量趋势
        fig_requests = px.line(
            filtered_data,
            x='timestamp',
            y='requests',
            color='provider',
            title='请求量趋势'
        )
        st.plotly_chart(fig_requests, use_container_width=True)
    
    def render_error_analysis(self):
        """渲染错误分析"""
        st.subheader("🚨 错误分析")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # 错误分布
            error_data = st.session_state.monitoring_data.groupby('provider')['error_count'].sum().reset_index()
            
            fig_errors = px.pie(
                error_data,
                values='error_count',
                names='provider',
                title='错误分布'
            )
            st.plotly_chart(fig_errors, use_container_width=True)
        
        with col2:
            # 错误趋势
            error_trend = st.session_state.monitoring_data.groupby(['timestamp', 'provider'])['error_count'].sum().reset_index()
            
            fig_error_trend = px.line(
                error_trend,
                x='timestamp',
                y='error_count',
                color='provider',
                title='错误趋势'
            )
            st.plotly_chart(fig_error_trend, use_container_width=True)
    
    def render_cost_analysis(self):
        """渲染成本分析"""
        st.subheader("💰 成本分析")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # 成本分布
            cost_data = st.session_state.monitoring_data.groupby('provider')['cost'].sum().reset_index()
            
            fig_cost = px.pie(
                cost_data,
                values='cost',
                names='provider',
                title='成本分布'
            )
            st.plotly_chart(fig_cost, use_container_width=True)
        
        with col2:
            # 成本趋势
            cost_trend = st.session_state.monitoring_data.groupby(['timestamp', 'provider'])['cost'].sum().reset_index()
            
            fig_cost_trend = px.line(
                cost_trend,
                x='timestamp',
                y='cost',
                color='provider',
                title='成本趋势'
            )
            st.plotly_chart(fig_cost_trend, use_container_width=True)
    
    def render_detailed_table(self):
        """渲染详细数据表"""
        st.subheader("📋 详细数据")
        
        # 数据过滤选项
        col1, col2, col3 = st.columns(3)
        
        with col1:
            selected_providers = st.multiselect(
                "选择提供商",
                options=st.session_state.monitoring_data['provider'].unique(),
                default=st.session_state.monitoring_data['provider'].unique()
            )
        
        with col2:
            start_time = st.datetime_input(
                "开始时间",
                value=datetime.now() - timedelta(hours=1)
            )
        
        with col3:
            end_time = st.datetime_input(
                "结束时间",
                value=datetime.now()
            )
        
        # 过滤数据
        filtered_data = st.session_state.monitoring_data[
            (st.session_state.monitoring_data['provider'].isin(selected_providers)) &
            (st.session_state.monitoring_data['timestamp'] >= pd.Timestamp(start_time)) &
            (st.session_state.monitoring_data['timestamp'] <= pd.Timestamp(end_time))
        ]
        
        # 显示数据表
        st.dataframe(
            filtered_data.sort_values('timestamp', ascending=False),
            use_container_width=True
        )
        
        # 导出功能
        if st.button("📥 导出数据"):
            csv = filtered_data.to_csv(index=False)
            st.download_button(
                label="下载CSV文件",
                data=csv,
                file_name=f"api_monitoring_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv"
            )
    
    def run(self):
        """运行仪表板"""
        self.render_header()
        
        # 主要内容
        self.render_overview_metrics()
        
        st.divider()
        
        self.render_provider_comparison()
        
        st.divider()
        
        self.render_time_series_charts()
        
        st.divider()
        
        col1, col2 = st.columns(2)
        
        with col1:
            self.render_error_analysis()
        
        with col2:
            self.render_cost_analysis()
        
        st.divider()
        
        self.render_detailed_table()

# 运行仪表板
if __name__ == "__main__":
    dashboard = APIMonitoringDashboard()
    dashboard.run()
```

## 部署和优化

### 部署配置

```python
# streamlit_config.py
"""
Streamlit应用部署配置
"""

# .streamlit/config.toml
STREAMLIT_CONFIG = """
[global]
developmentMode = false

[server]
port = 8501
address = "0.0.0.0"
maxUploadSize = 200
enableCORS = false
enableXsrfProtection = true

[browser]
gatherUsageStats = false

[theme]
primaryColor = "#FF6B6B"
backgroundColor = "#FFFFFF"
secondaryBackgroundColor = "#F0F2F6"
textColor = "#262730"
font = "sans serif"
"""

# requirements.txt
REQUIREMENTS = """
streamlit>=1.28.0
pandas>=1.5.0
plotly>=5.15.0
requests>=2.28.0
aiohttp>=3.8.0
python-dotenv>=0.19.0
"""

# Dockerfile
DOCKERFILE = """
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8501

HEALTHCHECK CMD curl --fail http://localhost:8501/_stcore/health

ENTRYPOINT ["streamlit", "run", "app.py", "--server.port=8501", "--server.address=0.0.0.0"]
"""

# docker-compose.yml
DOCKER_COMPOSE = """
version: '3.8'

services:
  streamlit-app:
    build: .
    ports:
      - "8501:8501"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
    volumes:
      - ./data:/app/data
    restart: unless-stopped
"""
```

### 性能优化技巧

```python
# streamlit_optimization.py
import streamlit as st
import pandas as pd
from functools import lru_cache
import time

class StreamlitOptimization:
    """Streamlit性能优化技巧"""
    
    @staticmethod
    @st.cache_data(ttl=300)  # 缓存5分钟
    def load_data(file_path: str) -> pd.DataFrame:
        """缓存数据加载"""
        return pd.read_csv(file_path)
    
    @staticmethod
    @st.cache_resource
    def initialize_model():
        """缓存模型初始化"""
        # 模拟模型加载
        time.sleep(2)
        return {"model": "initialized"}
    
    @staticmethod
    def optimize_dataframe_display():
        """优化DataFrame显示"""
        # 使用分页显示大数据
        data = pd.DataFrame({
            'col1': range(10000),
            'col2': range(10000, 20000)
        })
        
        # 分页显示
        page_size = 100
        page_number = st.number_input('页码', min_value=1, max_value=len(data)//page_size + 1, value=1)
        
        start_idx = (page_number - 1) * page_size
        end_idx = start_idx + page_size
        
        st.dataframe(data.iloc[start_idx:end_idx])
    
    @staticmethod
    def use_session_state_efficiently():
        """高效使用session state"""
        # 避免在session state中存储大对象
        if 'large_data' not in st.session_state:
            st.session_state.large_data_id = "data_123"  # 只存储ID
        
        # 使用回调函数更新状态
        def update_counter():
            st.session_state.counter += 1
        
        if 'counter' not in st.session_state:
            st.session_state.counter = 0
        
        st.button("增加计数", on_click=update_counter)
        st.write(f"计数: {st.session_state.counter}")
    
    @staticmethod
    def optimize_charts():
        """优化图表性能"""
        # 使用采样减少数据点
        large_data = pd.DataFrame({
            'x': range(100000),
            'y': range(100000)
        })
        
        # 采样显示
        sample_size = 1000
        sampled_data = large_data.sample(n=sample_size)
        
        st.line_chart(sampled_data.set_index('x'))

# 使用示例
def demo_optimization():
    """优化演示"""
    st.title("Streamlit性能优化演示")
    
    optimizer = StreamlitOptimization()
    
    tab1, tab2, tab3, tab4 = st.tabs(["数据缓存", "DataFrame优化", "状态管理", "图表优化"])
    
    with tab1:
        st.header("数据缓存")
        if st.button("加载数据"):
            # 这里会使用缓存
            model = optimizer.initialize_model()
            st.success("模型加载完成（使用缓存）")
    
    with tab2:
        st.header("DataFrame分页显示")
        optimizer.optimize_dataframe_display()
    
    with tab3:
        st.header("Session State优化")
        optimizer.use_session_state_efficiently()
    
    with tab4:
        st.header("图表性能优化")
        optimizer.optimize_charts()

if __name__ == "__main__":
    demo_optimization()
```

## 最佳实践总结

### 1. 用户体验优化
- 使用进度条和加载指示器
- 实现响应式布局
- 提供清晰的错误信息
- 支持键盘快捷键

### 2. 性能优化
- 合理使用缓存装饰器
- 避免不必要的重新计算
- 优化大数据集的显示
- 使用异步操作

### 3. 安全考虑
- 验证用户输入
- 安全存储API密钥
- 实现访问控制
- 防止XSS攻击

### 4. 部署建议
- 使用Docker容器化
- 配置健康检查
- 实现日志记录
- 监控应用性能

通过Streamlit，我们可以快速构建功能丰富、用户友好的AI应用原型，为模型接口整合项目提供直观的展示和交互界面。
