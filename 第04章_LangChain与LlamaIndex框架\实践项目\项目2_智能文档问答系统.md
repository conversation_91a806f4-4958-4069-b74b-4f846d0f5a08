# 项目2：智能文档问答系统

## 项目概述

本项目旨在构建一个企业级的智能文档问答系统，能够处理大规模文档集合，提供精准的问答服务，并支持多种文档格式和复杂查询。系统将充分利用LlamaIndex的文档处理能力和LangChain的工作流编排优势。

## 项目目标

1. **大规模文档处理**：支持TB级文档库的高效处理
2. **多格式支持**：处理PDF、Word、Excel、PPT等多种格式
3. **精准问答**：提供高质量的文档问答服务
4. **复杂查询**：支持多步推理和复合查询
5. **企业级部署**：满足企业级性能和安全要求

## 技术架构

```
智能文档问答系统架构
┌─────────────────────────────────────────────────────────┐
│                    API网关层                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │ REST API    │ │ GraphQL     │ │ WebSocket   │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
├─────────────────────────────────────────────────────────┤
│                    业务逻辑层                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │ 问答引擎     │ │ 查询路由     │ │ 结果合成     │        │
│  │(LangChain)  │ │(LangChain)  │ │(LangChain)  │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
├─────────────────────────────────────────────────────────┤
│                    数据处理层                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │ 文档解析     │ │ 内容提取     │ │ 索引构建     │        │
│  │(LlamaIndex) │ │(LlamaIndex) │ │(LlamaIndex) │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
├─────────────────────────────────────────────────────────┤
│                    存储层                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │ 文档存储     │ │ 向量数据库   │ │ 关系数据库   │        │
│  │(MinIO/S3)   │ │(Pinecone)   │ │(PostgreSQL) │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
└─────────────────────────────────────────────────────────┘
```

## 核心功能实现

### 1. 企业级文档处理引擎

```python
# intelligent_doc_qa/core/document_processor.py
from llama_index.core import Document, VectorStoreIndex
from llama_index.core.node_parser import SimpleNodeParser, HierarchicalNodeParser
from llama_index.core.extractors import (
    TitleExtractor, 
    KeywordExtractor, 
    SummaryExtractor,
    QuestionsAnsweredExtractor
)
from llama_index.readers.file import PDFReader, DocxReader, UnstructuredReader
from typing import List, Dict, Any, Optional, Union
import asyncio
import aiofiles
import hashlib
import json
import os
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnterpriseDocumentProcessor:
    """企业级文档处理器"""
    
    def __init__(self, 
                 storage_dir: str = "./enterprise_docs",
                 max_workers: int = 4,
                 chunk_size: int = 1024,
                 chunk_overlap: int = 200):
        self.storage_dir = storage_dir
        self.max_workers = max_workers
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        
        # 创建目录结构
        self.raw_docs_dir = os.path.join(storage_dir, "raw_documents")
        self.processed_docs_dir = os.path.join(storage_dir, "processed_documents")
        self.indexes_dir = os.path.join(storage_dir, "indexes")
        self.metadata_dir = os.path.join(storage_dir, "metadata")
        
        for dir_path in [self.raw_docs_dir, self.processed_docs_dir, 
                        self.indexes_dir, self.metadata_dir]:
            os.makedirs(dir_path, exist_ok=True)
        
        # 初始化处理器
        self.setup_processors()
        
        # 文档统计
        self.processing_stats = {
            "total_processed": 0,
            "successful": 0,
            "failed": 0,
            "processing_time": 0.0
        }
    
    def setup_processors(self):
        """设置文档处理器"""
        # 文档读取器
        self.readers = {
            '.pdf': PDFReader(),
            '.docx': DocxReader(),
            '.doc': DocxReader(),
            '.txt': UnstructuredReader(),
            '.md': UnstructuredReader(),
            '.html': UnstructuredReader()
        }
        
        # 节点解析器
        self.node_parser = HierarchicalNodeParser.from_defaults(
            chunk_sizes=[2048, 512, 128]  # 多层次分块
        )
        
        # 元数据提取器
        self.extractors = [
            TitleExtractor(nodes=5, llm=None),  # 需要配置LLM
            KeywordExtractor(keywords=15, llm=None),
            SummaryExtractor(summaries=["prev", "self", "next"], llm=None),
            QuestionsAnsweredExtractor(questions=5, llm=None)
        ]
    
    async def process_document_batch(self, 
                                   file_paths: List[str],
                                   batch_metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """批量处理文档"""
        start_time = datetime.now()
        
        results = {
            "batch_id": hashlib.md5(str(file_paths).encode()).hexdigest()[:8],
            "total_files": len(file_paths),
            "processed_files": [],
            "failed_files": [],
            "processing_time": 0.0,
            "total_nodes": 0
        }
        
        # 使用线程池并行处理
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 创建任务
            tasks = []
            for file_path in file_paths:
                task = asyncio.create_task(
                    self._process_single_document_async(file_path, batch_metadata)
                )
                tasks.append((file_path, task))
            
            # 等待所有任务完成
            for file_path, task in tasks:
                try:
                    result = await task
                    if result["success"]:
                        results["processed_files"].append(result)
                        results["total_nodes"] += result["node_count"]
                    else:
                        results["failed_files"].append({
                            "file_path": file_path,
                            "error": result["error"]
                        })
                except Exception as e:
                    results["failed_files"].append({
                        "file_path": file_path,
                        "error": str(e)
                    })
        
        # 计算处理时间
        end_time = datetime.now()
        results["processing_time"] = (end_time - start_time).total_seconds()
        
        # 更新统计信息
        self.processing_stats["total_processed"] += results["total_files"]
        self.processing_stats["successful"] += len(results["processed_files"])
        self.processing_stats["failed"] += len(results["failed_files"])
        self.processing_stats["processing_time"] += results["processing_time"]
        
        logger.info(f"批量处理完成: {len(results['processed_files'])}/{results['total_files']} 成功")
        
        return results
    
    async def _process_single_document_async(self, 
                                           file_path: str,
                                           metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """异步处理单个文档"""
        try:
            # 在线程池中执行文档处理
            result = await asyncio.to_thread(
                self._process_single_document_sync, file_path, metadata
            )
            return result
        except Exception as e:
            logger.error(f"处理文档失败 {file_path}: {str(e)}")
            return {
                "success": False,
                "file_path": file_path,
                "error": str(e)
            }
    
    def _process_single_document_sync(self, 
                                    file_path: str,
                                    metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """同步处理单个文档"""
        start_time = datetime.now()
        
        # 获取文件扩展名
        file_ext = os.path.splitext(file_path)[1].lower()
        
        if file_ext not in self.readers:
            raise ValueError(f"不支持的文件格式: {file_ext}")
        
        # 读取文档
        reader = self.readers[file_ext]
        documents = reader.load_data(file=file_path)
        
        if not documents:
            raise ValueError("文档读取失败或为空")
        
        # 处理每个文档
        all_nodes = []
        for doc in documents:
            # 添加元数据
            doc_metadata = {
                "file_path": file_path,
                "file_name": os.path.basename(file_path),
                "file_size": os.path.getsize(file_path),
                "processed_at": datetime.now().isoformat(),
                "file_type": file_ext
            }
            
            if metadata:
                doc_metadata.update(metadata)
            
            doc.metadata.update(doc_metadata)
            
            # 解析节点
            nodes = self.node_parser.get_nodes_from_documents([doc])
            
            # 提取元数据（如果配置了LLM）
            if hasattr(self, 'llm') and self.llm:
                for extractor in self.extractors:
                    nodes = extractor.extract(nodes)
            
            all_nodes.extend(nodes)
        
        # 保存处理结果
        doc_id = self._generate_document_id(file_path)
        self._save_processed_document(doc_id, all_nodes, doc_metadata)
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        return {
            "success": True,
            "file_path": file_path,
            "document_id": doc_id,
            "node_count": len(all_nodes),
            "processing_time": processing_time,
            "metadata": doc_metadata
        }
    
    def _generate_document_id(self, file_path: str) -> str:
        """生成文档ID"""
        file_content_hash = hashlib.md5(file_path.encode()).hexdigest()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"doc_{timestamp}_{file_content_hash[:8]}"
    
    def _save_processed_document(self, doc_id: str, nodes: List, metadata: Dict[str, Any]):
        """保存处理后的文档"""
        # 保存节点数据
        nodes_file = os.path.join(self.processed_docs_dir, f"{doc_id}_nodes.json")
        nodes_data = []
        
        for node in nodes:
            node_data = {
                "id": node.node_id,
                "text": node.text,
                "metadata": node.metadata,
                "relationships": getattr(node, 'relationships', {})
            }
            nodes_data.append(node_data)
        
        with open(nodes_file, 'w', encoding='utf-8') as f:
            json.dump(nodes_data, f, ensure_ascii=False, indent=2)
        
        # 保存文档元数据
        metadata_file = os.path.join(self.metadata_dir, f"{doc_id}_metadata.json")
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)
        
        logger.info(f"文档已保存: {doc_id} ({len(nodes)} 个节点)")
    
    def get_processing_statistics(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        stats = self.processing_stats.copy()
        
        if stats["total_processed"] > 0:
            stats["success_rate"] = stats["successful"] / stats["total_processed"]
            stats["avg_processing_time"] = stats["processing_time"] / stats["total_processed"]
        else:
            stats["success_rate"] = 0.0
            stats["avg_processing_time"] = 0.0
        
        return stats
    
    def list_processed_documents(self) -> List[Dict[str, Any]]:
        """列出已处理的文档"""
        documents = []
        
        for filename in os.listdir(self.metadata_dir):
            if filename.endswith("_metadata.json"):
                doc_id = filename.replace("_metadata.json", "")
                metadata_file = os.path.join(self.metadata_dir, filename)
                
                try:
                    with open(metadata_file, 'r', encoding='utf-8') as f:
                        metadata = json.load(f)
                    
                    # 检查节点文件是否存在
                    nodes_file = os.path.join(self.processed_docs_dir, f"{doc_id}_nodes.json")
                    node_count = 0
                    
                    if os.path.exists(nodes_file):
                        with open(nodes_file, 'r', encoding='utf-8') as f:
                            nodes_data = json.load(f)
                            node_count = len(nodes_data)
                    
                    documents.append({
                        "document_id": doc_id,
                        "metadata": metadata,
                        "node_count": node_count,
                        "status": "processed"
                    })
                    
                except Exception as e:
                    logger.error(f"读取文档元数据失败 {doc_id}: {str(e)}")
        
        return documents

# 高级索引管理器
class AdvancedIndexManager:
    """高级索引管理器"""
    
    def __init__(self, 
                 storage_dir: str,
                 vector_store_type: str = "simple",
                 embedding_model: str = "text-embedding-ada-002"):
        self.storage_dir = storage_dir
        self.vector_store_type = vector_store_type
        self.embedding_model = embedding_model
        
        # 索引存储
        self.indexes = {}
        self.index_metadata = {}
        
        # 初始化向量存储
        self.setup_vector_store()
    
    def setup_vector_store(self):
        """设置向量存储"""
        if self.vector_store_type == "pinecone":
            # 配置Pinecone
            self._setup_pinecone()
        elif self.vector_store_type == "chroma":
            # 配置Chroma
            self._setup_chroma()
        else:
            # 使用简单向量存储
            self._setup_simple_vector_store()
    
    def _setup_simple_vector_store(self):
        """设置简单向量存储"""
        from llama_index.core.vector_stores import SimpleVectorStore
        from llama_index.core.storage.storage_context import StorageContext
        
        self.vector_store = SimpleVectorStore()
        self.storage_context = StorageContext.from_defaults(
            vector_store=self.vector_store
        )
    
    def _setup_pinecone(self):
        """设置Pinecone向量存储"""
        try:
            import pinecone
            from llama_index.vector_stores.pinecone import PineconeVectorStore
            
            # 初始化Pinecone（需要API密钥）
            # pinecone.init(api_key="your-api-key", environment="your-env")
            
            # 创建或连接到索引
            # index = pinecone.Index("document-qa-index")
            # self.vector_store = PineconeVectorStore(pinecone_index=index)
            
            logger.info("Pinecone向量存储已配置")
        except ImportError:
            logger.warning("Pinecone未安装，使用简单向量存储")
            self._setup_simple_vector_store()
    
    def _setup_chroma(self):
        """设置Chroma向量存储"""
        try:
            import chromadb
            from llama_index.vector_stores.chroma import ChromaVectorStore
            
            # 创建Chroma客户端
            chroma_client = chromadb.PersistentClient(path=self.storage_dir)
            chroma_collection = chroma_client.get_or_create_collection("documents")
            
            self.vector_store = ChromaVectorStore(chroma_collection=chroma_collection)
            
            logger.info("Chroma向量存储已配置")
        except ImportError:
            logger.warning("Chroma未安装，使用简单向量存储")
            self._setup_simple_vector_store()
    
    def create_index_from_documents(self, 
                                  document_ids: List[str],
                                  index_name: str,
                                  index_type: str = "vector") -> str:
        """从文档创建索引"""
        # 加载文档节点
        all_nodes = []
        
        for doc_id in document_ids:
            nodes = self._load_document_nodes(doc_id)
            all_nodes.extend(nodes)
        
        if not all_nodes:
            raise ValueError("没有找到有效的文档节点")
        
        # 创建索引
        if index_type == "vector":
            index = VectorStoreIndex(
                nodes=all_nodes,
                storage_context=self.storage_context
            )
        else:
            raise ValueError(f"不支持的索引类型: {index_type}")
        
        # 保存索引
        self.indexes[index_name] = index
        self.index_metadata[index_name] = {
            "document_ids": document_ids,
            "node_count": len(all_nodes),
            "created_at": datetime.now().isoformat(),
            "index_type": index_type
        }
        
        logger.info(f"索引已创建: {index_name} ({len(all_nodes)} 个节点)")
        
        return index_name
    
    def _load_document_nodes(self, doc_id: str) -> List:
        """加载文档节点"""
        from llama_index.core.schema import TextNode
        
        nodes_file = os.path.join(
            self.storage_dir, "processed_documents", f"{doc_id}_nodes.json"
        )
        
        if not os.path.exists(nodes_file):
            logger.warning(f"节点文件不存在: {nodes_file}")
            return []
        
        try:
            with open(nodes_file, 'r', encoding='utf-8') as f:
                nodes_data = json.load(f)
            
            nodes = []
            for node_data in nodes_data:
                node = TextNode(
                    id_=node_data["id"],
                    text=node_data["text"],
                    metadata=node_data["metadata"]
                )
                nodes.append(node)
            
            return nodes
            
        except Exception as e:
            logger.error(f"加载节点失败 {doc_id}: {str(e)}")
            return []
    
    def get_index(self, index_name: str):
        """获取索引"""
        return self.indexes.get(index_name)
    
    def list_indexes(self) -> List[Dict[str, Any]]:
        """列出所有索引"""
        index_list = []
        
        for name, metadata in self.index_metadata.items():
            index_info = {
                "name": name,
                "metadata": metadata,
                "status": "active" if name in self.indexes else "inactive"
            }
            index_list.append(index_info)
        
        return index_list
    
    def delete_index(self, index_name: str) -> bool:
        """删除索引"""
        if index_name in self.indexes:
            del self.indexes[index_name]
        
        if index_name in self.index_metadata:
            del self.index_metadata[index_name]
        
        logger.info(f"索引已删除: {index_name}")
        return True

# 使用示例
async def demo_enterprise_document_processor():
    """演示企业级文档处理器"""
    # 初始化处理器
    processor = EnterpriseDocumentProcessor(
        storage_dir="./demo_enterprise_docs",
        max_workers=2
    )
    
    # 模拟文档文件（实际使用时需要真实文件）
    sample_files = [
        "sample_doc1.txt",
        "sample_doc2.txt", 
        "sample_doc3.txt"
    ]
    
    # 创建示例文件
    os.makedirs("./demo_files", exist_ok=True)
    for i, filename in enumerate(sample_files):
        file_path = f"./demo_files/{filename}"
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(f"这是示例文档 {i+1} 的内容。包含一些测试文本用于演示文档处理功能。")
    
    # 批量处理文档
    file_paths = [f"./demo_files/{f}" for f in sample_files]
    
    print("=== 企业级文档处理演示 ===")
    
    try:
        results = await processor.process_document_batch(
            file_paths=file_paths,
            batch_metadata={"department": "研发部", "project": "文档问答系统"}
        )
        
        print(f"批量处理结果:")
        print(f"- 总文件数: {results['total_files']}")
        print(f"- 成功处理: {len(results['processed_files'])}")
        print(f"- 处理失败: {len(results['failed_files'])}")
        print(f"- 总节点数: {results['total_nodes']}")
        print(f"- 处理时间: {results['processing_time']:.2f}秒")
        
        # 显示处理统计
        stats = processor.get_processing_statistics()
        print(f"\n处理统计:")
        print(f"- 成功率: {stats['success_rate']:.1%}")
        print(f"- 平均处理时间: {stats['avg_processing_time']:.2f}秒")
        
        # 列出已处理文档
        documents = processor.list_processed_documents()
        print(f"\n已处理文档: {len(documents)} 个")
        
        # 创建索引管理器
        index_manager = AdvancedIndexManager(processor.storage_dir)
        
        # 创建索引
        if documents:
            doc_ids = [doc["document_id"] for doc in documents]
            index_name = index_manager.create_index_from_documents(
                document_ids=doc_ids,
                index_name="demo_index"
            )
            print(f"索引已创建: {index_name}")
            
            # 列出索引
            indexes = index_manager.list_indexes()
            print(f"可用索引: {[idx['name'] for idx in indexes]}")
        
    except Exception as e:
        print(f"处理失败: {str(e)}")
    
    return processor

if __name__ == "__main__":
    asyncio.run(demo_enterprise_document_processor())
```

### 2. 智能查询引擎

```python
# intelligent_doc_qa/core/query_engine.py
from langchain.chains import ConversationalRetrievalChain, LLMChain
from langchain.chains.router import MultiPromptChain, LLMRouterChain
from langchain.chains.router.multi_prompt_prompt import MULTI_PROMPT_ROUTER_TEMPLATE
from langchain.prompts import PromptTemplate
from langchain.schema import BaseOutputParser
from llama_index.core.query_engine import RetrieverQueryEngine, SubQuestionQueryEngine
from llama_index.core.tools import QueryEngineTool, ToolMetadata
from typing import List, Dict, Any, Optional, Union
import asyncio
import json
from datetime import datetime

class IntelligentQueryEngine:
    """智能查询引擎 - 结合LangChain和LlamaIndex的查询能力"""
    
    def __init__(self, index_manager, llm, embedding_model=None):
        self.index_manager = index_manager
        self.llm = llm
        self.embedding_model = embedding_model
        
        # 查询历史
        self.query_history = []
        
        # 查询策略
        self.query_strategies = {
            "simple": self._simple_query,
            "multi_step": self._multi_step_query,
            "sub_question": self._sub_question_query,
            "conversational": self._conversational_query
        }
        
        # 初始化查询路由
        self.setup_query_router()
    
    def setup_query_router(self):
        """设置查询路由"""
        # 定义不同类型的查询处理器
        self.query_processors = {
            "factual": {
                "description": "回答事实性问题，需要从文档中查找具体信息",
                "prompt_template": """
                基于以下文档内容回答问题，要求准确、具体：
                
                文档内容：
                {context}
                
                问题：{question}
                
                请提供准确的事实性回答：
                """,
                "strategy": "simple"
            },
            "analytical": {
                "description": "分析性问题，需要综合多个信息源进行分析",
                "prompt_template": """
                基于以下文档内容进行分析，要求深入、全面：
                
                文档内容：
                {context}
                
                问题：{question}
                
                请提供深入的分析：
                """,
                "strategy": "multi_step"
            },
            "comparative": {
                "description": "比较性问题，需要对比不同信息或观点",
                "prompt_template": """
                基于以下文档内容进行比较分析：
                
                文档内容：
                {context}
                
                问题：{question}
                
                请提供详细的比较分析：
                """,
                "strategy": "sub_question"
            },
            "conversational": {
                "description": "对话性问题，需要考虑上下文和历史对话",
                "prompt_template": """
                基于文档内容和对话历史回答问题：
                
                文档内容：
                {context}
                
                对话历史：
                {chat_history}
                
                当前问题：{question}
                
                请提供连贯的回答：
                """,
                "strategy": "conversational"
            }
        }
    
    def analyze_query_type(self, query: str) -> str:
        """分析查询类型"""
        query_lower = query.lower()
        
        # 简单的规则分类
        if any(word in query_lower for word in ["什么", "哪个", "多少", "何时", "何地"]):
            return "factual"
        elif any(word in query_lower for word in ["为什么", "如何", "分析", "解释"]):
            return "analytical"
        elif any(word in query_lower for word in ["比较", "对比", "区别", "相同", "不同"]):
            return "comparative"
        elif len(self.query_history) > 0:
            return "conversational"
        else:
            return "factual"
    
    async def query(self, 
                   question: str,
                   index_names: Optional[List[str]] = None,
                   query_type: Optional[str] = None,
                   max_sources: int = 5) -> Dict[str, Any]:
        """执行智能查询"""
        start_time = datetime.now()
        
        # 分析查询类型
        if not query_type:
            query_type = self.analyze_query_type(question)
        
        # 获取查询处理器配置
        processor_config = self.query_processors.get(query_type, self.query_processors["factual"])
        strategy = processor_config["strategy"]
        
        # 选择索引
        if not index_names:
            index_names = list(self.index_manager.indexes.keys())
        
        if not index_names:
            return {
                "question": question,
                "answer": "没有可用的索引进行查询",
                "sources": [],
                "query_type": query_type,
                "processing_time": 0.0
            }
        
        try:
            # 执行查询策略
            result = await self.query_strategies[strategy](
                question, index_names, processor_config, max_sources
            )
            
            # 计算处理时间
            processing_time = (datetime.now() - start_time).total_seconds()
            result["processing_time"] = processing_time
            result["query_type"] = query_type
            
            # 记录查询历史
            self.query_history.append({
                "question": question,
                "answer": result["answer"],
                "query_type": query_type,
                "timestamp": start_time.isoformat()
            })
            
            # 限制历史记录数量
            if len(self.query_history) > 50:
                self.query_history = self.query_history[-50:]
            
            return result
            
        except Exception as e:
            return {
                "question": question,
                "answer": f"查询处理失败: {str(e)}",
                "sources": [],
                "query_type": query_type,
                "processing_time": (datetime.now() - start_time).total_seconds(),
                "error": str(e)
            }
    
    async def _simple_query(self, question: str, index_names: List[str], 
                          config: Dict[str, Any], max_sources: int) -> Dict[str, Any]:
        """简单查询策略"""
        # 使用第一个可用索引
        index_name = index_names[0]
        index = self.index_manager.get_index(index_name)
        
        if not index:
            raise ValueError(f"索引不存在: {index_name}")
        
        # 创建查询引擎
        query_engine = index.as_query_engine(
            similarity_top_k=max_sources,
            response_mode="compact"
        )
        
        # 执行查询
        response = query_engine.query(question)
        
        # 提取源文档
        sources = []
        if hasattr(response, 'source_nodes'):
            for node in response.source_nodes:
                sources.append({
                    "content": node.node.text,
                    "metadata": node.node.metadata,
                    "score": getattr(node, 'score', 0.0)
                })
        
        return {
            "question": question,
            "answer": str(response),
            "sources": sources,
            "strategy": "simple"
        }
    
    async def _multi_step_query(self, question: str, index_names: List[str],
                              config: Dict[str, Any], max_sources: int) -> Dict[str, Any]:
        """多步查询策略"""
        # 分解问题为多个子步骤
        sub_questions = await self._decompose_question(question)
        
        all_sources = []
        sub_answers = []
        
        # 对每个子问题进行查询
        for sub_q in sub_questions:
            sub_result = await self._simple_query(sub_q, index_names, config, max_sources)
            sub_answers.append({
                "question": sub_q,
                "answer": sub_result["answer"]
            })
            all_sources.extend(sub_result["sources"])
        
        # 合成最终答案
        final_answer = await self._synthesize_multi_step_answer(question, sub_answers)
        
        return {
            "question": question,
            "answer": final_answer,
            "sources": all_sources[:max_sources],  # 限制源数量
            "sub_questions": sub_questions,
            "sub_answers": sub_answers,
            "strategy": "multi_step"
        }
    
    async def _sub_question_query(self, question: str, index_names: List[str],
                                config: Dict[str, Any], max_sources: int) -> Dict[str, Any]:
        """子问题查询策略"""
        # 创建查询引擎工具
        query_engine_tools = []
        
        for index_name in index_names:
            index = self.index_manager.get_index(index_name)
            if index:
                tool = QueryEngineTool(
                    query_engine=index.as_query_engine(),
                    metadata=ToolMetadata(
                        name=f"index_{index_name}",
                        description=f"查询索引 {index_name} 中的文档"
                    )
                )
                query_engine_tools.append(tool)
        
        if not query_engine_tools:
            raise ValueError("没有可用的查询引擎工具")
        
        # 创建子问题查询引擎
        sub_question_engine = SubQuestionQueryEngine.from_defaults(
            query_engine_tools=query_engine_tools,
            verbose=True
        )
        
        # 执行查询
        response = sub_question_engine.query(question)
        
        # 提取源文档
        sources = []
        if hasattr(response, 'source_nodes'):
            for node in response.source_nodes:
                sources.append({
                    "content": node.node.text,
                    "metadata": node.node.metadata,
                    "score": getattr(node, 'score', 0.0)
                })
        
        return {
            "question": question,
            "answer": str(response),
            "sources": sources[:max_sources],
            "strategy": "sub_question"
        }
    
    async def _conversational_query(self, question: str, index_names: List[str],
                                  config: Dict[str, Any], max_sources: int) -> Dict[str, Any]:
        """对话式查询策略"""
        # 获取对话历史
        chat_history = self._format_chat_history()
        
        # 使用简单查询获取相关文档
        simple_result = await self._simple_query(question, index_names, config, max_sources)
        
        # 构建对话式提示
        context = "\n\n".join([source["content"] for source in simple_result["sources"]])
        
        prompt = config["prompt_template"].format(
            context=context,
            chat_history=chat_history,
            question=question
        )
        
        # 使用LLM生成对话式回答
        conversational_answer = await asyncio.to_thread(self.llm, prompt)
        
        return {
            "question": question,
            "answer": conversational_answer,
            "sources": simple_result["sources"],
            "chat_history": chat_history,
            "strategy": "conversational"
        }
    
    async def _decompose_question(self, question: str) -> List[str]:
        """分解复杂问题为子问题"""
        decompose_prompt = f"""
        将以下复杂问题分解为2-4个更简单的子问题：
        
        原问题：{question}
        
        请列出子问题（每行一个）：
        """
        
        response = await asyncio.to_thread(self.llm, decompose_prompt)
        
        # 解析子问题
        sub_questions = []
        for line in response.split('\n'):
            line = line.strip()
            if line and not line.startswith('子问题') and '?' in line:
                sub_questions.append(line)
        
        return sub_questions[:4]  # 最多4个子问题
    
    async def _synthesize_multi_step_answer(self, original_question: str, 
                                          sub_answers: List[Dict[str, str]]) -> str:
        """合成多步查询的最终答案"""
        sub_qa_text = "\n\n".join([
            f"问题：{qa['question']}\n答案：{qa['answer']}"
            for qa in sub_answers
        ])
        
        synthesis_prompt = f"""
        基于以下子问题的答案，为原始问题提供综合性回答：
        
        原始问题：{original_question}
        
        子问题和答案：
        {sub_qa_text}
        
        请提供综合性的最终答案：
        """
        
        final_answer = await asyncio.to_thread(self.llm, synthesis_prompt)
        return final_answer
    
    def _format_chat_history(self, max_history: int = 5) -> str:
        """格式化对话历史"""
        if not self.query_history:
            return "无对话历史"
        
        recent_history = self.query_history[-max_history:]
        formatted_history = []
        
        for i, entry in enumerate(recent_history):
            formatted_history.append(f"Q{i+1}: {entry['question']}")
            formatted_history.append(f"A{i+1}: {entry['answer']}")
        
        return "\n".join(formatted_history)
    
    def get_query_statistics(self) -> Dict[str, Any]:
        """获取查询统计信息"""
        if not self.query_history:
            return {"total_queries": 0}
        
        # 统计查询类型
        type_counts = {}
        for entry in self.query_history:
            query_type = entry.get("query_type", "unknown")
            type_counts[query_type] = type_counts.get(query_type, 0) + 1
        
        return {
            "total_queries": len(self.query_history),
            "query_types": type_counts,
            "recent_queries": self.query_history[-10:]  # 最近10个查询
        }
    
    def clear_history(self):
        """清空查询历史"""
        self.query_history.clear()

# 使用示例
async def demo_intelligent_query_engine():
    """演示智能查询引擎"""
    from langchain.llms import OpenAI
    
    # 这里需要实际的索引管理器和LLM
    print("=== 智能查询引擎演示 ===")
    print("注意：需要配置真实的索引管理器和LLM才能运行完整功能")
    
    # 模拟查询引擎
    class MockIndexManager:
        def __init__(self):
            self.indexes = {"demo_index": "mock_index"}
        
        def get_index(self, name):
            return None  # 模拟索引
    
    class MockLLM:
        def __call__(self, prompt):
            return "这是模拟的LLM响应"
    
    # 创建模拟组件
    index_manager = MockIndexManager()
    llm = MockLLM()
    
    # 创建查询引擎
    query_engine = IntelligentQueryEngine(index_manager, llm)
    
    # 测试查询类型分析
    test_queries = [
        "什么是人工智能？",
        "为什么深度学习如此重要？",
        "比较机器学习和深度学习的区别",
        "基于之前的讨论，还有什么问题？"
    ]
    
    for query in test_queries:
        query_type = query_engine.analyze_query_type(query)
        print(f"查询: {query}")
        print(f"类型: {query_type}")
        print()
    
    # 显示查询统计
    stats = query_engine.get_query_statistics()
    print(f"查询统计: {stats}")

if __name__ == "__main__":
    asyncio.run(demo_intelligent_query_engine())
```

## 项目扩展功能

### 1. 实时协作功能

```python
# intelligent_doc_qa/features/collaboration.py
import asyncio
import websockets
import json
from typing import Dict, List, Set
from datetime import datetime

class CollaborationManager:
    """实时协作管理器"""
    
    def __init__(self):
        self.active_sessions: Dict[str, Set[websockets.WebSocketServerProtocol]] = {}
        self.shared_queries: Dict[str, Dict] = {}
        self.user_activities: List[Dict] = []
    
    async def handle_client(self, websocket, path):
        """处理客户端连接"""
        session_id = path.split('/')[-1]
        
        if session_id not in self.active_sessions:
            self.active_sessions[session_id] = set()
        
        self.active_sessions[session_id].add(websocket)
        
        try:
            async for message in websocket:
                await self.handle_message(session_id, websocket, message)
        finally:
            self.active_sessions[session_id].discard(websocket)
    
    async def handle_message(self, session_id: str, websocket, message: str):
        """处理消息"""
        try:
            data = json.loads(message)
            message_type = data.get('type')
            
            if message_type == 'query':
                await self.handle_shared_query(session_id, data)
            elif message_type == 'annotation':
                await self.handle_annotation(session_id, data)
            
        except Exception as e:
            await websocket.send(json.dumps({
                'type': 'error',
                'message': str(e)
            }))
    
    async def handle_shared_query(self, session_id: str, data: Dict):
        """处理共享查询"""
        query_id = data.get('query_id')
        query_text = data.get('query')
        user_id = data.get('user_id')
        
        # 记录查询
        self.shared_queries[query_id] = {
            'query': query_text,
            'user_id': user_id,
            'session_id': session_id,
            'timestamp': datetime.now().isoformat(),
            'status': 'processing'
        }
        
        # 广播给会话中的所有用户
        await self.broadcast_to_session(session_id, {
            'type': 'shared_query',
            'query_id': query_id,
            'query': query_text,
            'user_id': user_id
        })
    
    async def broadcast_to_session(self, session_id: str, message: Dict):
        """向会话中的所有用户广播消息"""
        if session_id in self.active_sessions:
            message_str = json.dumps(message)
            
            # 发送给所有连接的客户端
            disconnected = set()
            for websocket in self.active_sessions[session_id]:
                try:
                    await websocket.send(message_str)
                except websockets.exceptions.ConnectionClosed:
                    disconnected.add(websocket)
            
            # 清理断开的连接
            for websocket in disconnected:
                self.active_sessions[session_id].discard(websocket)
```

### 2. 多模态文档支持

```python
# intelligent_doc_qa/features/multimodal.py
from typing import List, Dict, Any, Optional
import base64
from PIL import Image
import io

class MultimodalProcessor:
    """多模态文档处理器"""
    
    def __init__(self):
        self.image_processors = self._setup_image_processors()
        self.audio_processors = self._setup_audio_processors()
    
    def _setup_image_processors(self):
        """设置图像处理器"""
        return {
            'ocr': self._ocr_extract_text,
            'description': self._generate_image_description,
            'chart_analysis': self._analyze_charts
        }
    
    def _setup_audio_processors(self):
        """设置音频处理器"""
        return {
            'transcription': self._transcribe_audio,
            'speaker_identification': self._identify_speakers
        }
    
    def process_image(self, image_data: bytes, 
                     processing_types: List[str] = None) -> Dict[str, Any]:
        """处理图像"""
        if processing_types is None:
            processing_types = ['ocr', 'description']
        
        results = {}
        
        # 加载图像
        image = Image.open(io.BytesIO(image_data))
        
        for proc_type in processing_types:
            if proc_type in self.image_processors:
                try:
                    result = self.image_processors[proc_type](image)
                    results[proc_type] = result
                except Exception as e:
                    results[proc_type] = {'error': str(e)}
        
        return results
    
    def _ocr_extract_text(self, image: Image.Image) -> Dict[str, Any]:
        """OCR文本提取"""
        try:
            import pytesseract
            
            # 提取文本
            text = pytesseract.image_to_string(image, lang='chi_sim+eng')
            
            # 获取详细信息
            data = pytesseract.image_to_data(image, output_type=pytesseract.Output.DICT)
            
            return {
                'text': text,
                'confidence': sum(data['conf']) / len(data['conf']),
                'word_count': len(text.split())
            }
        except ImportError:
            return {'error': 'pytesseract未安装'}
    
    def _generate_image_description(self, image: Image.Image) -> Dict[str, Any]:
        """生成图像描述"""
        # 这里可以集成视觉语言模型
        return {
            'description': '图像描述功能需要集成视觉语言模型',
            'objects': [],
            'scene': 'unknown'
        }
    
    def _analyze_charts(self, image: Image.Image) -> Dict[str, Any]:
        """分析图表"""
        # 这里可以集成图表分析模型
        return {
            'chart_type': 'unknown',
            'data_points': [],
            'insights': []
        }
```

## 项目价值

1. **企业级应用**：满足大规模文档处理需求
2. **技术整合**：展示框架协同应用的最佳实践
3. **性能优化**：提供高效的文档问答解决方案
4. **可扩展性**：支持多种扩展功能和定制需求
5. **实用价值**：解决实际的企业文档管理问题

这个智能文档问答系统项目展示了如何构建企业级的AI应用，充分利用了LangChain和LlamaIndex的各自优势，为学员提供了完整的项目开发经验。
