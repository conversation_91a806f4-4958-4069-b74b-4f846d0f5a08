"""
LlamaIndex高级功能示例代码
包含自定义索引、高级查询引擎、多模态处理等高级特性的完整示例
"""

from llama_index.core import Document, VectorStoreIndex, SimpleDirectoryReader
from llama_index.core.node_parser import SimpleNodeParser, SentenceWindowNodeParser
from llama_index.core.extractors import TitleExtractor, KeywordExtractor, SummaryExtractor
from llama_index.core.query_engine import RetrieverQueryEngine, SubQuestionQueryEngine
from llama_index.core.retrievers import VectorIndexRetriever
from llama_index.core.postprocessor import SimilarityPostprocessor, KeywordNodePostprocessor
from llama_index.core.base.base_retriever import BaseRetriever
from llama_index.core.schema import NodeWithScore, QueryBundle
from llama_index.core.base import BaseIndex
from typing import List, Dict, Any, Optional, Union
import numpy as np
import asyncio
import json
import os
from datetime import datetime
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

# ============================================================================
# 1. 自定义索引实现
# ============================================================================

class HybridIndex(BaseIndex):
    """混合索引 - 结合向量检索和关键词检索"""
    
    def __init__(self, documents: List[Document], **kwargs):
        super().__init__(**kwargs)
        self.documents = documents
        self.vector_index = None
        self.keyword_index = {}
        self.tfidf_vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')
        self.tfidf_matrix = None
        self._build_index()
    
    def _build_index(self):
        """构建混合索引"""
        # 构建向量索引
        self.vector_index = VectorStoreIndex.from_documents(self.documents)
        
        # 构建关键词索引
        texts = [doc.text for doc in self.documents]
        
        # 构建TF-IDF矩阵
        self.tfidf_matrix = self.tfidf_vectorizer.fit_transform(texts)
        
        # 构建关键词倒排索引
        for i, doc in enumerate(self.documents):
            words = doc.text.lower().split()
            for word in set(words):  # 去重
                if len(word) > 2:  # 过滤短词
                    if word not in self.keyword_index:
                        self.keyword_index[word] = []
                    self.keyword_index[word].append(i)
        
        print(f"混合索引构建完成: {len(self.documents)} 文档, {len(self.keyword_index)} 关键词")
    
    def as_retriever(self, **kwargs) -> 'HybridRetriever':
        """返回混合检索器"""
        return HybridRetriever(self, **kwargs)
    
    def _insert(self, nodes, **kwargs):
        """插入新节点"""
        # 简化实现
        pass
    
    def _delete_node(self, node_id: str, **kwargs):
        """删除节点"""
        # 简化实现
        pass
    
    def ref_doc_info(self) -> Dict[str, Any]:
        """返回文档信息"""
        return {"num_docs": len(self.documents)}

class HybridRetriever(BaseRetriever):
    """混合检索器"""
    
    def __init__(self, index: HybridIndex, 
                 similarity_top_k: int = 5,
                 vector_weight: float = 0.7,
                 keyword_weight: float = 0.3):
        super().__init__()
        self.index = index
        self.similarity_top_k = similarity_top_k
        self.vector_weight = vector_weight
        self.keyword_weight = keyword_weight
    
    def _retrieve(self, query_bundle: QueryBundle) -> List[NodeWithScore]:
        """执行混合检索"""
        query_text = query_bundle.query_str
        
        # 向量检索
        vector_scores = self._vector_retrieve(query_text)
        
        # 关键词检索
        keyword_scores = self._keyword_retrieve(query_text)
        
        # 合并分数
        combined_scores = self._combine_scores(vector_scores, keyword_scores)
        
        # 排序并返回top-k结果
        sorted_results = sorted(combined_scores.items(), key=lambda x: x[1], reverse=True)
        top_results = sorted_results[:self.similarity_top_k]
        
        # 转换为NodeWithScore格式
        nodes_with_scores = []
        for doc_idx, score in top_results:
            if doc_idx < len(self.index.documents):
                doc = self.index.documents[doc_idx]
                # 创建简化的节点
                node = type('Node', (), {
                    'text': doc.text,
                    'metadata': doc.metadata,
                    'node_id': f"doc_{doc_idx}"
                })()
                
                nodes_with_scores.append(NodeWithScore(node=node, score=score))
        
        return nodes_with_scores
    
    def _vector_retrieve(self, query: str) -> Dict[int, float]:
        """向量检索"""
        # 使用TF-IDF进行向量检索
        query_vector = self.index.tfidf_vectorizer.transform([query])
        similarities = cosine_similarity(query_vector, self.index.tfidf_matrix).flatten()
        
        return {i: float(sim) for i, sim in enumerate(similarities)}
    
    def _keyword_retrieve(self, query: str) -> Dict[int, float]:
        """关键词检索"""
        query_words = query.lower().split()
        keyword_scores = {}
        
        for word in query_words:
            if word in self.index.keyword_index:
                doc_indices = self.index.keyword_index[word]
                for doc_idx in doc_indices:
                    keyword_scores[doc_idx] = keyword_scores.get(doc_idx, 0) + 1
        
        # 归一化分数
        if keyword_scores:
            max_score = max(keyword_scores.values())
            keyword_scores = {k: v / max_score for k, v in keyword_scores.items()}
        
        return keyword_scores
    
    def _combine_scores(self, vector_scores: Dict[int, float], 
                       keyword_scores: Dict[int, float]) -> Dict[int, float]:
        """合并检索分数"""
        combined_scores = {}
        
        # 获取所有文档索引
        all_indices = set(vector_scores.keys()) | set(keyword_scores.keys())
        
        for idx in all_indices:
            vector_score = vector_scores.get(idx, 0.0)
            keyword_score = keyword_scores.get(idx, 0.0)
            
            combined_score = (
                self.vector_weight * vector_score + 
                self.keyword_weight * keyword_score
            )
            
            combined_scores[idx] = combined_score
        
        return combined_scores

# ============================================================================
# 2. 高级查询引擎
# ============================================================================

class AdaptiveQueryEngine:
    """自适应查询引擎 - 根据查询类型选择最佳策略"""
    
    def __init__(self, index, llm=None):
        self.index = index
        self.llm = llm
        self.query_history = []
        self.performance_metrics = {}
        
        # 查询策略
        self.strategies = {
            "simple": self._simple_query,
            "detailed": self._detailed_query,
            "comparative": self._comparative_query,
            "analytical": self._analytical_query
        }
    
    def query(self, query_text: str, strategy: str = "auto") -> Dict[str, Any]:
        """执行自适应查询"""
        start_time = datetime.now()
        
        # 自动选择策略
        if strategy == "auto":
            strategy = self._select_strategy(query_text)
        
        # 执行查询
        try:
            result = self.strategies[strategy](query_text)
            success = True
            error = None
        except Exception as e:
            result = {"answer": f"查询失败: {str(e)}", "sources": []}
            success = False
            error = str(e)
        
        # 记录性能指标
        processing_time = (datetime.now() - start_time).total_seconds()
        
        query_record = {
            "query": query_text,
            "strategy": strategy,
            "processing_time": processing_time,
            "success": success,
            "error": error,
            "timestamp": start_time.isoformat()
        }
        
        self.query_history.append(query_record)
        self._update_performance_metrics(strategy, processing_time, success)
        
        result.update({
            "strategy_used": strategy,
            "processing_time": processing_time,
            "query_id": len(self.query_history)
        })
        
        return result
    
    def _select_strategy(self, query_text: str) -> str:
        """选择查询策略"""
        query_lower = query_text.lower()
        
        # 基于关键词的策略选择
        if any(word in query_lower for word in ["比较", "对比", "区别", "相同", "不同"]):
            return "comparative"
        elif any(word in query_lower for word in ["分析", "为什么", "原因", "影响"]):
            return "analytical"
        elif any(word in query_lower for word in ["详细", "具体", "深入", "全面"]):
            return "detailed"
        else:
            return "simple"
    
    def _simple_query(self, query_text: str) -> Dict[str, Any]:
        """简单查询策略"""
        query_engine = self.index.as_query_engine(
            similarity_top_k=3,
            response_mode="compact"
        )
        
        response = query_engine.query(query_text)
        
        sources = []
        if hasattr(response, 'source_nodes'):
            for node in response.source_nodes:
                sources.append({
                    "content": node.node.text[:200] + "...",
                    "metadata": node.node.metadata,
                    "score": getattr(node, 'score', 0.0)
                })
        
        return {
            "answer": str(response),
            "sources": sources,
            "strategy_details": "使用简单检索策略"
        }
    
    def _detailed_query(self, query_text: str) -> Dict[str, Any]:
        """详细查询策略"""
        query_engine = self.index.as_query_engine(
            similarity_top_k=8,
            response_mode="tree_summarize"
        )
        
        response = query_engine.query(query_text)
        
        sources = []
        if hasattr(response, 'source_nodes'):
            for node in response.source_nodes:
                sources.append({
                    "content": node.node.text,
                    "metadata": node.node.metadata,
                    "score": getattr(node, 'score', 0.0)
                })
        
        return {
            "answer": str(response),
            "sources": sources,
            "strategy_details": "使用详细检索策略，包含更多上下文"
        }
    
    def _comparative_query(self, query_text: str) -> Dict[str, Any]:
        """比较查询策略"""
        # 分解比较查询
        comparison_aspects = self._extract_comparison_aspects(query_text)
        
        results = {}
        all_sources = []
        
        for aspect in comparison_aspects:
            aspect_query = f"{query_text} {aspect}"
            query_engine = self.index.as_query_engine(similarity_top_k=5)
            response = query_engine.query(aspect_query)
            
            results[aspect] = str(response)
            
            if hasattr(response, 'source_nodes'):
                for node in response.source_nodes:
                    all_sources.append({
                        "content": node.node.text[:200] + "...",
                        "metadata": node.node.metadata,
                        "score": getattr(node, 'score', 0.0),
                        "aspect": aspect
                    })
        
        # 合成比较结果
        comparison_summary = self._synthesize_comparison(query_text, results)
        
        return {
            "answer": comparison_summary,
            "sources": all_sources,
            "comparison_aspects": comparison_aspects,
            "detailed_results": results,
            "strategy_details": "使用比较分析策略"
        }
    
    def _analytical_query(self, query_text: str) -> Dict[str, Any]:
        """分析查询策略"""
        # 多步分析
        analysis_steps = [
            "背景信息",
            "关键因素",
            "影响分析",
            "结论总结"
        ]
        
        step_results = {}
        all_sources = []
        
        for step in analysis_steps:
            step_query = f"{query_text} {step}"
            query_engine = self.index.as_query_engine(similarity_top_k=4)
            response = query_engine.query(step_query)
            
            step_results[step] = str(response)
            
            if hasattr(response, 'source_nodes'):
                for node in response.source_nodes:
                    all_sources.append({
                        "content": node.node.text[:200] + "...",
                        "metadata": node.node.metadata,
                        "score": getattr(node, 'score', 0.0),
                        "analysis_step": step
                    })
        
        # 合成分析结果
        analysis_summary = self._synthesize_analysis(query_text, step_results)
        
        return {
            "answer": analysis_summary,
            "sources": all_sources,
            "analysis_steps": step_results,
            "strategy_details": "使用多步分析策略"
        }
    
    def _extract_comparison_aspects(self, query_text: str) -> List[str]:
        """提取比较方面"""
        # 简化实现
        default_aspects = ["特点", "优势", "劣势", "应用场景"]
        return default_aspects
    
    def _synthesize_comparison(self, query_text: str, results: Dict[str, str]) -> str:
        """合成比较结果"""
        synthesis = f"关于 '{query_text}' 的比较分析：\n\n"
        
        for aspect, result in results.items():
            synthesis += f"**{aspect}**：\n{result}\n\n"
        
        synthesis += "综合来看，各方面都有其特点和适用场景。"
        
        return synthesis
    
    def _synthesize_analysis(self, query_text: str, step_results: Dict[str, str]) -> str:
        """合成分析结果"""
        synthesis = f"关于 '{query_text}' 的深度分析：\n\n"
        
        for step, result in step_results.items():
            synthesis += f"**{step}**：\n{result}\n\n"
        
        synthesis += "通过多维度分析，我们可以得出全面的理解。"
        
        return synthesis
    
    def _update_performance_metrics(self, strategy: str, processing_time: float, success: bool):
        """更新性能指标"""
        if strategy not in self.performance_metrics:
            self.performance_metrics[strategy] = {
                "total_queries": 0,
                "successful_queries": 0,
                "total_time": 0.0,
                "avg_time": 0.0,
                "success_rate": 0.0
            }
        
        metrics = self.performance_metrics[strategy]
        metrics["total_queries"] += 1
        metrics["total_time"] += processing_time
        
        if success:
            metrics["successful_queries"] += 1
        
        metrics["avg_time"] = metrics["total_time"] / metrics["total_queries"]
        metrics["success_rate"] = metrics["successful_queries"] / metrics["total_queries"]
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        return {
            "total_queries": len(self.query_history),
            "strategy_metrics": self.performance_metrics,
            "recent_queries": self.query_history[-10:] if self.query_history else []
        }

# ============================================================================
# 3. 高级文档处理
# ============================================================================

class AdvancedDocumentProcessor:
    """高级文档处理器"""
    
    def __init__(self, chunk_size: int = 1024, chunk_overlap: int = 200):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        
        # 节点解析器
        self.node_parser = SimpleNodeParser.from_defaults(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap
        )
        
        # 句子窗口解析器
        self.sentence_window_parser = SentenceWindowNodeParser.from_defaults(
            window_size=3,
            window_metadata_key="window",
            original_text_metadata_key="original_text"
        )
        
        # 元数据提取器
        self.extractors = [
            TitleExtractor(nodes=5),
            KeywordExtractor(keywords=10),
            SummaryExtractor(summaries=["prev", "self"])
        ]
    
    def process_documents(self, documents: List[Document], 
                         use_sentence_window: bool = False,
                         extract_metadata: bool = True) -> List:
        """处理文档"""
        # 选择解析器
        parser = self.sentence_window_parser if use_sentence_window else self.node_parser
        
        # 解析文档为节点
        nodes = parser.get_nodes_from_documents(documents)
        
        # 提取元数据
        if extract_metadata:
            for extractor in self.extractors:
                try:
                    nodes = extractor.extract(nodes)
                except Exception as e:
                    print(f"元数据提取失败 {extractor.__class__.__name__}: {e}")
        
        return nodes
    
    def create_hierarchical_nodes(self, documents: List[Document]) -> List:
        """创建分层节点"""
        from llama_index.core.node_parser import HierarchicalNodeParser
        
        hierarchical_parser = HierarchicalNodeParser.from_defaults(
            chunk_sizes=[2048, 512, 128]
        )
        
        nodes = hierarchical_parser.get_nodes_from_documents(documents)
        return nodes
    
    def analyze_document_structure(self, document: Document) -> Dict[str, Any]:
        """分析文档结构"""
        text = document.text
        
        analysis = {
            "total_length": len(text),
            "word_count": len(text.split()),
            "paragraph_count": len(text.split('\n\n')),
            "sentence_count": len([s for s in text.split('.') if s.strip()]),
            "avg_sentence_length": 0,
            "complexity_score": 0
        }
        
        sentences = [s.strip() for s in text.split('.') if s.strip()]
        if sentences:
            analysis["avg_sentence_length"] = sum(len(s.split()) for s in sentences) / len(sentences)
        
        # 简单的复杂度评分
        analysis["complexity_score"] = min(analysis["avg_sentence_length"] / 10, 1.0)
        
        return analysis

# ============================================================================
# 4. 使用示例
# ============================================================================

async def demo_advanced_llamaindex():
    """演示LlamaIndex高级功能"""
    print("=== LlamaIndex高级功能演示 ===")
    
    # 创建示例文档
    documents = [
        Document(text="人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。AI包括机器学习、深度学习、自然语言处理等多个子领域。"),
        Document(text="机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习和改进。常见的机器学习算法包括线性回归、决策树、神经网络等。"),
        Document(text="深度学习是机器学习的一个分支，它使用神经网络来模拟人脑的学习过程。深度学习在图像识别、语音识别、自然语言处理等领域取得了突破性进展。"),
        Document(text="自然语言处理（NLP）是人工智能的一个重要分支，专注于使计算机能够理解、解释和生成人类语言。NLP技术广泛应用于搜索引擎、聊天机器人、翻译系统等。")
    ]
    
    # 1. 演示混合索引
    print("\n1. 混合索引演示")
    try:
        hybrid_index = HybridIndex(documents)
        hybrid_retriever = hybrid_index.as_retriever(similarity_top_k=3)
        
        query = QueryBundle(query_str="什么是机器学习？")
        results = hybrid_retriever.retrieve(query)
        
        print(f"查询: {query.query_str}")
        print(f"找到 {len(results)} 个相关结果:")
        for i, result in enumerate(results):
            print(f"  {i+1}. 分数: {result.score:.3f}")
            print(f"     内容: {result.node.text[:100]}...")
    
    except Exception as e:
        print(f"混合索引演示失败: {e}")
    
    # 2. 演示自适应查询引擎
    print("\n2. 自适应查询引擎演示")
    try:
        # 创建简单索引用于演示
        simple_index = VectorStoreIndex.from_documents(documents)
        adaptive_engine = AdaptiveQueryEngine(simple_index)
        
        test_queries = [
            "什么是人工智能？",
            "详细解释机器学习的概念",
            "比较机器学习和深度学习的区别",
            "分析人工智能的发展趋势"
        ]
        
        for query in test_queries:
            result = adaptive_engine.query(query)
            print(f"\n查询: {query}")
            print(f"策略: {result['strategy_used']}")
            print(f"处理时间: {result['processing_time']:.3f}秒")
            print(f"回答: {result['answer'][:150]}...")
        
        # 显示性能报告
        performance_report = adaptive_engine.get_performance_report()
        print(f"\n性能报告:")
        print(f"总查询数: {performance_report['total_queries']}")
        for strategy, metrics in performance_report['strategy_metrics'].items():
            print(f"  {strategy}: 成功率 {metrics['success_rate']:.1%}, 平均时间 {metrics['avg_time']:.3f}秒")
    
    except Exception as e:
        print(f"自适应查询引擎演示失败: {e}")
    
    # 3. 演示高级文档处理
    print("\n3. 高级文档处理演示")
    try:
        processor = AdvancedDocumentProcessor()
        
        # 处理文档
        nodes = processor.process_documents(documents, extract_metadata=False)
        print(f"处理后生成 {len(nodes)} 个节点")
        
        # 分析文档结构
        for i, doc in enumerate(documents[:2]):  # 只分析前两个文档
            analysis = processor.analyze_document_structure(doc)
            print(f"\n文档 {i+1} 结构分析:")
            print(f"  字数: {analysis['word_count']}")
            print(f"  句子数: {analysis['sentence_count']}")
            print(f"  平均句长: {analysis['avg_sentence_length']:.1f}")
            print(f"  复杂度: {analysis['complexity_score']:.2f}")
    
    except Exception as e:
        print(f"高级文档处理演示失败: {e}")
    
    print("\n演示完成！")

if __name__ == "__main__":
    asyncio.run(demo_advanced_llamaindex())
