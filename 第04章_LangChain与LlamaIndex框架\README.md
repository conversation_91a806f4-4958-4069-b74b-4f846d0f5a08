# 第04章：大型语言模型开发框架 - LangChain 与 LlamaIndex

## 章节概述

本章节将深入介绍两个最重要的 LLM 应用开发框架：LangChain 和 LlamaIndex。学员将学习如何使用这些框架快速构建复杂的 LLM 应用，包括文档问答、代理系统、工作流编排等。通过对比学习，掌握不同框架的特点和适用场景。

## 学习目标

完成本章节学习后，学员将能够：

1. **掌握 LangChain 核心概念**：理解链、代理、记忆等核心组件
2. **熟练使用 LlamaIndex**：掌握文档索引、查询引擎等功能
3. **构建复杂应用**：使用框架快速开发 LLM 应用
4. **选择合适框架**：根据需求选择最适合的开发框架
5. **优化应用性能**：掌握框架的高级特性和优化技巧

## 章节结构

### 4.1 LangChain 框架深度解析
**时长：120分钟**

#### 理论内容
- **LangChain 核心概念**
  - 链（Chains）：组合多个组件的工作流
  - 代理（Agents）：能够使用工具的智能体
  - 记忆（Memory）：对话历史管理
  - 工具（Tools）：外部功能集成
  - 提示模板（Prompt Templates）：结构化提示

- **核心组件详解**
  - LLM 包装器：统一不同模型接口
  - 输出解析器：结构化输出处理
  - 文档加载器：多种数据源支持
  - 向量存储：嵌入向量管理
  - 检索器：信息检索接口

- **链的类型与应用**
  - LLMChain：基础 LLM 调用链
  - SequentialChain：顺序执行链
  - RouterChain：条件分支链
  - MapReduceChain：并行处理链

#### 视觉化学习辅助
- **LangChain 架构图**：展示框架的整体结构
- **组件关系图**：各组件之间的依赖关系
- **数据流图**：数据在不同组件间的流转
- **链类型对比表**：不同链的特点和适用场景

#### 实践操作
```python
# LangChain 基础使用示例
from langchain.llms import OpenAI
from langchain.prompts import PromptTemplate
from langchain.chains import LLMChain
from langchain.memory import ConversationBufferMemory
from langchain.agents import initialize_agent, Tool
from langchain.agents import AgentType

# 1. 基础 LLM 链
llm = OpenAI(temperature=0.7)

prompt = PromptTemplate(
    input_variables=["product"],
    template="为{product}写一个创意广告文案"
)

chain = LLMChain(llm=llm, prompt=prompt)
result = chain.run("智能手表")
print(f"广告文案: {result}")

# 2. 带记忆的对话链
memory = ConversationBufferMemory()

conversation_prompt = PromptTemplate(
    input_variables=["history", "input"],
    template="""以下是人类和AI的对话历史：
{history}

人类: {input}
AI:"""
)

conversation_chain = LLMChain(
    llm=llm,
    prompt=conversation_prompt,
    memory=memory,
    verbose=True
)

# 3. 自定义工具和代理
def calculate_age(birth_year):
    """计算年龄的工具"""
    from datetime import datetime
    current_year = datetime.now().year
    return current_year - int(birth_year)

def search_weather(city):
    """查询天气的工具（模拟）"""
    return f"{city}今天天气晴朗，温度25°C"

tools = [
    Tool(
        name="计算年龄",
        func=calculate_age,
        description="根据出生年份计算年龄，输入格式：YYYY"
    ),
    Tool(
        name="查询天气",
        func=search_weather,
        description="查询指定城市的天气情况"
    )
]

agent = initialize_agent(
    tools=tools,
    llm=llm,
    agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION,
    verbose=True
)

# 使用代理
response = agent.run("我1990年出生，今年多少岁了？")
print(response)
```

### 4.2 LlamaIndex 数据处理与检索
**时长：120分钟**

#### 理论内容
- **LlamaIndex 核心理念**
  - 数据连接器（Data Connectors）
  - 数据索引（Data Indexes）
  - 查询引擎（Query Engines）
  - 聊天引擎（Chat Engines）

- **索引类型详解**
  - 向量存储索引（VectorStoreIndex）
  - 列表索引（ListIndex）
  - 树索引（TreeIndex）
  - 关键词表索引（KeywordTableIndex）

- **查询策略**
  - 相似性搜索
  - 混合搜索
  - 多步查询
  - 子问题查询

#### 视觉化学习辅助
- **LlamaIndex 工作流程图**：从数据加载到查询响应的完整流程
- **索引类型对比图**：不同索引的结构和特点
- **查询策略示意图**：各种查询方法的工作原理
- **性能对比图表**：不同配置下的查询性能

#### 实践操作
```python
# LlamaIndex 基础使用示例
from llama_index import VectorStoreIndex, SimpleDirectoryReader
from llama_index import ServiceContext, LLMPredictor
from llama_index.llms import OpenAI
from llama_index.embeddings import OpenAIEmbedding
from llama_index.node_parser import SimpleNodeParser
from llama_index.text_splitter import TokenTextSplitter

# 1. 配置服务上下文
llm = OpenAI(model="gpt-3.5-turbo", temperature=0.1)
embed_model = OpenAIEmbedding()

service_context = ServiceContext.from_defaults(
    llm=llm,
    embed_model=embed_model,
    node_parser=SimpleNodeParser.from_defaults(
        text_splitter=TokenTextSplitter(chunk_size=512, chunk_overlap=50)
    )
)

# 2. 加载和索引文档
documents = SimpleDirectoryReader("./data").load_data()
index = VectorStoreIndex.from_documents(
    documents, 
    service_context=service_context
)

# 3. 创建查询引擎
query_engine = index.as_query_engine(
    similarity_top_k=3,
    response_mode="compact"
)

# 4. 执行查询
response = query_engine.query("什么是机器学习？")
print(f"回答: {response}")

# 5. 创建聊天引擎
chat_engine = index.as_chat_engine(
    chat_mode="condense_question",
    verbose=True
)

# 6. 多轮对话
chat_response = chat_engine.chat("机器学习有哪些类型？")
print(f"聊天回答: {chat_response}")

# 7. 自定义查询处理
from llama_index.query_engine import SubQuestionQueryEngine
from llama_index.tools import QueryEngineTool, ToolMetadata

# 创建子问题查询引擎
query_engine_tools = [
    QueryEngineTool(
        query_engine=query_engine,
        metadata=ToolMetadata(
            name="machine_learning_docs",
            description="包含机器学习相关文档的查询引擎"
        )
    )
]

sub_question_engine = SubQuestionQueryEngine.from_defaults(
    query_engine_tools=query_engine_tools,
    service_context=service_context
)

# 复杂查询
complex_response = sub_question_engine.query(
    "比较监督学习和无监督学习的优缺点"
)
print(f"复杂查询回答: {complex_response}")
```

### 4.3 框架对比与选择策略
**时长：90分钟**

#### 理论内容
- **LangChain vs LlamaIndex**
  - 设计理念差异
  - 功能特点对比
  - 性能表现分析
  - 生态系统比较

- **适用场景分析**
  - LangChain 适用场景：
    - 复杂工作流编排
    - 多工具集成
    - 代理系统开发
    - 对话应用构建

  - LlamaIndex 适用场景：
    - 文档问答系统
    - 知识库检索
    - RAG 应用开发
    - 数据分析助手

- **混合使用策略**
  - 优势互补
  - 架构设计
  - 性能优化
  - 维护考虑

#### 视觉化学习辅助
- **功能对比矩阵**：两个框架的功能特性对比
- **性能基准测试**：不同任务下的性能表现
- **架构对比图**：两个框架的设计架构差异
- **选择决策树**：根据需求选择框架的决策流程

#### 实践操作
```python
# 框架集成使用示例
from langchain.agents import Tool
from langchain.agents import initialize_agent, AgentType
from langchain.llms import OpenAI
from llama_index import VectorStoreIndex, SimpleDirectoryReader

class HybridFrameworkApp:
    def __init__(self):
        self.llm = OpenAI(temperature=0)
        
        # 初始化 LlamaIndex
        documents = SimpleDirectoryReader("./knowledge_base").load_data()
        self.index = VectorStoreIndex.from_documents(documents)
        self.query_engine = self.index.as_query_engine()
        
        # 创建工具
        self.tools = [
            Tool(
                name="知识库查询",
                func=self._query_knowledge_base,
                description="查询知识库中的信息，适用于事实性问题"
            ),
            Tool(
                name="计算器",
                func=self._calculate,
                description="执行数学计算"
            )
        ]
        
        # 初始化 LangChain 代理
        self.agent = initialize_agent(
            self.tools,
            self.llm,
            agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION,
            verbose=True
        )
    
    def _query_knowledge_base(self, query: str) -> str:
        """使用 LlamaIndex 查询知识库"""
        response = self.query_engine.query(query)
        return str(response)
    
    def _calculate(self, expression: str) -> str:
        """安全的数学计算"""
        try:
            # 简单的计算器实现
            result = eval(expression)
            return f"计算结果: {result}"
        except:
            return "计算错误，请检查表达式"
    
    def chat(self, message: str) -> str:
        """统一的聊天接口"""
        return self.agent.run(message)

# 使用示例
app = HybridFrameworkApp()
response = app.chat("根据知识库，机器学习的准确率如何计算？请给出公式并计算一个例子")
print(response)
```

### 4.4 高级特性与性能优化
**时长：90分钟**

#### 理论内容
- **高级特性**
  - 自定义组件开发
  - 插件系统使用
  - 异步处理支持
  - 流式响应实现

- **性能优化策略**
  - 缓存机制设计
  - 批量处理优化
  - 内存管理
  - 并发控制

- **生产环境部署**
  - 容器化部署
  - 负载均衡
  - 监控和日志
  - 错误处理

#### 实践操作
```python
# 高级特性使用示例
import asyncio
from langchain.cache import InMemoryCache
from langchain.globals import set_llm_cache
from langchain.callbacks import StreamingStdOutCallbackHandler

# 1. 启用缓存
set_llm_cache(InMemoryCache())

# 2. 流式响应
class CustomStreamingHandler(StreamingStdOutCallbackHandler):
    def on_llm_new_token(self, token: str, **kwargs) -> None:
        print(f"Token: {token}", end="", flush=True)

# 3. 异步处理
async def async_chain_processing():
    from langchain.llms import OpenAI
    from langchain.chains import LLMChain
    from langchain.prompts import PromptTemplate
    
    llm = OpenAI(
        streaming=True,
        callbacks=[CustomStreamingHandler()]
    )
    
    prompt = PromptTemplate(
        input_variables=["topic"],
        template="写一篇关于{topic}的简短文章"
    )
    
    chain = LLMChain(llm=llm, prompt=prompt)
    
    # 并发处理多个请求
    tasks = [
        chain.arun(topic="人工智能"),
        chain.arun(topic="机器学习"),
        chain.arun(topic="深度学习")
    ]
    
    results = await asyncio.gather(*tasks)
    return results

# 4. 自定义组件
from langchain.schema import BaseRetriever, Document
from typing import List

class CustomRetriever(BaseRetriever):
    def __init__(self, documents: List[Document]):
        self.documents = documents
    
    def get_relevant_documents(self, query: str) -> List[Document]:
        # 自定义检索逻辑
        relevant_docs = []
        for doc in self.documents:
            if query.lower() in doc.page_content.lower():
                relevant_docs.append(doc)
        return relevant_docs[:3]  # 返回前3个相关文档

# 5. 性能监控
from langchain.callbacks import get_openai_callback

def monitor_token_usage():
    with get_openai_callback() as cb:
        # 执行 LLM 操作
        llm = OpenAI()
        result = llm("解释什么是机器学习")
        
        print(f"总 tokens: {cb.total_tokens}")
        print(f"提示 tokens: {cb.prompt_tokens}")
        print(f"完成 tokens: {cb.completion_tokens}")
        print(f"总成本: ${cb.total_cost}")
        
        return result, cb
```

## 重点知识点

### 框架核心概念
1. **LangChain 核心**
   - 链式编程思想
   - 组件化设计
   - 代理模式应用
   - 记忆管理机制

2. **LlamaIndex 核心**
   - 数据索引策略
   - 查询优化技术
   - 嵌入向量管理
   - 多模态数据处理

### 最佳实践
1. **架构设计**
   - 模块化设计原则
   - 可扩展性考虑
   - 错误处理策略
   - 性能监控方案

2. **开发技巧**
   - 提示工程优化
   - 缓存策略设计
   - 异步处理实现
   - 资源管理优化

## 案例研究

### 案例1：企业知识管理系统
**需求分析**：
- 多源数据整合
- 智能问答功能
- 权限控制
- 使用分析

**技术选型**：
- LlamaIndex：文档索引和检索
- LangChain：工作流编排和权限控制
- 向量数据库：Pinecone
- 前端：Streamlit

**实现要点**：
- 文档预处理和分块
- 多级索引构建
- 权限过滤机制
- 查询结果排序

### 案例2：智能客服升级
**背景**：传统客服系统的 AI 化改造

**技术架构**：
- LangChain：对话管理和工具集成
- LlamaIndex：知识库检索
- 外部 API：订单查询、物流跟踪
- 数据库：用户画像和对话历史

**关键功能**：
- 意图识别和槽位填充
- 多轮对话管理
- 知识库智能检索
- 人工转接机制

## 练习项目

### 项目1：个人知识助手
**目标**：构建个人文档管理和问答系统

**功能要求**：
- 支持多种文档格式
- 智能分类和标签
- 自然语言查询
- 知识图谱可视化

**技术栈**：
- LlamaIndex：文档处理和索引
- LangChain：查询处理和对话
- Streamlit：用户界面
- Neo4j：知识图谱存储

### 项目2：智能研究助手
**目标**：帮助研究人员进行文献调研和分析

**功能要求**：
- 论文自动摘要
- 相关文献推荐
- 研究趋势分析
- 引用网络分析

**技术要点**：
- 学术数据源集成
- 多语言文本处理
- 时间序列分析
- 网络图分析

## 参考资料

### 官方文档
1. [LangChain Documentation](https://python.langchain.com/)
2. [LlamaIndex Documentation](https://docs.llamaindex.ai/)
3. [LangSmith](https://smith.langchain.com/) - LangChain 调试平台
4. [LlamaHub](https://llamahub.ai/) - LlamaIndex 数据连接器

### 社区资源
1. **GitHub 仓库**
   - LangChain Examples
   - LlamaIndex Tutorials
   - Community Templates

2. **技术博客**
   - LangChain Blog
   - LlamaIndex Blog
   - 开发者经验分享

### 学习资源
1. **在线课程**
   - LangChain 官方教程
   - YouTube 技术讲解
   - Coursera 相关课程

2. **书籍推荐**
   - "Building LLM Applications with LangChain"
   - "RAG Systems with LlamaIndex"

## 常见问题解答

**Q1：LangChain 和 LlamaIndex 可以一起使用吗？**
A1：可以，两个框架可以很好地互补。通常用 LlamaIndex 处理文档检索，用 LangChain 处理工作流编排。

**Q2：如何选择合适的索引类型？**
A2：根据数据特点和查询需求选择：向量索引适合语义搜索，关键词索引适合精确匹配，树索引适合层次化数据。

**Q3：如何优化框架的性能？**
A3：使用缓存、批量处理、异步操作、合理的分块策略，以及选择合适的嵌入模型和向量数据库。

---

**下一章预告**：第05章将深入学习 Embeddings 与向量数据库技术，掌握语义搜索和相似度计算的核心技术。
