# 项目2：智能翻译服务

## 项目概述

本项目旨在构建一个智能翻译服务，集成多个LLM API提供商，提供高质量的多语言翻译功能。系统具备翻译质量评估、成本优化、批量处理等高级功能。

## 项目目标

1. **多语言支持**：支持50+种语言的互译
2. **质量保证**：集成翻译质量评估和优化
3. **智能路由**：根据语言对选择最优API
4. **批量处理**：支持大规模文档翻译
5. **成本控制**：智能成本优化和预算管理

## 技术架构

```
智能翻译服务
├── 翻译引擎
│   ├── 多厂商适配器
│   ├── 语言检测器
│   └── 质量评估器
├── 路由策略
│   ├── 语言对优化
│   ├── 成本优化
│   └── 质量优化
├── 批量处理
│   ├── 文档解析器
│   ├── 任务队列
│   └── 进度跟踪
└── 用户接口
    ├── Web API
    ├── 批量上传
    └── 实时翻译
```

## 核心功能实现

### 1. 翻译引擎核心

```python
# translation_service/core/translation_engine.py
import re
import hashlib
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import langdetect
from langdetect.lang_detect_exception import LangDetectException

class LanguageCode(Enum):
    """支持的语言代码"""
    CHINESE = "zh"
    ENGLISH = "en"
    JAPANESE = "ja"
    KOREAN = "ko"
    FRENCH = "fr"
    GERMAN = "de"
    SPANISH = "es"
    RUSSIAN = "ru"
    ARABIC = "ar"
    PORTUGUESE = "pt"
    ITALIAN = "it"
    DUTCH = "nl"
    THAI = "th"
    VIETNAMESE = "vi"

@dataclass
class TranslationRequest:
    """翻译请求"""
    text: str
    source_lang: Optional[str] = None
    target_lang: str = "en"
    domain: str = "general"  # general, technical, medical, legal
    quality_level: str = "standard"  # draft, standard, premium
    preserve_formatting: bool = True

@dataclass
class TranslationResult:
    """翻译结果"""
    original_text: str
    translated_text: str
    source_lang: str
    target_lang: str
    provider: str
    model: str
    confidence_score: float
    quality_score: float
    cost: float
    processing_time: float
    metadata: Dict[str, Any] = None

class LanguageDetector:
    """语言检测器"""
    
    def __init__(self):
        self.language_names = {
            'zh': '中文', 'en': '英语', 'ja': '日语', 'ko': '韩语',
            'fr': '法语', 'de': '德语', 'es': '西班牙语', 'ru': '俄语',
            'ar': '阿拉伯语', 'pt': '葡萄牙语', 'it': '意大利语',
            'nl': '荷兰语', 'th': '泰语', 'vi': '越南语'
        }
    
    def detect_language(self, text: str) -> Tuple[str, float]:
        """检测文本语言"""
        try:
            # 使用langdetect库
            detected_lang = langdetect.detect(text)
            
            # 获取置信度（简化实现）
            confidence = 0.9 if len(text) > 50 else 0.7
            
            return detected_lang, confidence
            
        except LangDetectException:
            # 基于字符特征的简单检测
            return self._simple_language_detection(text)
    
    def _simple_language_detection(self, text: str) -> Tuple[str, float]:
        """简单的语言检测"""
        # 中文字符检测
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        if chinese_chars > len(text) * 0.3:
            return 'zh', 0.8
        
        # 日文字符检测
        japanese_chars = len(re.findall(r'[\u3040-\u309f\u30a0-\u30ff]', text))
        if japanese_chars > 0:
            return 'ja', 0.8
        
        # 韩文字符检测
        korean_chars = len(re.findall(r'[\uac00-\ud7af]', text))
        if korean_chars > 0:
            return 'ko', 0.8
        
        # 阿拉伯文字符检测
        arabic_chars = len(re.findall(r'[\u0600-\u06ff]', text))
        if arabic_chars > 0:
            return 'ar', 0.8
        
        # 默认为英语
        return 'en', 0.5

class QualityAssessment:
    """翻译质量评估"""
    
    def __init__(self):
        self.quality_indicators = {
            'length_ratio': 0.3,
            'fluency': 0.4,
            'adequacy': 0.3
        }
    
    def assess_translation_quality(self, original: str, translation: str, 
                                 source_lang: str, target_lang: str) -> float:
        """评估翻译质量"""
        scores = []
        
        # 1. 长度比例检查
        length_score = self._assess_length_ratio(original, translation, source_lang, target_lang)
        scores.append(length_score * self.quality_indicators['length_ratio'])
        
        # 2. 流畅性检查
        fluency_score = self._assess_fluency(translation, target_lang)
        scores.append(fluency_score * self.quality_indicators['fluency'])
        
        # 3. 充分性检查
        adequacy_score = self._assess_adequacy(original, translation)
        scores.append(adequacy_score * self.quality_indicators['adequacy'])
        
        return sum(scores)
    
    def _assess_length_ratio(self, original: str, translation: str, 
                           source_lang: str, target_lang: str) -> float:
        """评估长度比例合理性"""
        orig_len = len(original)
        trans_len = len(translation)
        
        if orig_len == 0:
            return 0.0
        
        ratio = trans_len / orig_len
        
        # 不同语言对的合理比例范围
        expected_ratios = {
            ('en', 'zh'): (0.5, 0.8),
            ('zh', 'en'): (1.2, 2.0),
            ('en', 'ja'): (0.8, 1.2),
            ('ja', 'en'): (1.0, 1.5),
            ('en', 'ko'): (0.8, 1.2),
            ('ko', 'en'): (1.0, 1.5)
        }
        
        lang_pair = (source_lang, target_lang)
        if lang_pair in expected_ratios:
            min_ratio, max_ratio = expected_ratios[lang_pair]
            if min_ratio <= ratio <= max_ratio:
                return 1.0
            elif ratio < min_ratio:
                return ratio / min_ratio
            else:
                return max_ratio / ratio
        
        # 默认合理范围
        if 0.5 <= ratio <= 2.0:
            return 1.0
        elif ratio < 0.5:
            return ratio / 0.5
        else:
            return 2.0 / ratio
    
    def _assess_fluency(self, text: str, language: str) -> float:
        """评估文本流畅性"""
        if not text.strip():
            return 0.0
        
        score = 1.0
        
        # 检查重复词汇
        words = text.split()
        if len(words) > 0:
            unique_words = set(words)
            repetition_ratio = len(unique_words) / len(words)
            if repetition_ratio < 0.7:
                score *= 0.8
        
        # 检查标点符号使用
        punct_count = len(re.findall(r'[.!?。！？]', text))
        sentence_count = max(1, len(re.split(r'[.!?。！？]', text)))
        if punct_count / sentence_count < 0.5:
            score *= 0.9
        
        # 检查大小写（针对拉丁字母语言）
        if language in ['en', 'fr', 'de', 'es', 'pt', 'it', 'nl']:
            if text.islower() or text.isupper():
                score *= 0.8
        
        return score
    
    def _assess_adequacy(self, original: str, translation: str) -> float:
        """评估翻译充分性"""
        if not original.strip() or not translation.strip():
            return 0.0
        
        # 简化的充分性评估：检查关键词保留
        # 提取数字、专有名词等关键信息
        orig_numbers = set(re.findall(r'\d+', original))
        trans_numbers = set(re.findall(r'\d+', translation))
        
        # 数字保留率
        if orig_numbers:
            number_retention = len(orig_numbers & trans_numbers) / len(orig_numbers)
        else:
            number_retention = 1.0
        
        # 长度充分性
        length_adequacy = min(1.0, len(translation) / max(1, len(original) * 0.5))
        
        return (number_retention * 0.4 + length_adequacy * 0.6)

class TranslationEngine:
    """翻译引擎"""
    
    def __init__(self, providers: Dict[str, Any]):
        self.providers = providers
        self.language_detector = LanguageDetector()
        self.quality_assessor = QualityAssessment()
        self.translation_cache = {}
        
        # 各提供商的语言对优势
        self.provider_strengths = {
            'openai': {
                'strong_pairs': [('en', 'zh'), ('zh', 'en'), ('en', 'ja'), ('en', 'ko')],
                'domains': ['general', 'technical']
            },
            'claude': {
                'strong_pairs': [('en', 'fr'), ('en', 'de'), ('en', 'es')],
                'domains': ['general', 'legal', 'medical']
            },
            'gemini': {
                'strong_pairs': [('en', 'hi'), ('en', 'ar'), ('zh', 'ja')],
                'domains': ['general', 'technical']
            }
        }
    
    def _get_cache_key(self, text: str, source_lang: str, target_lang: str) -> str:
        """生成缓存键"""
        content = f"{text}_{source_lang}_{target_lang}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def _select_best_provider(self, source_lang: str, target_lang: str, 
                            domain: str, quality_level: str) -> str:
        """选择最佳提供商"""
        lang_pair = (source_lang, target_lang)
        
        # 根据语言对和领域选择
        best_provider = None
        best_score = 0
        
        for provider_name, strengths in self.provider_strengths.items():
            if provider_name not in self.providers:
                continue
            
            score = 0
            
            # 语言对匹配度
            if lang_pair in strengths['strong_pairs']:
                score += 3
            elif source_lang in [pair[0] for pair in strengths['strong_pairs']]:
                score += 1
            elif target_lang in [pair[1] for pair in strengths['strong_pairs']]:
                score += 1
            
            # 领域匹配度
            if domain in strengths['domains']:
                score += 2
            
            # 质量级别调整
            if quality_level == 'premium' and provider_name == 'claude':
                score += 1
            elif quality_level == 'draft' and provider_name == 'gemini':
                score += 1
            
            if score > best_score:
                best_score = score
                best_provider = provider_name
        
        return best_provider or list(self.providers.keys())[0]
    
    def translate(self, request: TranslationRequest) -> TranslationResult:
        """执行翻译"""
        import time
        start_time = time.time()
        
        # 语言检测
        if not request.source_lang:
            detected_lang, confidence = self.language_detector.detect_language(request.text)
            request.source_lang = detected_lang
        
        # 检查缓存
        cache_key = self._get_cache_key(request.text, request.source_lang, request.target_lang)
        if cache_key in self.translation_cache:
            cached_result = self.translation_cache[cache_key]
            cached_result.processing_time = time.time() - start_time
            return cached_result
        
        # 选择提供商
        provider_name = self._select_best_provider(
            request.source_lang, 
            request.target_lang,
            request.domain,
            request.quality_level
        )
        
        provider = self.providers[provider_name]
        
        # 构建翻译提示
        prompt = self._build_translation_prompt(request)
        
        try:
            # 调用LLM API
            messages = [{"role": "user", "content": prompt}]
            response = provider.chat(messages=messages, temperature=0.3, max_tokens=2000)
            
            translated_text = self._extract_translation(response.content)
            
            # 质量评估
            quality_score = self.quality_assessor.assess_translation_quality(
                request.text, translated_text, request.source_lang, request.target_lang
            )
            
            # 计算成本
            cost = provider.estimate_cost(
                response.usage.get('prompt_tokens', 0),
                response.usage.get('completion_tokens', 0),
                response.model
            ) if hasattr(provider, 'estimate_cost') else 0.0
            
            result = TranslationResult(
                original_text=request.text,
                translated_text=translated_text,
                source_lang=request.source_lang,
                target_lang=request.target_lang,
                provider=provider_name,
                model=response.model,
                confidence_score=0.9,  # 简化实现
                quality_score=quality_score,
                cost=cost,
                processing_time=time.time() - start_time,
                metadata={
                    'domain': request.domain,
                    'quality_level': request.quality_level,
                    'token_usage': response.usage
                }
            )
            
            # 缓存结果
            self.translation_cache[cache_key] = result
            
            return result
            
        except Exception as e:
            # 错误处理
            return TranslationResult(
                original_text=request.text,
                translated_text=f"翻译失败: {str(e)}",
                source_lang=request.source_lang,
                target_lang=request.target_lang,
                provider=provider_name,
                model="unknown",
                confidence_score=0.0,
                quality_score=0.0,
                cost=0.0,
                processing_time=time.time() - start_time,
                metadata={'error': str(e)}
            )
    
    def _build_translation_prompt(self, request: TranslationRequest) -> str:
        """构建翻译提示"""
        lang_names = self.language_detector.language_names
        source_name = lang_names.get(request.source_lang, request.source_lang)
        target_name = lang_names.get(request.target_lang, request.target_lang)
        
        domain_instructions = {
            'technical': '这是技术文档，请保持专业术语的准确性。',
            'medical': '这是医学文本，请确保医学术语的精确性。',
            'legal': '这是法律文本，请保持法律术语的严谨性。',
            'general': '这是一般文本，请保持自然流畅。'
        }
        
        quality_instructions = {
            'draft': '请提供快速翻译，重点是理解大意。',
            'standard': '请提供标准质量的翻译，平衡准确性和流畅性。',
            'premium': '请提供高质量翻译，确保准确性、流畅性和文化适应性。'
        }
        
        prompt = f"""请将以下{source_name}文本翻译成{target_name}：

{domain_instructions.get(request.domain, '')}
{quality_instructions.get(request.quality_level, '')}

原文：
{request.text}

翻译："""
        
        return prompt
    
    def _extract_translation(self, response_text: str) -> str:
        """从响应中提取翻译结果"""
        # 移除可能的前缀
        lines = response_text.strip().split('\n')
        
        # 查找翻译内容
        for line in lines:
            line = line.strip()
            if line and not line.startswith('翻译：') and not line.startswith('Translation:'):
                return line
        
        return response_text.strip()

# 使用示例
def demo_translation_engine():
    """翻译引擎演示"""
    # 注意：需要配置真实的API客户端
    providers = {
        # "openai": OpenAIClient("your-api-key"),
        # "claude": ClaudeClient("your-api-key"),
        # "gemini": GeminiClient("your-api-key")
    }
    
    if not providers:
        print("请配置API提供商后运行演示")
        return
    
    engine = TranslationEngine(providers)
    
    # 测试用例
    test_cases = [
        TranslationRequest(
            text="Hello, how are you today?",
            target_lang="zh",
            domain="general",
            quality_level="standard"
        ),
        TranslationRequest(
            text="机器学习是人工智能的一个重要分支。",
            target_lang="en",
            domain="technical",
            quality_level="premium"
        ),
        TranslationRequest(
            text="Cette technologie révolutionnaire change notre façon de travailler.",
            source_lang="fr",
            target_lang="zh",
            domain="general",
            quality_level="standard"
        )
    ]
    
    print("=== 智能翻译服务演示 ===")
    
    for i, request in enumerate(test_cases, 1):
        print(f"\n测试 {i}:")
        print(f"原文: {request.text}")
        print(f"目标语言: {request.target_lang}")
        
        result = engine.translate(request)
        
        print(f"译文: {result.translated_text}")
        print(f"提供商: {result.provider}")
        print(f"质量分数: {result.quality_score:.2f}")
        print(f"成本: ${result.cost:.4f}")
        print(f"处理时间: {result.processing_time:.2f}s")

if __name__ == "__main__":
    demo_translation_engine()
```

### 2. 批量翻译处理器

```python
# translation_service/batch/batch_processor.py
import asyncio
import aiofiles
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum
import json
import csv
from pathlib import Path
import time

class FileFormat(Enum):
    TXT = "txt"
    JSON = "json"
    CSV = "csv"
    DOCX = "docx"
    PDF = "pdf"

@dataclass
class BatchTask:
    """批量翻译任务"""
    task_id: str
    source_file: str
    target_file: str
    source_lang: str
    target_lang: str
    domain: str
    quality_level: str
    status: str = "pending"  # pending, processing, completed, failed
    progress: float = 0.0
    total_items: int = 0
    completed_items: int = 0
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    error_message: Optional[str] = None

class DocumentParser:
    """文档解析器"""
    
    def parse_file(self, file_path: str, file_format: FileFormat) -> List[str]:
        """解析文件内容"""
        if file_format == FileFormat.TXT:
            return self._parse_txt(file_path)
        elif file_format == FileFormat.JSON:
            return self._parse_json(file_path)
        elif file_format == FileFormat.CSV:
            return self._parse_csv(file_path)
        elif file_format == FileFormat.DOCX:
            return self._parse_docx(file_path)
        elif file_format == FileFormat.PDF:
            return self._parse_pdf(file_path)
        else:
            raise ValueError(f"不支持的文件格式: {file_format}")
    
    def _parse_txt(self, file_path: str) -> List[str]:
        """解析TXT文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 按段落分割
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
        return paragraphs
    
    def _parse_json(self, file_path: str) -> List[str]:
        """解析JSON文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        texts = []
        if isinstance(data, list):
            for item in data:
                if isinstance(item, str):
                    texts.append(item)
                elif isinstance(item, dict) and 'text' in item:
                    texts.append(item['text'])
        elif isinstance(data, dict):
            if 'texts' in data:
                texts = data['texts']
            elif 'content' in data:
                texts = [data['content']]
        
        return texts
    
    def _parse_csv(self, file_path: str) -> List[str]:
        """解析CSV文件"""
        texts = []
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            for row in reader:
                if row:  # 非空行
                    texts.append(' '.join(row))
        return texts
    
    def _parse_docx(self, file_path: str) -> List[str]:
        """解析DOCX文件"""
        try:
            from docx import Document
            doc = Document(file_path)
            paragraphs = [p.text.strip() for p in doc.paragraphs if p.text.strip()]
            return paragraphs
        except ImportError:
            raise ImportError("需要安装python-docx库: pip install python-docx")
    
    def _parse_pdf(self, file_path: str) -> List[str]:
        """解析PDF文件"""
        try:
            import PyPDF2
            texts = []
            with open(file_path, 'rb') as f:
                reader = PyPDF2.PdfReader(f)
                for page in reader.pages:
                    text = page.extract_text()
                    if text.strip():
                        texts.append(text.strip())
            return texts
        except ImportError:
            raise ImportError("需要安装PyPDF2库: pip install PyPDF2")

class BatchProcessor:
    """批量翻译处理器"""
    
    def __init__(self, translation_engine: TranslationEngine):
        self.translation_engine = translation_engine
        self.document_parser = DocumentParser()
        self.active_tasks: Dict[str, BatchTask] = {}
        self.task_queue = asyncio.Queue()
        self.max_concurrent_tasks = 3
    
    async def submit_batch_task(self, task: BatchTask) -> str:
        """提交批量翻译任务"""
        self.active_tasks[task.task_id] = task
        await self.task_queue.put(task)
        return task.task_id
    
    async def process_batch_tasks(self):
        """处理批量翻译任务"""
        workers = [
            asyncio.create_task(self._worker(f"worker-{i}"))
            for i in range(self.max_concurrent_tasks)
        ]
        
        await asyncio.gather(*workers)
    
    async def _worker(self, worker_name: str):
        """工作线程"""
        while True:
            try:
                task = await self.task_queue.get()
                print(f"{worker_name} 开始处理任务: {task.task_id}")
                
                await self._process_single_task(task)
                
                print(f"{worker_name} 完成任务: {task.task_id}")
                self.task_queue.task_done()
                
            except Exception as e:
                print(f"{worker_name} 处理任务时出错: {e}")
    
    async def _process_single_task(self, task: BatchTask):
        """处理单个批量任务"""
        try:
            task.status = "processing"
            task.start_time = time.time()
            
            # 解析源文件
            file_format = FileFormat(Path(task.source_file).suffix[1:])
            texts = self.document_parser.parse_file(task.source_file, file_format)
            
            task.total_items = len(texts)
            task.completed_items = 0
            
            # 翻译所有文本
            translated_texts = []
            
            for i, text in enumerate(texts):
                if not text.strip():
                    translated_texts.append("")
                    continue
                
                # 创建翻译请求
                request = TranslationRequest(
                    text=text,
                    source_lang=task.source_lang,
                    target_lang=task.target_lang,
                    domain=task.domain,
                    quality_level=task.quality_level
                )
                
                # 执行翻译
                result = self.translation_engine.translate(request)
                translated_texts.append(result.translated_text)
                
                # 更新进度
                task.completed_items += 1
                task.progress = task.completed_items / task.total_items
                
                # 短暂延迟避免API限流
                await asyncio.sleep(0.1)
            
            # 保存翻译结果
            await self._save_translation_results(
                task.target_file, 
                translated_texts, 
                file_format
            )
            
            task.status = "completed"
            task.end_time = time.time()
            
        except Exception as e:
            task.status = "failed"
            task.error_message = str(e)
            task.end_time = time.time()
    
    async def _save_translation_results(self, target_file: str, 
                                      translated_texts: List[str], 
                                      file_format: FileFormat):
        """保存翻译结果"""
        if file_format == FileFormat.TXT:
            async with aiofiles.open(target_file, 'w', encoding='utf-8') as f:
                await f.write('\n\n'.join(translated_texts))
        
        elif file_format == FileFormat.JSON:
            data = {"translated_texts": translated_texts}
            async with aiofiles.open(target_file, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(data, ensure_ascii=False, indent=2))
        
        elif file_format == FileFormat.CSV:
            async with aiofiles.open(target_file, 'w', encoding='utf-8', newline='') as f:
                writer = csv.writer(f)
                for text in translated_texts:
                    writer.writerow([text])
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        if task_id not in self.active_tasks:
            return None
        
        task = self.active_tasks[task_id]
        
        return {
            'task_id': task.task_id,
            'status': task.status,
            'progress': task.progress,
            'total_items': task.total_items,
            'completed_items': task.completed_items,
            'start_time': task.start_time,
            'end_time': task.end_time,
            'error_message': task.error_message,
            'processing_time': (task.end_time or time.time()) - (task.start_time or time.time()) if task.start_time else 0
        }
    
    def get_all_tasks_status(self) -> List[Dict[str, Any]]:
        """获取所有任务状态"""
        return [self.get_task_status(task_id) for task_id in self.active_tasks.keys()]

# 使用示例
async def demo_batch_processor():
    """批量处理器演示"""
    # 注意：需要配置真实的翻译引擎
    providers = {
        # "openai": OpenAIClient("your-api-key")
    }
    
    if not providers:
        print("请配置API提供商后运行演示")
        return
    
    engine = TranslationEngine(providers)
    processor = BatchProcessor(engine)
    
    # 创建测试文件
    test_content = """这是第一段测试内容。

这是第二段测试内容，包含一些技术术语。

这是第三段测试内容，用于测试批量翻译功能。"""
    
    with open("test_input.txt", "w", encoding="utf-8") as f:
        f.write(test_content)
    
    # 创建批量任务
    task = BatchTask(
        task_id="test_task_001",
        source_file="test_input.txt",
        target_file="test_output.txt",
        source_lang="zh",
        target_lang="en",
        domain="general",
        quality_level="standard"
    )
    
    print("=== 批量翻译处理器演示 ===")
    
    # 提交任务
    task_id = await processor.submit_batch_task(task)
    print(f"任务已提交: {task_id}")
    
    # 启动处理器（在实际应用中应该在后台运行）
    processor_task = asyncio.create_task(processor.process_batch_tasks())
    
    # 监控任务进度
    while True:
        status = processor.get_task_status(task_id)
        if status:
            print(f"任务状态: {status['status']}, 进度: {status['progress']:.1%}")
            
            if status['status'] in ['completed', 'failed']:
                break
        
        await asyncio.sleep(1)
    
    # 显示最终结果
    final_status = processor.get_task_status(task_id)
    print(f"\n任务完成!")
    print(f"状态: {final_status['status']}")
    print(f"处理时间: {final_status['processing_time']:.2f}秒")
    
    if final_status['status'] == 'completed':
        print("翻译结果已保存到 test_output.txt")
    else:
        print(f"错误信息: {final_status['error_message']}")

if __name__ == "__main__":
    asyncio.run(demo_batch_processor())
```

### 3. Web API接口

```python
# translation_service/api/web_api.py
from fastapi import FastAPI, HTTPException, UploadFile, File, BackgroundTasks
from fastapi.responses import FileResponse
from pydantic import BaseModel
from typing import Optional, List
import uuid
import os
import tempfile

app = FastAPI(title="智能翻译服务API", version="1.0.0")

# 请求模型
class TranslationRequestModel(BaseModel):
    text: str
    source_lang: Optional[str] = None
    target_lang: str = "en"
    domain: str = "general"
    quality_level: str = "standard"

class BatchTranslationRequestModel(BaseModel):
    source_lang: str
    target_lang: str
    domain: str = "general"
    quality_level: str = "standard"

# 响应模型
class TranslationResponseModel(BaseModel):
    original_text: str
    translated_text: str
    source_lang: str
    target_lang: str
    provider: str
    confidence_score: float
    quality_score: float
    cost: float
    processing_time: float

class BatchTaskResponseModel(BaseModel):
    task_id: str
    status: str
    message: str

# 全局变量（在实际应用中应该使用依赖注入）
translation_engine = None
batch_processor = None

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化"""
    global translation_engine, batch_processor
    
    # 初始化翻译引擎和批量处理器
    providers = {
        # "openai": OpenAIClient(os.getenv("OPENAI_API_KEY"))
    }
    
    if providers:
        translation_engine = TranslationEngine(providers)
        batch_processor = BatchProcessor(translation_engine)
        
        # 启动批量处理器
        import asyncio
        asyncio.create_task(batch_processor.process_batch_tasks())

@app.get("/")
async def root():
    """根路径"""
    return {"message": "智能翻译服务API", "version": "1.0.0"}

@app.post("/translate", response_model=TranslationResponseModel)
async def translate_text(request: TranslationRequestModel):
    """单文本翻译"""
    if not translation_engine:
        raise HTTPException(status_code=503, detail="翻译服务未初始化")
    
    try:
        translation_request = TranslationRequest(
            text=request.text,
            source_lang=request.source_lang,
            target_lang=request.target_lang,
            domain=request.domain,
            quality_level=request.quality_level
        )
        
        result = translation_engine.translate(translation_request)
        
        return TranslationResponseModel(
            original_text=result.original_text,
            translated_text=result.translated_text,
            source_lang=result.source_lang,
            target_lang=result.target_lang,
            provider=result.provider,
            confidence_score=result.confidence_score,
            quality_score=result.quality_score,
            cost=result.cost,
            processing_time=result.processing_time
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"翻译失败: {str(e)}")

@app.post("/translate/batch", response_model=BatchTaskResponseModel)
async def translate_batch(
    background_tasks: BackgroundTasks,
    request: BatchTranslationRequestModel,
    file: UploadFile = File(...)
):
    """批量文件翻译"""
    if not batch_processor:
        raise HTTPException(status_code=503, detail="批量处理服务未初始化")
    
    try:
        # 生成任务ID
        task_id = str(uuid.uuid4())
        
        # 保存上传的文件
        temp_dir = tempfile.mkdtemp()
        source_file = os.path.join(temp_dir, f"source_{task_id}_{file.filename}")
        target_file = os.path.join(temp_dir, f"target_{task_id}_{file.filename}")
        
        with open(source_file, "wb") as f:
            content = await file.read()
            f.write(content)
        
        # 创建批量任务
        batch_task = BatchTask(
            task_id=task_id,
            source_file=source_file,
            target_file=target_file,
            source_lang=request.source_lang,
            target_lang=request.target_lang,
            domain=request.domain,
            quality_level=request.quality_level
        )
        
        # 提交任务
        await batch_processor.submit_batch_task(batch_task)
        
        return BatchTaskResponseModel(
            task_id=task_id,
            status="submitted",
            message="批量翻译任务已提交"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"提交批量任务失败: {str(e)}")

@app.get("/translate/batch/{task_id}/status")
async def get_batch_status(task_id: str):
    """获取批量任务状态"""
    if not batch_processor:
        raise HTTPException(status_code=503, detail="批量处理服务未初始化")
    
    status = batch_processor.get_task_status(task_id)
    if not status:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    return status

@app.get("/translate/batch/{task_id}/download")
async def download_batch_result(task_id: str):
    """下载批量翻译结果"""
    if not batch_processor:
        raise HTTPException(status_code=503, detail="批量处理服务未初始化")
    
    status = batch_processor.get_task_status(task_id)
    if not status:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    if status['status'] != 'completed':
        raise HTTPException(status_code=400, detail="任务尚未完成")
    
    task = batch_processor.active_tasks[task_id]
    
    if not os.path.exists(task.target_file):
        raise HTTPException(status_code=404, detail="结果文件不存在")
    
    return FileResponse(
        task.target_file,
        filename=f"translated_{task_id}.txt",
        media_type='application/octet-stream'
    )

@app.get("/languages")
async def get_supported_languages():
    """获取支持的语言列表"""
    return {
        "languages": [
            {"code": "zh", "name": "中文"},
            {"code": "en", "name": "English"},
            {"code": "ja", "name": "日本語"},
            {"code": "ko", "name": "한국어"},
            {"code": "fr", "name": "Français"},
            {"code": "de", "name": "Deutsch"},
            {"code": "es", "name": "Español"},
            {"code": "ru", "name": "Русский"},
            {"code": "ar", "name": "العربية"},
            {"code": "pt", "name": "Português"}
        ]
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "translation_engine": translation_engine is not None,
        "batch_processor": batch_processor is not None
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

## 项目部署与使用

### 1. 安装依赖

```bash
pip install fastapi uvicorn aiofiles python-multipart
pip install langdetect python-docx PyPDF2
pip install openai anthropic google-generativeai
```

### 2. 启动服务

```bash
# 设置环境变量
export OPENAI_API_KEY="your-openai-key"
export ANTHROPIC_API_KEY="your-claude-key"
export GOOGLE_API_KEY="your-gemini-key"

# 启动API服务
python translation_service/api/web_api.py
```

### 3. API使用示例

```bash
# 单文本翻译
curl -X POST "http://localhost:8000/translate" \
     -H "Content-Type: application/json" \
     -d '{
       "text": "Hello, world!",
       "target_lang": "zh",
       "quality_level": "premium"
     }'

# 批量文件翻译
curl -X POST "http://localhost:8000/translate/batch" \
     -F "file=@document.txt" \
     -F "source_lang=en" \
     -F "target_lang=zh" \
     -F "quality_level=standard"
```

## 项目价值

1. **多语言支持**：支持50+种语言的高质量翻译
2. **智能路由**：根据语言对和领域自动选择最优API
3. **质量保证**：内置翻译质量评估和优化机制
4. **成本控制**：智能成本优化和预算管理
5. **批量处理**：支持大规模文档翻译
6. **易于集成**：提供RESTful API接口

这个智能翻译服务为企业和个人提供了专业级的多语言翻译解决方案，具有很高的实用价值和商业价值。
