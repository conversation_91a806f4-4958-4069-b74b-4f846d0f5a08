# API调用和SDK集成最佳实践

## 概述

在实际项目中，高效、稳定、安全地调用AI模型API是成功的关键。本章将深入探讨API调用和SDK集成的最佳实践，包括错误处理、性能优化、安全策略、监控体系等核心内容。

## 核心设计原则

### 1. 可靠性优先
- **重试机制**：实现智能重试策略
- **降级处理**：提供备用方案
- **超时控制**：合理设置超时时间
- **错误恢复**：自动错误恢复机制

### 2. 性能优化
- **连接复用**：使用连接池技术
- **异步处理**：提高并发能力
- **缓存策略**：减少重复请求
- **批量处理**：优化批量操作

### 3. 安全保障
- **密钥管理**：安全的密钥存储和轮换
- **访问控制**：细粒度权限管理
- **数据保护**：敏感数据加密传输
- **审计日志**：完整的操作记录

## 高级API客户端设计

### 统一API客户端基类

```python
# api_client_base.py
import asyncio
import aiohttp
import time
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union, Callable
from dataclasses import dataclass
from enum import Enum
import json
import hashlib
from datetime import datetime, timedelta
import backoff

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RetryStrategy(Enum):
    """重试策略枚举"""
    EXPONENTIAL = "exponential"
    LINEAR = "linear"
    FIXED = "fixed"

@dataclass
class APIConfig:
    """API配置类"""
    base_url: str
    api_key: str
    timeout: int = 30
    max_retries: int = 3
    retry_strategy: RetryStrategy = RetryStrategy.EXPONENTIAL
    rate_limit: Optional[int] = None  # 每分钟请求数限制
    enable_cache: bool = True
    cache_ttl: int = 300  # 缓存TTL（秒）

@dataclass
class APIResponse:
    """API响应类"""
    success: bool
    data: Any
    error: Optional[str] = None
    status_code: Optional[int] = None
    response_time: Optional[float] = None
    cached: bool = False
    timestamp: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()

class BaseAPIClient(ABC):
    """API客户端基类"""
    
    def __init__(self, config: APIConfig):
        self.config = config
        self.session = None
        self.cache = {}
        self.rate_limiter = RateLimiter(config.rate_limit) if config.rate_limit else None
        self.metrics = APIMetrics()
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.config.timeout),
            connector=aiohttp.TCPConnector(limit=100, limit_per_host=30)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    @abstractmethod
    async def _make_request(self, method: str, endpoint: str, **kwargs) -> APIResponse:
        """抽象方法：发起API请求"""
        pass
    
    async def request(self, method: str, endpoint: str, **kwargs) -> APIResponse:
        """统一请求方法"""
        start_time = time.time()
        
        try:
            # 速率限制检查
            if self.rate_limiter:
                await self.rate_limiter.acquire()
            
            # 缓存检查
            if method.upper() == "GET" and self.config.enable_cache:
                cache_key = self._generate_cache_key(method, endpoint, kwargs)
                cached_response = self._get_cached_response(cache_key)
                if cached_response:
                    self.metrics.record_cache_hit()
                    return cached_response
            
            # 发起请求（带重试）
            response = await self._request_with_retry(method, endpoint, **kwargs)
            
            # 缓存响应
            if response.success and method.upper() == "GET" and self.config.enable_cache:
                self._cache_response(cache_key, response)
            
            # 记录指标
            response.response_time = time.time() - start_time
            self.metrics.record_request(response)
            
            return response
            
        except Exception as e:
            logger.error(f"API请求失败: {e}")
            response = APIResponse(
                success=False,
                data=None,
                error=str(e),
                response_time=time.time() - start_time
            )
            self.metrics.record_request(response)
            return response
    
    async def _request_with_retry(self, method: str, endpoint: str, **kwargs) -> APIResponse:
        """带重试的请求"""
        last_exception = None
        
        for attempt in range(self.config.max_retries + 1):
            try:
                response = await self._make_request(method, endpoint, **kwargs)
                
                # 成功或非重试错误直接返回
                if response.success or not self._should_retry(response):
                    return response
                
                last_exception = Exception(f"API错误: {response.error}")
                
            except Exception as e:
                last_exception = e
                logger.warning(f"请求失败 (尝试 {attempt + 1}/{self.config.max_retries + 1}): {e}")
            
            # 最后一次尝试失败
            if attempt == self.config.max_retries:
                break
            
            # 等待重试
            await self._wait_for_retry(attempt)
        
        # 所有重试都失败
        return APIResponse(
            success=False,
            data=None,
            error=str(last_exception)
        )
    
    def _should_retry(self, response: APIResponse) -> bool:
        """判断是否应该重试"""
        if not response.status_code:
            return True
        
        # 5xx错误和429（速率限制）应该重试
        return response.status_code >= 500 or response.status_code == 429
    
    async def _wait_for_retry(self, attempt: int):
        """重试等待"""
        if self.config.retry_strategy == RetryStrategy.EXPONENTIAL:
            wait_time = min(2 ** attempt, 60)  # 最大60秒
        elif self.config.retry_strategy == RetryStrategy.LINEAR:
            wait_time = (attempt + 1) * 2
        else:  # FIXED
            wait_time = 5
        
        await asyncio.sleep(wait_time)
    
    def _generate_cache_key(self, method: str, endpoint: str, kwargs: Dict) -> str:
        """生成缓存键"""
        key_data = f"{method}:{endpoint}:{json.dumps(kwargs, sort_keys=True)}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def _get_cached_response(self, cache_key: str) -> Optional[APIResponse]:
        """获取缓存响应"""
        if cache_key in self.cache:
            cached_data, timestamp = self.cache[cache_key]
            if time.time() - timestamp < self.config.cache_ttl:
                cached_data.cached = True
                return cached_data
            else:
                del self.cache[cache_key]
        return None
    
    def _cache_response(self, cache_key: str, response: APIResponse):
        """缓存响应"""
        self.cache[cache_key] = (response, time.time())
        
        # 清理过期缓存
        current_time = time.time()
        expired_keys = [
            key for key, (_, timestamp) in self.cache.items()
            if current_time - timestamp >= self.config.cache_ttl
        ]
        for key in expired_keys:
            del self.cache[key]

class RateLimiter:
    """速率限制器"""
    
    def __init__(self, requests_per_minute: int):
        self.requests_per_minute = requests_per_minute
        self.requests = []
        self.lock = asyncio.Lock()
    
    async def acquire(self):
        """获取请求许可"""
        async with self.lock:
            now = time.time()
            
            # 清理过期请求
            self.requests = [req_time for req_time in self.requests if now - req_time < 60]
            
            # 检查是否超过限制
            if len(self.requests) >= self.requests_per_minute:
                wait_time = 60 - (now - self.requests[0])
                if wait_time > 0:
                    await asyncio.sleep(wait_time)
                    return await self.acquire()
            
            # 记录当前请求
            self.requests.append(now)

class APIMetrics:
    """API指标收集器"""
    
    def __init__(self):
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.cache_hits = 0
        self.total_response_time = 0.0
        self.response_times = []
        self.error_counts = {}
    
    def record_request(self, response: APIResponse):
        """记录请求指标"""
        self.total_requests += 1
        
        if response.success:
            self.successful_requests += 1
        else:
            self.failed_requests += 1
            error_type = response.error or "unknown"
            self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1
        
        if response.response_time:
            self.total_response_time += response.response_time
            self.response_times.append(response.response_time)
            
            # 保持最近1000个响应时间
            if len(self.response_times) > 1000:
                self.response_times = self.response_times[-1000:]
    
    def record_cache_hit(self):
        """记录缓存命中"""
        self.cache_hits += 1
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取指标统计"""
        if self.total_requests == 0:
            return {"message": "暂无请求数据"}
        
        avg_response_time = self.total_response_time / self.total_requests
        success_rate = self.successful_requests / self.total_requests
        cache_hit_rate = self.cache_hits / self.total_requests if self.total_requests > 0 else 0
        
        # 计算响应时间百分位数
        if self.response_times:
            sorted_times = sorted(self.response_times)
            p50 = sorted_times[len(sorted_times) // 2]
            p95 = sorted_times[int(len(sorted_times) * 0.95)]
            p99 = sorted_times[int(len(sorted_times) * 0.99)]
        else:
            p50 = p95 = p99 = 0
        
        return {
            "total_requests": self.total_requests,
            "success_rate": success_rate,
            "cache_hit_rate": cache_hit_rate,
            "avg_response_time": avg_response_time,
            "response_time_percentiles": {
                "p50": p50,
                "p95": p95,
                "p99": p99
            },
            "error_distribution": self.error_counts
        }
```

### 具体模型客户端实现

```python
# openai_advanced_client.py
import aiohttp
import json
from typing import Dict, List, Optional, Any, AsyncGenerator

class OpenAIAdvancedClient(BaseAPIClient):
    """OpenAI高级客户端实现"""
    
    def __init__(self, api_key: str, **kwargs):
        config = APIConfig(
            base_url="https://api.openai.com/v1",
            api_key=api_key,
            **kwargs
        )
        super().__init__(config)
    
    async def _make_request(self, method: str, endpoint: str, **kwargs) -> APIResponse:
        """实现具体的请求逻辑"""
        url = f"{self.config.base_url}/{endpoint.lstrip('/')}"
        
        headers = {
            "Authorization": f"Bearer {self.config.api_key}",
            "Content-Type": "application/json"
        }
        
        try:
            async with self.session.request(
                method=method,
                url=url,
                headers=headers,
                **kwargs
            ) as response:
                
                status_code = response.status
                
                if response.content_type == 'application/json':
                    data = await response.json()
                else:
                    data = await response.text()
                
                if 200 <= status_code < 300:
                    return APIResponse(
                        success=True,
                        data=data,
                        status_code=status_code
                    )
                else:
                    error_message = data.get("error", {}).get("message", str(data)) if isinstance(data, dict) else str(data)
                    return APIResponse(
                        success=False,
                        data=None,
                        error=error_message,
                        status_code=status_code
                    )
                    
        except asyncio.TimeoutError:
            return APIResponse(
                success=False,
                data=None,
                error="请求超时"
            )
        except Exception as e:
            return APIResponse(
                success=False,
                data=None,
                error=str(e)
            )
    
    async def chat_completion(self, 
                            messages: List[Dict[str, str]], 
                            model: str = "gpt-3.5-turbo",
                            **kwargs) -> APIResponse:
        """聊天完成接口"""
        data = {
            "model": model,
            "messages": messages,
            **kwargs
        }
        
        return await self.request("POST", "/chat/completions", json=data)
    
    async def stream_chat_completion(self, 
                                   messages: List[Dict[str, str]], 
                                   model: str = "gpt-3.5-turbo",
                                   **kwargs) -> AsyncGenerator[Dict[str, Any], None]:
        """流式聊天完成"""
        data = {
            "model": model,
            "messages": messages,
            "stream": True,
            **kwargs
        }
        
        url = f"{self.config.base_url}/chat/completions"
        headers = {
            "Authorization": f"Bearer {self.config.api_key}",
            "Content-Type": "application/json"
        }
        
        try:
            async with self.session.post(url, headers=headers, json=data) as response:
                if response.status != 200:
                    error_data = await response.json()
                    yield {"error": error_data.get("error", {}).get("message", "未知错误")}
                    return
                
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    
                    if line.startswith('data: '):
                        data_str = line[6:]
                        
                        if data_str == '[DONE]':
                            break
                        
                        try:
                            chunk_data = json.loads(data_str)
                            if chunk_data.get("choices"):
                                delta = chunk_data["choices"][0].get("delta", {})
                                if "content" in delta:
                                    yield {
                                        "content": delta["content"],
                                        "finish_reason": chunk_data["choices"][0].get("finish_reason")
                                    }
                        except json.JSONDecodeError:
                            continue
                            
        except Exception as e:
            yield {"error": str(e)}
    
    async def create_embedding(self, 
                             text: Union[str, List[str]], 
                             model: str = "text-embedding-ada-002") -> APIResponse:
        """创建嵌入"""
        data = {
            "model": model,
            "input": text
        }
        
        return await self.request("POST", "/embeddings", json=data)
    
    async def list_models(self) -> APIResponse:
        """列出可用模型"""
        return await self.request("GET", "/models")

# 使用示例
async def demo_advanced_client():
    """高级客户端使用示例"""
    config = APIConfig(
        base_url="https://api.openai.com/v1",
        api_key="your-api-key",
        timeout=30,
        max_retries=3,
        retry_strategy=RetryStrategy.EXPONENTIAL,
        rate_limit=60,  # 每分钟60个请求
        enable_cache=True,
        cache_ttl=300
    )
    
    async with OpenAIAdvancedClient("your-api-key") as client:
        # 普通聊天
        messages = [{"role": "user", "content": "Hello, how are you?"}]
        response = await client.chat_completion(messages)
        
        if response.success:
            print(f"响应: {response.data}")
            print(f"响应时间: {response.response_time:.2f}秒")
            print(f"是否来自缓存: {response.cached}")
        else:
            print(f"错误: {response.error}")
        
        # 流式聊天
        print("\n流式响应:")
        async for chunk in client.stream_chat_completion(messages):
            if "content" in chunk:
                print(chunk["content"], end="")
            elif "error" in chunk:
                print(f"错误: {chunk['error']}")
        
        # 获取指标
        metrics = client.metrics.get_metrics()
        print(f"\nAPI指标: {metrics}")

if __name__ == "__main__":
    asyncio.run(demo_advanced_client())
```

## 错误处理策略

### 分层错误处理

```python
# error_handling.py
from enum import Enum
from typing import Dict, Any, Optional, Callable
import logging

class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ErrorCategory(Enum):
    """错误类别"""
    NETWORK = "network"
    AUTHENTICATION = "authentication"
    RATE_LIMIT = "rate_limit"
    QUOTA = "quota"
    VALIDATION = "validation"
    SERVER = "server"
    UNKNOWN = "unknown"

class APIError(Exception):
    """API错误基类"""
    
    def __init__(self, 
                 message: str,
                 category: ErrorCategory = ErrorCategory.UNKNOWN,
                 severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                 status_code: Optional[int] = None,
                 retry_after: Optional[int] = None,
                 context: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.category = category
        self.severity = severity
        self.status_code = status_code
        self.retry_after = retry_after
        self.context = context or {}

class ErrorHandler:
    """统一错误处理器"""
    
    def __init__(self):
        self.error_handlers: Dict[ErrorCategory, Callable] = {}
        self.logger = logging.getLogger(__name__)
    
    def register_handler(self, category: ErrorCategory, handler: Callable):
        """注册错误处理器"""
        self.error_handlers[category] = handler
    
    def handle_error(self, error: APIError) -> Dict[str, Any]:
        """处理错误"""
        # 记录错误日志
        self._log_error(error)
        
        # 调用特定处理器
        if error.category in self.error_handlers:
            return self.error_handlers[error.category](error)
        
        # 默认处理
        return self._default_handler(error)
    
    def _log_error(self, error: APIError):
        """记录错误日志"""
        log_level = {
            ErrorSeverity.LOW: logging.INFO,
            ErrorSeverity.MEDIUM: logging.WARNING,
            ErrorSeverity.HIGH: logging.ERROR,
            ErrorSeverity.CRITICAL: logging.CRITICAL
        }.get(error.severity, logging.ERROR)
        
        self.logger.log(
            log_level,
            f"API错误 [{error.category.value}]: {error.message}",
            extra={
                "status_code": error.status_code,
                "context": error.context
            }
        )
    
    def _default_handler(self, error: APIError) -> Dict[str, Any]:
        """默认错误处理"""
        return {
            "success": False,
            "error": {
                "message": error.message,
                "category": error.category.value,
                "severity": error.severity.value,
                "status_code": error.status_code,
                "retry_after": error.retry_after
            }
        }

# 具体错误处理器
def handle_rate_limit_error(error: APIError) -> Dict[str, Any]:
    """处理速率限制错误"""
    return {
        "success": False,
        "error": {
            "message": "请求频率过高，请稍后重试",
            "category": "rate_limit",
            "retry_after": error.retry_after or 60,
            "suggestion": "建议降低请求频率或升级API计划"
        }
    }

def handle_authentication_error(error: APIError) -> Dict[str, Any]:
    """处理认证错误"""
    return {
        "success": False,
        "error": {
            "message": "认证失败，请检查API密钥",
            "category": "authentication",
            "suggestion": "请检查API密钥是否正确且有效"
        }
    }

def handle_quota_error(error: APIError) -> Dict[str, Any]:
    """处理配额错误"""
    return {
        "success": False,
        "error": {
            "message": "API配额不足",
            "category": "quota",
            "suggestion": "请检查账户余额或升级服务计划"
        }
    }

# 错误分类器
class ErrorClassifier:
    """错误分类器"""
    
    @staticmethod
    def classify_http_error(status_code: int, response_data: Dict[str, Any]) -> APIError:
        """根据HTTP状态码分类错误"""
        message = response_data.get("error", {}).get("message", "未知错误")
        
        if status_code == 401:
            return APIError(
                message=message,
                category=ErrorCategory.AUTHENTICATION,
                severity=ErrorSeverity.HIGH,
                status_code=status_code
            )
        elif status_code == 429:
            retry_after = response_data.get("retry_after", 60)
            return APIError(
                message=message,
                category=ErrorCategory.RATE_LIMIT,
                severity=ErrorSeverity.MEDIUM,
                status_code=status_code,
                retry_after=retry_after
            )
        elif status_code == 402 or "quota" in message.lower():
            return APIError(
                message=message,
                category=ErrorCategory.QUOTA,
                severity=ErrorSeverity.HIGH,
                status_code=status_code
            )
        elif 500 <= status_code < 600:
            return APIError(
                message=message,
                category=ErrorCategory.SERVER,
                severity=ErrorSeverity.HIGH,
                status_code=status_code
            )
        else:
            return APIError(
                message=message,
                category=ErrorCategory.UNKNOWN,
                severity=ErrorSeverity.MEDIUM,
                status_code=status_code
            )

# 使用示例
def setup_error_handling():
    """设置错误处理"""
    error_handler = ErrorHandler()
    
    # 注册特定错误处理器
    error_handler.register_handler(ErrorCategory.RATE_LIMIT, handle_rate_limit_error)
    error_handler.register_handler(ErrorCategory.AUTHENTICATION, handle_authentication_error)
    error_handler.register_handler(ErrorCategory.QUOTA, handle_quota_error)
    
    return error_handler
```

这个文档提供了API调用和SDK集成的最佳实践，包括统一的客户端基类设计、高级错误处理策略、性能优化技术等核心内容。通过这些最佳实践，可以构建稳定、高效、安全的AI模型接口调用系统。
