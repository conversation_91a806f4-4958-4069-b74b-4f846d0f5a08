# 2.1 API基础知识与环境搭建

## 学习目标

完成本节学习后，学员将能够：
1. 理解REST API的基本概念和工作原理
2. 掌握HTTP请求方法和状态码的含义
3. 了解API认证机制和安全最佳实践
4. 搭建完整的LLM API开发环境
5. 能够进行基础的API调用和调试

## 理论基础

### REST API 核心概念

#### 什么是REST API？
REST（Representational State Transfer）是一种软件架构风格，用于设计网络应用程序的接口。LLM API通常采用REST架构，具有以下特点：

- **无状态性**：每个请求都包含处理该请求所需的所有信息
- **统一接口**：使用标准的HTTP方法（GET、POST、PUT、DELETE）
- **资源导向**：通过URL标识资源
- **分层系统**：支持缓存、负载均衡等中间层

#### HTTP请求方法详解

| 方法 | 用途 | LLM API中的应用 | 示例 |
|------|------|----------------|------|
| GET | 获取资源 | 获取模型列表、查询使用情况 | `GET /v1/models` |
| POST | 创建资源 | 发送聊天请求、生成文本 | `POST /v1/chat/completions` |
| PUT | 更新资源 | 更新配置（较少使用） | `PUT /v1/settings` |
| DELETE | 删除资源 | 删除会话（较少使用） | `DELETE /v1/sessions/{id}` |

#### HTTP状态码解析

**成功状态码（2xx）**
- `200 OK`：请求成功
- `201 Created`：资源创建成功
- `202 Accepted`：请求已接受，正在处理

**客户端错误（4xx）**
- `400 Bad Request`：请求格式错误
- `401 Unauthorized`：认证失败
- `403 Forbidden`：权限不足
- `404 Not Found`：资源不存在
- `429 Too Many Requests`：请求频率超限

**服务器错误（5xx）**
- `500 Internal Server Error`：服务器内部错误
- `502 Bad Gateway`：网关错误
- `503 Service Unavailable`：服务不可用

### 认证机制详解

#### API Key认证
最常用的认证方式，通过在请求头中包含API密钥来验证身份：

```http
Authorization: Bearer sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

#### Bearer Token认证
使用JWT或其他token格式：

```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 安全最佳实践

1. **密钥管理**
   - 使用环境变量存储API密钥
   - 定期轮换密钥
   - 不在代码中硬编码密钥
   - 使用密钥管理服务（如AWS Secrets Manager）

2. **网络安全**
   - 始终使用HTTPS
   - 实施IP白名单（如适用）
   - 监控异常访问模式

3. **权限控制**
   - 使用最小权限原则
   - 为不同环境使用不同的密钥
   - 实施访问日志记录

## 可视化图表说明

### 图表1：API调用流程图
**制作说明**：创建一个流程图，展示从客户端发起请求到接收响应的完整过程

```
[客户端应用] 
    ↓ 1. 构建HTTP请求
[HTTP请求]
    ↓ 2. 添加认证头
[认证处理]
    ↓ 3. 发送到API服务器
[LLM API服务器]
    ↓ 4. 验证认证
[权限验证]
    ↓ 5. 处理请求
[模型推理]
    ↓ 6. 返回响应
[HTTP响应]
    ↓ 7. 客户端处理
[客户端应用]
```

**图表要素**：
- 使用不同颜色区分客户端、网络传输、服务器端
- 标注每个步骤的耗时（典型值）
- 包含错误处理分支

### 图表2：认证机制对比表
**制作说明**：创建一个对比表格，展示不同认证方式的特点

| 认证方式 | 安全级别 | 实现复杂度 | 适用场景 | 示例 |
|----------|----------|------------|----------|------|
| API Key | 中等 | 简单 | 服务端应用 | OpenAI API |
| OAuth 2.0 | 高 | 复杂 | 用户授权应用 | Google API |
| JWT Token | 高 | 中等 | 微服务架构 | 自建服务 |

## 环境搭建实践

### Python环境配置

#### 1. 基础环境准备

```bash
# 创建虚拟环境
python -m venv llm_api_env

# 激活虚拟环境
# Windows
llm_api_env\Scripts\activate
# macOS/Linux
source llm_api_env/bin/activate

# 升级pip
python -m pip install --upgrade pip
```

#### 2. 必要库安装

```bash
# 核心库
pip install requests>=2.28.0
pip install openai>=1.0.0
pip install python-dotenv>=1.0.0

# 数据处理
pip install pandas>=1.5.0
pip install numpy>=1.24.0

# 异步支持
pip install aiohttp>=3.8.0
pip install asyncio

# 开发工具
pip install jupyter>=1.0.0
pip install ipython>=8.0.0

# 测试工具
pip install pytest>=7.0.0
pip install requests-mock>=1.10.0
```

#### 3. 环境变量配置

创建`.env`文件：

```bash
# .env 文件内容
OPENAI_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
ANTHROPIC_API_KEY=sk-ant-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
GOOGLE_API_KEY=AIzaSyxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# 可选配置
OPENAI_ORG_ID=org-xxxxxxxxxxxxxxxxxxxxxxxx
API_BASE_URL=https://api.openai.com/v1
REQUEST_TIMEOUT=30
MAX_RETRIES=3
```

### 开发工具配置

#### VS Code配置

创建`.vscode/settings.json`：

```json
{
    "python.defaultInterpreterPath": "./llm_api_env/bin/python",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": true,
    "python.formatting.provider": "black",
    "python.testing.pytestEnabled": true,
    "files.exclude": {
        "**/__pycache__": true,
        "**/.pytest_cache": true,
        "**/llm_api_env": true
    }
}
```

#### 项目结构

```
llm_api_project/
├── .env                    # 环境变量
├── .gitignore             # Git忽略文件
├── requirements.txt       # 依赖列表
├── config/
│   ├── __init__.py
│   └── settings.py        # 配置管理
├── src/
│   ├── __init__.py
│   ├── api_clients/       # API客户端
│   ├── utils/            # 工具函数
│   └── examples/         # 示例代码
├── tests/                # 测试代码
└── docs/                 # 文档
```

## 基础API调用实践

### 配置管理模块

```python
# config/settings.py
import os
from dotenv import load_dotenv
from typing import Optional

# 加载环境变量
load_dotenv()

class APIConfig:
    """API配置管理类"""
    
    def __init__(self):
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        self.anthropic_api_key = os.getenv('ANTHROPIC_API_KEY')
        self.google_api_key = os.getenv('GOOGLE_API_KEY')
        
        # 通用配置
        self.request_timeout = int(os.getenv('REQUEST_TIMEOUT', 30))
        self.max_retries = int(os.getenv('MAX_RETRIES', 3))
        self.base_url = os.getenv('API_BASE_URL', 'https://api.openai.com/v1')
        
        # 验证必要的配置
        self._validate_config()
    
    def _validate_config(self):
        """验证配置的有效性"""
        if not self.openai_api_key:
            raise ValueError("OPENAI_API_KEY 环境变量未设置")
        
        if not self.openai_api_key.startswith('sk-'):
            raise ValueError("OPENAI_API_KEY 格式不正确")
    
    def get_headers(self, api_type: str = 'openai') -> dict:
        """获取API请求头"""
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'LLM-API-Client/1.0'
        }
        
        if api_type == 'openai':
            headers['Authorization'] = f'Bearer {self.openai_api_key}'
        elif api_type == 'anthropic':
            headers['x-api-key'] = self.anthropic_api_key
        elif api_type == 'google':
            headers['Authorization'] = f'Bearer {self.google_api_key}'
        
        return headers

# 全局配置实例
config = APIConfig()
```

### 基础API客户端

```python
# src/api_clients/base_client.py
import requests
import time
import json
from typing import Dict, Any, Optional
from config.settings import config
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BaseAPIClient:
    """基础API客户端类"""
    
    def __init__(self, api_type: str = 'openai'):
        self.api_type = api_type
        self.session = requests.Session()
        self.session.headers.update(config.get_headers(api_type))
        
    def _make_request(self, method: str, url: str, **kwargs) -> requests.Response:
        """发起HTTP请求，包含重试机制"""
        
        for attempt in range(config.max_retries):
            try:
                response = self.session.request(
                    method=method,
                    url=url,
                    timeout=config.request_timeout,
                    **kwargs
                )
                
                # 检查响应状态
                if response.status_code == 429:  # 速率限制
                    retry_after = int(response.headers.get('Retry-After', 60))
                    logger.warning(f"速率限制，等待 {retry_after} 秒后重试")
                    time.sleep(retry_after)
                    continue
                
                response.raise_for_status()
                return response
                
            except requests.exceptions.RequestException as e:
                logger.error(f"请求失败 (尝试 {attempt + 1}/{config.max_retries}): {e}")
                if attempt == config.max_retries - 1:
                    raise
                time.sleep(2 ** attempt)  # 指数退避
        
        raise Exception("所有重试都失败了")
    
    def get(self, endpoint: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """GET请求"""
        url = f"{config.base_url}/{endpoint.lstrip('/')}"
        response = self._make_request('GET', url, params=params)
        return response.json()
    
    def post(self, endpoint: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """POST请求"""
        url = f"{config.base_url}/{endpoint.lstrip('/')}"
        response = self._make_request('POST', url, json=data)
        return response.json()

# 使用示例
if __name__ == "__main__":
    client = BaseAPIClient()
    
    # 测试连接
    try:
        models = client.get('models')
        print(f"成功连接到API，可用模型数量: {len(models.get('data', []))}")
    except Exception as e:
        print(f"连接失败: {e}")
```

## 实践练习

### 练习1：环境验证脚本

**任务**：编写一个脚本来验证开发环境是否正确配置

```python
# exercises/environment_check.py
import sys
import os
import importlib
from typing import List, Tuple

def check_python_version() -> Tuple[bool, str]:
    """检查Python版本"""
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        return True, f"Python {version.major}.{version.minor}.{version.micro} ✓"
    else:
        return False, f"Python版本过低: {version.major}.{version.minor}.{version.micro}"

def check_required_packages() -> List[Tuple[str, bool, str]]:
    """检查必要的包是否安装"""
    required_packages = [
        'requests', 'openai', 'python-dotenv', 
        'pandas', 'numpy', 'aiohttp'
    ]
    
    results = []
    for package in required_packages:
        try:
            importlib.import_module(package.replace('-', '_'))
            results.append((package, True, "已安装 ✓"))
        except ImportError:
            results.append((package, False, "未安装 ✗"))
    
    return results

def check_environment_variables() -> List[Tuple[str, bool, str]]:
    """检查环境变量"""
    required_vars = ['OPENAI_API_KEY']
    optional_vars = ['ANTHROPIC_API_KEY', 'GOOGLE_API_KEY']
    
    results = []
    
    # 检查必需的环境变量
    for var in required_vars:
        value = os.getenv(var)
        if value:
            masked_value = value[:8] + "..." + value[-4:] if len(value) > 12 else "***"
            results.append((var, True, f"已设置: {masked_value} ✓"))
        else:
            results.append((var, False, "未设置 ✗"))
    
    # 检查可选的环境变量
    for var in optional_vars:
        value = os.getenv(var)
        if value:
            masked_value = value[:8] + "..." + value[-4:] if len(value) > 12 else "***"
            results.append((var, True, f"已设置: {masked_value} ✓"))
        else:
            results.append((var, False, "未设置 (可选)"))
    
    return results

def main():
    """主检查函数"""
    print("=== LLM API 开发环境检查 ===\n")
    
    # 检查Python版本
    python_ok, python_msg = check_python_version()
    print(f"Python版本: {python_msg}")
    
    # 检查包安装
    print("\n包安装检查:")
    package_results = check_required_packages()
    all_packages_ok = True
    
    for package, ok, msg in package_results:
        print(f"  {package}: {msg}")
        if not ok:
            all_packages_ok = False
    
    # 检查环境变量
    print("\n环境变量检查:")
    env_results = check_environment_variables()
    required_env_ok = True
    
    for var, ok, msg in env_results:
        print(f"  {var}: {msg}")
        if not ok and var in ['OPENAI_API_KEY']:
            required_env_ok = False
    
    # 总结
    print("\n=== 检查结果 ===")
    if python_ok and all_packages_ok and required_env_ok:
        print("✓ 环境配置完整，可以开始学习！")
    else:
        print("✗ 环境配置不完整，请根据上述提示进行修复")
        
        if not python_ok:
            print("  - 请升级Python到3.8或更高版本")
        if not all_packages_ok:
            print("  - 请安装缺失的Python包")
        if not required_env_ok:
            print("  - 请设置必需的环境变量")

if __name__ == "__main__":
    main()
```

### 练习2：API连接测试

**任务**：编写一个测试脚本来验证API连接

```python
# exercises/api_connection_test.py
import asyncio
import aiohttp
from src.api_clients.base_client import BaseAPIClient
from config.settings import config

async def test_api_async():
    """异步API测试"""
    async with aiohttp.ClientSession() as session:
        headers = config.get_headers('openai')
        
        try:
            async with session.get(
                f"{config.base_url}/models",
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return True, f"异步连接成功，模型数量: {len(data.get('data', []))}"
                else:
                    return False, f"异步连接失败，状态码: {response.status}"
        except Exception as e:
            return False, f"异步连接异常: {e}"

def test_api_sync():
    """同步API测试"""
    try:
        client = BaseAPIClient()
        models = client.get('models')
        return True, f"同步连接成功，模型数量: {len(models.get('data', []))}"
    except Exception as e:
        return False, f"同步连接失败: {e}"

async def main():
    """主测试函数"""
    print("=== API连接测试 ===\n")
    
    # 同步测试
    print("1. 同步API测试:")
    sync_ok, sync_msg = test_api_sync()
    print(f"   {sync_msg}")
    
    # 异步测试
    print("\n2. 异步API测试:")
    async_ok, async_msg = await test_api_async()
    print(f"   {async_msg}")
    
    # 总结
    print(f"\n=== 测试结果 ===")
    if sync_ok and async_ok:
        print("✓ 所有API连接测试通过！")
    else:
        print("✗ 部分API连接测试失败，请检查网络和API密钥")

if __name__ == "__main__":
    asyncio.run(main())
```

## 常见问题解答

### Q1: API密钥安全存储的最佳实践是什么？

**A1**: 
1. **使用环境变量**：永远不要在代码中硬编码API密钥
2. **使用.env文件**：在开发环境中使用.env文件，并将其添加到.gitignore
3. **生产环境**：使用云服务的密钥管理服务（如AWS Secrets Manager、Azure Key Vault）
4. **权限控制**：为不同环境使用不同的API密钥，实施最小权限原则

### Q2: 如何处理API调用的速率限制？

**A2**:
1. **实施重试机制**：使用指数退避策略
2. **监控Retry-After头**：遵循API返回的重试时间
3. **请求队列**：实施请求队列来控制并发
4. **缓存策略**：缓存常用的响应以减少API调用

### Q3: 如何调试API调用问题？

**A3**:
1. **启用详细日志**：记录请求和响应的详细信息
2. **使用调试工具**：如Postman、curl命令行工具
3. **检查状态码**：理解不同HTTP状态码的含义
4. **验证请求格式**：确保JSON格式正确，必需字段完整

## 评估标准

### 知识理解（40%）
- [ ] 能够解释REST API的基本概念
- [ ] 理解HTTP方法和状态码的含义
- [ ] 掌握API认证机制的工作原理

### 实践能力（40%）
- [ ] 能够正确配置开发环境
- [ ] 成功进行基础的API调用
- [ ] 实施错误处理和重试机制

### 问题解决（20%）
- [ ] 能够诊断和解决常见的API问题
- [ ] 理解安全最佳实践并能应用
- [ ] 能够优化API调用的性能

## 下一步学习

完成本节学习后，建议：
1. 练习使用不同的HTTP客户端工具
2. 尝试调用其他类型的API服务
3. 学习更高级的认证机制（如OAuth 2.0）
4. 准备进入下一节：OpenAI API深度实践
