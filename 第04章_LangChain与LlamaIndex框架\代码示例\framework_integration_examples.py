"""
LangChain与LlamaIndex框架集成示例
展示如何将两个框架的优势结合起来，构建更强大的AI应用
"""

from langchain.chains import LL<PERSON>hain
from langchain.prompts import PromptTemplate
from langchain.agents import Tool, AgentExecutor, initialize_agent, AgentType
from langchain.memory import ConversationBufferMemory
from llama_index.core import Document, VectorStoreIndex, SimpleDirectoryReader
from llama_index.core.query_engine import RetrieverQueryEngine
from llama_index.core.retrievers import VectorIndexRetriever
from llama_index.core.tools import QueryEngineTool, ToolMetadata
from typing import List, Dict, Any, Optional
import asyncio
import json
from datetime import datetime

# ============================================================================
# 1. 基础集成：LlamaIndex作为LangChain工具
# ============================================================================

class LlamaIndexTool:
    """将LlamaIndex查询引擎包装为LangChain工具"""
    
    def __init__(self, index, tool_name: str = "knowledge_base", 
                 tool_description: str = "查询知识库获取相关信息"):
        self.index = index
        self.tool_name = tool_name
        self.tool_description = tool_description
        self.query_engine = index.as_query_engine()
    
    def create_langchain_tool(self) -> Tool:
        """创建LangChain工具"""
        def query_knowledge_base(query: str) -> str:
            """查询知识库"""
            try:
                response = self.query_engine.query(query)
                return str(response)
            except Exception as e:
                return f"查询失败: {str(e)}"
        
        return Tool(
            name=self.tool_name,
            description=self.tool_description,
            func=query_knowledge_base
        )

class IntegratedRAGAgent:
    """集成RAG代理 - 结合LangChain的代理能力和LlamaIndex的检索能力"""
    
    def __init__(self, llm, documents: List[Document]):
        self.llm = llm
        self.documents = documents
        
        # 创建LlamaIndex索引
        self.index = VectorStoreIndex.from_documents(documents)
        
        # 创建工具
        self.tools = self._create_tools()
        
        # 创建记忆
        self.memory = ConversationBufferMemory(
            memory_key="chat_history",
            return_messages=True
        )
        
        # 创建代理
        self.agent = self._create_agent()
    
    def _create_tools(self) -> List[Tool]:
        """创建工具列表"""
        tools = []
        
        # LlamaIndex知识库工具
        llamaindex_tool = LlamaIndexTool(
            index=self.index,
            tool_name="knowledge_search",
            tool_description="搜索知识库中的相关信息，适用于事实性问题"
        )
        tools.append(llamaindex_tool.create_langchain_tool())
        
        # 计算工具
        def calculator(expression: str) -> str:
            """简单计算器"""
            try:
                # 安全的数学表达式计算
                allowed_chars = set('0123456789+-*/.() ')
                if all(c in allowed_chars for c in expression):
                    result = eval(expression)
                    return f"计算结果: {result}"
                else:
                    return "表达式包含不允许的字符"
            except Exception as e:
                return f"计算错误: {str(e)}"
        
        tools.append(Tool(
            name="calculator",
            description="执行数学计算，输入数学表达式",
            func=calculator
        ))
        
        # 文档分析工具
        def analyze_documents(query: str) -> str:
            """分析文档统计信息"""
            total_docs = len(self.documents)
            total_words = sum(len(doc.text.split()) for doc in self.documents)
            avg_words = total_words / total_docs if total_docs > 0 else 0
            
            return f"文档统计: 总文档数 {total_docs}, 总词数 {total_words}, 平均词数 {avg_words:.1f}"
        
        tools.append(Tool(
            name="document_analyzer",
            description="分析文档集合的统计信息",
            func=analyze_documents
        ))
        
        return tools
    
    def _create_agent(self) -> AgentExecutor:
        """创建代理执行器"""
        agent = initialize_agent(
            tools=self.tools,
            llm=self.llm,
            agent=AgentType.CONVERSATIONAL_REACT_DESCRIPTION,
            memory=self.memory,
            verbose=True,
            max_iterations=3
        )
        return agent
    
    def query(self, question: str) -> Dict[str, Any]:
        """执行查询"""
        start_time = datetime.now()
        
        try:
            response = self.agent.run(question)
            success = True
            error = None
        except Exception as e:
            response = f"查询失败: {str(e)}"
            success = False
            error = str(e)
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        return {
            "question": question,
            "answer": response,
            "success": success,
            "error": error,
            "processing_time": processing_time,
            "timestamp": start_time.isoformat()
        }

# ============================================================================
# 2. 高级集成：多索引智能路由
# ============================================================================

class MultiIndexRouter:
    """多索引路由器 - 智能选择最适合的索引进行查询"""
    
    def __init__(self, llm):
        self.llm = llm
        self.indexes = {}
        self.index_descriptions = {}
        self.query_history = []
    
    def add_index(self, name: str, index, description: str):
        """添加索引"""
        self.indexes[name] = index
        self.index_descriptions[name] = description
    
    def route_query(self, query: str) -> Dict[str, Any]:
        """路由查询到最适合的索引"""
        # 选择最佳索引
        best_index_name = self._select_best_index(query)
        
        if not best_index_name:
            return {
                "answer": "没有找到合适的索引处理此查询",
                "index_used": None,
                "confidence": 0.0
            }
        
        # 执行查询
        index = self.indexes[best_index_name]
        query_engine = index.as_query_engine()
        response = query_engine.query(query)
        
        # 记录查询历史
        query_record = {
            "query": query,
            "index_used": best_index_name,
            "timestamp": datetime.now().isoformat()
        }
        self.query_history.append(query_record)
        
        return {
            "answer": str(response),
            "index_used": best_index_name,
            "index_description": self.index_descriptions[best_index_name],
            "confidence": 0.8  # 简化的置信度
        }
    
    def _select_best_index(self, query: str) -> Optional[str]:
        """选择最佳索引"""
        if not self.indexes:
            return None
        
        # 简化的索引选择逻辑
        query_lower = query.lower()
        
        # 基于关键词的简单匹配
        for index_name, description in self.index_descriptions.items():
            description_lower = description.lower()
            
            # 计算关键词匹配度
            query_words = set(query_lower.split())
            description_words = set(description_lower.split())
            
            overlap = len(query_words & description_words)
            if overlap > 0:
                return index_name
        
        # 如果没有明显匹配，返回第一个索引
        return list(self.indexes.keys())[0]
    
    def get_routing_statistics(self) -> Dict[str, Any]:
        """获取路由统计"""
        if not self.query_history:
            return {"total_queries": 0}
        
        index_usage = {}
        for record in self.query_history:
            index_name = record["index_used"]
            index_usage[index_name] = index_usage.get(index_name, 0) + 1
        
        return {
            "total_queries": len(self.query_history),
            "index_usage": index_usage,
            "recent_queries": self.query_history[-5:]
        }

# ============================================================================
# 3. 工作流集成：复杂任务编排
# ============================================================================

class HybridWorkflowEngine:
    """混合工作流引擎 - 编排复杂的AI任务流程"""
    
    def __init__(self, llm):
        self.llm = llm
        self.workflows = {}
        self.execution_history = []
    
    def register_workflow(self, name: str, workflow_config: Dict[str, Any]):
        """注册工作流"""
        self.workflows[name] = workflow_config
    
    async def execute_workflow(self, workflow_name: str, 
                             input_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行工作流"""
        if workflow_name not in self.workflows:
            raise ValueError(f"工作流 '{workflow_name}' 不存在")
        
        workflow_config = self.workflows[workflow_name]
        steps = workflow_config.get("steps", [])
        
        execution_id = f"{workflow_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        execution_log = {
            "execution_id": execution_id,
            "workflow_name": workflow_name,
            "start_time": datetime.now().isoformat(),
            "input_data": input_data,
            "steps": [],
            "final_result": None,
            "success": False
        }
        
        current_data = input_data.copy()
        
        try:
            for i, step in enumerate(steps):
                step_result = await self._execute_step(step, current_data)
                
                step_log = {
                    "step_index": i,
                    "step_name": step.get("name", f"step_{i}"),
                    "step_type": step.get("type"),
                    "input": current_data.copy(),
                    "output": step_result,
                    "timestamp": datetime.now().isoformat()
                }
                
                execution_log["steps"].append(step_log)
                
                # 更新当前数据
                if isinstance(step_result, dict):
                    current_data.update(step_result)
                else:
                    current_data["last_result"] = step_result
            
            execution_log["final_result"] = current_data
            execution_log["success"] = True
            
        except Exception as e:
            execution_log["error"] = str(e)
            execution_log["success"] = False
        
        execution_log["end_time"] = datetime.now().isoformat()
        self.execution_history.append(execution_log)
        
        return execution_log
    
    async def _execute_step(self, step: Dict[str, Any], 
                          current_data: Dict[str, Any]) -> Any:
        """执行单个步骤"""
        step_type = step.get("type")
        step_config = step.get("config", {})
        
        if step_type == "llm_query":
            return await self._execute_llm_step(step_config, current_data)
        elif step_type == "index_search":
            return await self._execute_index_step(step_config, current_data)
        elif step_type == "data_transform":
            return await self._execute_transform_step(step_config, current_data)
        else:
            raise ValueError(f"不支持的步骤类型: {step_type}")
    
    async def _execute_llm_step(self, config: Dict[str, Any], 
                              data: Dict[str, Any]) -> str:
        """执行LLM步骤"""
        prompt_template = config.get("prompt_template", "{input}")
        
        # 格式化提示
        prompt = prompt_template.format(**data)
        
        # 调用LLM
        response = await asyncio.to_thread(self.llm, prompt)
        return response
    
    async def _execute_index_step(self, config: Dict[str, Any], 
                                data: Dict[str, Any]) -> str:
        """执行索引搜索步骤"""
        # 这里需要实际的索引对象
        # 简化实现
        query = data.get("query", "")
        return f"索引搜索结果: {query}"
    
    async def _execute_transform_step(self, config: Dict[str, Any], 
                                    data: Dict[str, Any]) -> Dict[str, Any]:
        """执行数据转换步骤"""
        transform_type = config.get("transform_type", "identity")
        
        if transform_type == "extract_keywords":
            text = data.get("text", "")
            keywords = text.split()[:5]  # 简化的关键词提取
            return {"keywords": keywords}
        elif transform_type == "summarize":
            text = data.get("text", "")
            summary = text[:100] + "..." if len(text) > 100 else text
            return {"summary": summary}
        else:
            return data

# ============================================================================
# 4. 使用示例
# ============================================================================

async def demo_framework_integration():
    """演示框架集成"""
    print("=== LangChain与LlamaIndex框架集成演示 ===")
    
    # 创建示例文档
    documents = [
        Document(text="人工智能是计算机科学的一个分支，致力于创建智能机器。AI技术包括机器学习、深度学习、自然语言处理等。"),
        Document(text="机器学习是AI的核心技术，通过算法让计算机从数据中学习。常见算法包括线性回归、决策树、神经网络等。"),
        Document(text="深度学习使用多层神经网络模拟人脑处理信息。在图像识别、语音识别等领域取得突破性进展。"),
        Document(text="自然语言处理让计算机理解和生成人类语言。应用包括搜索引擎、聊天机器人、机器翻译等。")
    ]
    
    # 模拟LLM
    class MockLLM:
        def __call__(self, prompt):
            if "计算" in prompt:
                return "这是一个数学计算问题"
            elif "人工智能" in prompt:
                return "人工智能是一个广泛的技术领域"
            else:
                return "这是LLM的回答"
    
    llm = MockLLM()
    
    # 1. 演示集成RAG代理
    print("\n1. 集成RAG代理演示")
    try:
        rag_agent = IntegratedRAGAgent(llm, documents)
        
        test_queries = [
            "什么是人工智能？",
            "计算 2 + 3 * 4",
            "分析一下文档集合"
        ]
        
        for query in test_queries:
            result = rag_agent.query(query)
            print(f"\n查询: {query}")
            print(f"成功: {result['success']}")
            print(f"回答: {result['answer'][:100]}...")
            print(f"处理时间: {result['processing_time']:.3f}秒")
    
    except Exception as e:
        print(f"集成RAG代理演示失败: {e}")
    
    # 2. 演示多索引路由
    print("\n2. 多索引路由演示")
    try:
        router = MultiIndexRouter(llm)
        
        # 创建不同主题的索引
        ai_docs = [doc for doc in documents if "人工智能" in doc.text or "AI" in doc.text]
        ml_docs = [doc for doc in documents if "机器学习" in doc.text or "算法" in doc.text]
        
        if ai_docs:
            ai_index = VectorStoreIndex.from_documents(ai_docs)
            router.add_index("ai_knowledge", ai_index, "人工智能相关知识")
        
        if ml_docs:
            ml_index = VectorStoreIndex.from_documents(ml_docs)
            router.add_index("ml_knowledge", ml_index, "机器学习相关知识")
        
        # 测试路由
        test_queries = [
            "什么是人工智能？",
            "机器学习算法有哪些？"
        ]
        
        for query in test_queries:
            result = router.route_query(query)
            print(f"\n查询: {query}")
            print(f"使用索引: {result['index_used']}")
            print(f"回答: {result['answer'][:100]}...")
        
        # 显示路由统计
        stats = router.get_routing_statistics()
        print(f"\n路由统计: {stats}")
    
    except Exception as e:
        print(f"多索引路由演示失败: {e}")
    
    # 3. 演示工作流引擎
    print("\n3. 工作流引擎演示")
    try:
        workflow_engine = HybridWorkflowEngine(llm)
        
        # 注册示例工作流
        content_analysis_workflow = {
            "name": "内容分析工作流",
            "description": "分析文本内容并生成摘要",
            "steps": [
                {
                    "name": "提取关键词",
                    "type": "data_transform",
                    "config": {"transform_type": "extract_keywords"}
                },
                {
                    "name": "生成摘要",
                    "type": "data_transform",
                    "config": {"transform_type": "summarize"}
                },
                {
                    "name": "LLM分析",
                    "type": "llm_query",
                    "config": {
                        "prompt_template": "分析以下内容的关键词 {keywords} 和摘要 {summary}，提供深入见解。"
                    }
                }
            ]
        }
        
        workflow_engine.register_workflow("content_analysis", content_analysis_workflow)
        
        # 执行工作流
        input_data = {
            "text": "人工智能技术正在快速发展，机器学习和深度学习是其核心技术。这些技术在各个领域都有广泛应用。"
        }
        
        result = await workflow_engine.execute_workflow("content_analysis", input_data)
        
        print(f"工作流执行结果:")
        print(f"- 执行ID: {result['execution_id']}")
        print(f"- 成功: {result['success']}")
        print(f"- 步骤数: {len(result['steps'])}")
        
        for step in result['steps']:
            print(f"  步骤: {step['step_name']} -> {str(step['output'])[:50]}...")
    
    except Exception as e:
        print(f"工作流引擎演示失败: {e}")
    
    print("\n演示完成！")

if __name__ == "__main__":
    asyncio.run(demo_framework_integration())
