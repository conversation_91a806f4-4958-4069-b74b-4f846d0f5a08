# 第05章：自然语言模型的 Embeddings 与向量数据库

## 章节概述

本章节将深入探讨 Embeddings（嵌入向量）技术和向量数据库的原理与应用。学员将学习如何将文本转换为高维向量表示，理解语义相似度计算，并掌握使用向量数据库构建高效语义搜索系统的技术。

## 学习目标

完成本章节学习后，学员将能够：

1. **理解 Embeddings 原理**：掌握词向量、句向量的生成和应用
2. **熟练使用向量数据库**：掌握主流向量数据库的使用方法
3. **构建语义搜索系统**：实现基于语义相似度的搜索功能
4. **优化向量检索性能**：掌握索引优化和查询加速技术
5. **处理多模态数据**：理解图像、音频等多模态向量化技术

## 章节结构

### 5.1 Embeddings 原理与实现
**时长**：120分钟

#### 理论内容
- **向量化基础概念**
  - 什么是 Embeddings
  - 从 One-Hot 到密集向量
  - 维度诅咒与降维技术
  - 向量空间的几何意义

- **经典 Embeddings 方法**
  - Word2Vec（CBOW 和 Skip-gram）
  - GloVe（全局向量表示）
  - FastText（子词级别向量）
  - Doc2Vec（文档向量）

- **现代 Transformer Embeddings**
  - BERT 系列模型
  - Sentence-BERT
  - OpenAI text-embedding-ada-002
  - 多语言嵌入模型

#### 视觉化学习辅助
- **向量空间可视化**：使用 t-SNE/UMAP 展示词向量分布
- **相似度计算图解**：余弦相似度、欧氏距离等计算方法
- **模型架构对比图**：不同嵌入模型的结构差异
- **性能基准图表**：各种嵌入模型在不同任务上的表现

#### 实践操作
```python
# Embeddings 生成与使用示例
import numpy as np
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
import openai
from typing import List, Tuple

class EmbeddingManager:
    def __init__(self, model_name="all-MiniLM-L6-v2"):
        """
        初始化嵌入管理器
        支持多种嵌入模型：
        - all-MiniLM-L6-v2: 轻量级，速度快
        - all-mpnet-base-v2: 高质量，平衡性能
        - paraphrase-multilingual-MiniLM-L12-v2: 多语言支持
        """
        self.model = SentenceTransformer(model_name)
        self.model_name = model_name
    
    def encode_texts(self, texts: List[str]) -> np.ndarray:
        """将文本列表转换为嵌入向量"""
        embeddings = self.model.encode(texts, convert_to_numpy=True)
        return embeddings
    
    def compute_similarity(self, text1: str, text2: str) -> float:
        """计算两个文本的相似度"""
        embeddings = self.encode_texts([text1, text2])
        similarity = cosine_similarity([embeddings[0]], [embeddings[1]])[0][0]
        return similarity
    
    def find_most_similar(self, query: str, candidates: List[str], top_k: int = 5) -> List[Tuple[str, float]]:
        """在候选文本中找到最相似的文本"""
        query_embedding = self.encode_texts([query])
        candidate_embeddings = self.encode_texts(candidates)
        
        similarities = cosine_similarity(query_embedding, candidate_embeddings)[0]
        
        # 获取最相似的 top_k 个结果
        top_indices = np.argsort(similarities)[::-1][:top_k]
        results = [(candidates[i], similarities[i]) for i in top_indices]
        
        return results

# OpenAI Embeddings 使用示例
class OpenAIEmbeddings:
    def __init__(self, api_key: str):
        openai.api_key = api_key
    
    def get_embedding(self, text: str, model: str = "text-embedding-ada-002") -> List[float]:
        """获取 OpenAI 嵌入向量"""
        response = openai.Embedding.create(
            input=text,
            model=model
        )
        return response['data'][0]['embedding']
    
    def batch_embeddings(self, texts: List[str], model: str = "text-embedding-ada-002") -> List[List[float]]:
        """批量获取嵌入向量"""
        response = openai.Embedding.create(
            input=texts,
            model=model
        )
        return [item['embedding'] for item in response['data']]

# 使用示例
embedding_manager = EmbeddingManager()

# 示例文本
texts = [
    "机器学习是人工智能的一个分支",
    "深度学习使用神经网络进行学习",
    "自然语言处理处理人类语言",
    "计算机视觉让机器理解图像",
    "今天天气很好，适合出门散步"
]

# 生成嵌入向量
embeddings = embedding_manager.encode_texts(texts)
print(f"嵌入向量维度: {embeddings.shape}")

# 查找相似文本
query = "什么是机器学习？"
similar_texts = embedding_manager.find_most_similar(query, texts, top_k=3)

print(f"查询: {query}")
for text, similarity in similar_texts:
    print(f"相似度: {similarity:.3f} - {text}")
```

### 5.2 向量数据库技术详解
**时长**：120分钟

#### 理论内容
- **向量数据库概述**
  - 传统数据库 vs 向量数据库
  - 向量数据库的应用场景
  - 主流向量数据库对比

- **核心技术原理**
  - 近似最近邻搜索（ANN）
  - 索引算法：LSH、IVF、HNSW、Annoy
  - 量化技术：PQ、SQ
  - 分布式架构设计

- **主流向量数据库**
  - **Pinecone**：云端托管服务
  - **Weaviate**：开源图向量数据库
  - **Chroma**：轻量级嵌入数据库
  - **Milvus**：高性能向量数据库
  - **FAISS**：Facebook 的相似性搜索库

#### 视觉化学习辅助
- **索引算法对比图**：不同索引方法的性能和适用场景
- **数据库架构图**：向量数据库的内部结构
- **查询流程图**：从查询到结果返回的完整流程
- **性能基准测试**：不同数据库在各种场景下的表现

#### 实践操作
```python
# 多种向量数据库使用示例
import chromadb
import pinecone
import faiss
import numpy as np
from typing import List, Dict, Any

# 1. Chroma 数据库使用
class ChromaVectorDB:
    def __init__(self, collection_name: str = "default"):
        self.client = chromadb.Client()
        self.collection = self.client.create_collection(
            name=collection_name,
            metadata={"hnsw:space": "cosine"}
        )
    
    def add_documents(self, documents: List[str], embeddings: List[List[float]], 
                     metadatas: List[Dict] = None, ids: List[str] = None):
        """添加文档到向量数据库"""
        if ids is None:
            ids = [f"doc_{i}" for i in range(len(documents))]
        
        self.collection.add(
            documents=documents,
            embeddings=embeddings,
            metadatas=metadatas,
            ids=ids
        )
    
    def query(self, query_embedding: List[float], n_results: int = 5):
        """查询相似文档"""
        results = self.collection.query(
            query_embeddings=[query_embedding],
            n_results=n_results
        )
        return results

# 2. FAISS 使用示例
class FAISSVectorDB:
    def __init__(self, dimension: int):
        self.dimension = dimension
        self.index = faiss.IndexFlatIP(dimension)  # 内积索引
        self.documents = []
    
    def add_vectors(self, vectors: np.ndarray, documents: List[str]):
        """添加向量到索引"""
        # 归一化向量（用于余弦相似度）
        faiss.normalize_L2(vectors)
        self.index.add(vectors)
        self.documents.extend(documents)
    
    def search(self, query_vector: np.ndarray, k: int = 5):
        """搜索最相似的向量"""
        # 归一化查询向量
        faiss.normalize_L2(query_vector.reshape(1, -1))
        
        scores, indices = self.index.search(query_vector.reshape(1, -1), k)
        
        results = []
        for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
            if idx != -1:  # 有效索引
                results.append({
                    'document': self.documents[idx],
                    'score': float(score),
                    'index': int(idx)
                })
        
        return results

# 3. Pinecone 使用示例（需要 API key）
class PineconeVectorDB:
    def __init__(self, api_key: str, environment: str, index_name: str):
        pinecone.init(api_key=api_key, environment=environment)
        
        # 创建索引（如果不存在）
        if index_name not in pinecone.list_indexes():
            pinecone.create_index(
                name=index_name,
                dimension=1536,  # OpenAI ada-002 的维度
                metric="cosine"
            )
        
        self.index = pinecone.Index(index_name)
    
    def upsert_vectors(self, vectors: List[Tuple[str, List[float], Dict]]):
        """插入或更新向量"""
        self.index.upsert(vectors=vectors)
    
    def query(self, vector: List[float], top_k: int = 5, 
              filter: Dict = None, include_metadata: bool = True):
        """查询相似向量"""
        response = self.index.query(
            vector=vector,
            top_k=top_k,
            filter=filter,
            include_metadata=include_metadata
        )
        return response

# 综合使用示例
class UnifiedVectorDB:
    def __init__(self, db_type: str = "chroma", **kwargs):
        self.db_type = db_type
        
        if db_type == "chroma":
            self.db = ChromaVectorDB(kwargs.get("collection_name", "default"))
        elif db_type == "faiss":
            self.db = FAISSVectorDB(kwargs.get("dimension", 384))
        elif db_type == "pinecone":
            self.db = PineconeVectorDB(
                kwargs["api_key"], 
                kwargs["environment"], 
                kwargs["index_name"]
            )
        else:
            raise ValueError(f"不支持的数据库类型: {db_type}")
    
    def add_documents(self, documents: List[str], embeddings: List[List[float]]):
        """统一的文档添加接口"""
        if self.db_type == "chroma":
            self.db.add_documents(documents, embeddings)
        elif self.db_type == "faiss":
            self.db.add_vectors(np.array(embeddings), documents)
        elif self.db_type == "pinecone":
            vectors = [(f"doc_{i}", emb, {"text": doc}) 
                      for i, (doc, emb) in enumerate(zip(documents, embeddings))]
            self.db.upsert_vectors(vectors)
    
    def search(self, query_embedding: List[float], top_k: int = 5):
        """统一的搜索接口"""
        if self.db_type == "chroma":
            return self.db.query(query_embedding, top_k)
        elif self.db_type == "faiss":
            return self.db.search(np.array(query_embedding), top_k)
        elif self.db_type == "pinecone":
            return self.db.query(query_embedding, top_k)

# 使用示例
# 初始化嵌入管理器和向量数据库
embedding_manager = EmbeddingManager()
vector_db = UnifiedVectorDB("chroma")

# 准备文档
documents = [
    "Python 是一种高级编程语言",
    "机器学习算法可以从数据中学习",
    "深度学习是机器学习的一个子集",
    "自然语言处理帮助计算机理解人类语言",
    "向量数据库专门用于存储和检索向量数据"
]

# 生成嵌入向量并存储
embeddings = embedding_manager.encode_texts(documents)
vector_db.add_documents(documents, embeddings.tolist())

# 查询
query = "什么是深度学习？"
query_embedding = embedding_manager.encode_texts([query])[0]
results = vector_db.search(query_embedding.tolist(), top_k=3)

print(f"查询: {query}")
print("搜索结果:")
for result in results['documents'][0]:
    print(f"- {result}")
```

### 5.3 语义搜索系统构建
**时长**：120分钟

#### 理论内容
- **语义搜索 vs 关键词搜索**
  - 传统搜索的局限性
  - 语义理解的优势
  - 混合搜索策略

- **系统架构设计**
  - 数据预处理流程
  - 索引构建策略
  - 查询处理优化
  - 结果排序算法

- **高级技术**
  - 多语言语义搜索
  - 跨模态检索
  - 实时索引更新
  - 个性化搜索

#### 实践操作
```python
# 完整的语义搜索系统
import os
import json
from datetime import datetime
from typing import List, Dict, Optional, Tuple
import logging

class SemanticSearchSystem:
    def __init__(self, embedding_model: str = "all-MiniLM-L6-v2", 
                 vector_db_type: str = "chroma"):
        self.embedding_manager = EmbeddingManager(embedding_model)
        self.vector_db = UnifiedVectorDB(vector_db_type)
        self.document_store = {}  # 存储原始文档信息
        self.logger = self._setup_logger()
    
    def _setup_logger(self):
        """设置日志记录"""
        logger = logging.getLogger("SemanticSearch")
        logger.setLevel(logging.INFO)
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        return logger
    
    def add_document(self, doc_id: str, content: str, metadata: Dict = None):
        """添加单个文档"""
        try:
            # 生成嵌入向量
            embedding = self.embedding_manager.encode_texts([content])[0]
            
            # 存储到向量数据库
            self.vector_db.add_documents([content], [embedding.tolist()])
            
            # 存储文档信息
            self.document_store[doc_id] = {
                'content': content,
                'metadata': metadata or {},
                'created_at': datetime.now().isoformat(),
                'embedding_model': self.embedding_manager.model_name
            }
            
            self.logger.info(f"文档 {doc_id} 添加成功")
            
        except Exception as e:
            self.logger.error(f"添加文档 {doc_id} 失败: {e}")
            raise
    
    def batch_add_documents(self, documents: List[Dict]):
        """批量添加文档"""
        contents = []
        doc_ids = []
        
        for doc in documents:
            doc_id = doc['id']
            content = doc['content']
            metadata = doc.get('metadata', {})
            
            contents.append(content)
            doc_ids.append(doc_id)
            
            # 存储文档信息
            self.document_store[doc_id] = {
                'content': content,
                'metadata': metadata,
                'created_at': datetime.now().isoformat(),
                'embedding_model': self.embedding_manager.model_name
            }
        
        # 批量生成嵌入向量
        embeddings = self.embedding_manager.encode_texts(contents)
        
        # 批量存储到向量数据库
        self.vector_db.add_documents(contents, embeddings.tolist())
        
        self.logger.info(f"批量添加 {len(documents)} 个文档成功")
    
    def search(self, query: str, top_k: int = 5, 
               filter_metadata: Dict = None) -> List[Dict]:
        """语义搜索"""
        try:
            # 生成查询向量
            query_embedding = self.embedding_manager.encode_texts([query])[0]
            
            # 向量搜索
            results = self.vector_db.search(query_embedding.tolist(), top_k)
            
            # 处理搜索结果
            formatted_results = []
            
            if self.vector_db.db_type == "chroma":
                documents = results['documents'][0]
                distances = results['distances'][0] if 'distances' in results else []
                
                for i, doc in enumerate(documents):
                    # 找到对应的文档ID
                    doc_id = self._find_document_id(doc)
                    
                    result = {
                        'document_id': doc_id,
                        'content': doc,
                        'similarity_score': 1 - distances[i] if distances else None,
                        'metadata': self.document_store.get(doc_id, {}).get('metadata', {})
                    }
                    
                    # 应用元数据过滤
                    if self._matches_filter(result['metadata'], filter_metadata):
                        formatted_results.append(result)
            
            self.logger.info(f"搜索查询 '{query}' 返回 {len(formatted_results)} 个结果")
            return formatted_results
            
        except Exception as e:
            self.logger.error(f"搜索失败: {e}")
            raise
    
    def _find_document_id(self, content: str) -> Optional[str]:
        """根据内容查找文档ID"""
        for doc_id, doc_info in self.document_store.items():
            if doc_info['content'] == content:
                return doc_id
        return None
    
    def _matches_filter(self, metadata: Dict, filter_metadata: Dict) -> bool:
        """检查元数据是否匹配过滤条件"""
        if not filter_metadata:
            return True
        
        for key, value in filter_metadata.items():
            if key not in metadata or metadata[key] != value:
                return False
        
        return True
    
    def get_document_info(self, doc_id: str) -> Optional[Dict]:
        """获取文档信息"""
        return self.document_store.get(doc_id)
    
    def update_document(self, doc_id: str, new_content: str, new_metadata: Dict = None):
        """更新文档（简化实现）"""
        if doc_id in self.document_store:
            # 删除旧文档并添加新文档
            del self.document_store[doc_id]
            self.add_document(doc_id, new_content, new_metadata)
            self.logger.info(f"文档 {doc_id} 更新成功")
        else:
            raise ValueError(f"文档 {doc_id} 不存在")
    
    def delete_document(self, doc_id: str):
        """删除文档（简化实现）"""
        if doc_id in self.document_store:
            del self.document_store[doc_id]
            self.logger.info(f"文档 {doc_id} 删除成功")
        else:
            raise ValueError(f"文档 {doc_id} 不存在")
    
    def get_statistics(self) -> Dict:
        """获取系统统计信息"""
        return {
            'total_documents': len(self.document_store),
            'embedding_model': self.embedding_manager.model_name,
            'vector_db_type': self.vector_db.db_type,
            'created_at': datetime.now().isoformat()
        }

# 使用示例
def demo_semantic_search():
    # 初始化搜索系统
    search_system = SemanticSearchSystem()
    
    # 准备测试文档
    documents = [
        {
            'id': 'doc1',
            'content': 'Python 是一种高级编程语言，广泛用于数据科学和机器学习',
            'metadata': {'category': 'programming', 'language': 'zh'}
        },
        {
            'id': 'doc2', 
            'content': '机器学习是人工智能的一个分支，让计算机能够从数据中学习',
            'metadata': {'category': 'ai', 'language': 'zh'}
        },
        {
            'id': 'doc3',
            'content': '深度学习使用神经网络来模拟人脑的学习过程',
            'metadata': {'category': 'ai', 'language': 'zh'}
        },
        {
            'id': 'doc4',
            'content': '自然语言处理帮助计算机理解和生成人类语言',
            'metadata': {'category': 'nlp', 'language': 'zh'}
        }
    ]
    
    # 批量添加文档
    search_system.batch_add_documents(documents)
    
    # 执行搜索
    queries = [
        "什么是机器学习？",
        "Python 编程语言",
        "神经网络如何工作？"
    ]
    
    for query in queries:
        print(f"\n查询: {query}")
        results = search_system.search(query, top_k=3)
        
        for i, result in enumerate(results, 1):
            print(f"{i}. 文档ID: {result['document_id']}")
            print(f"   相似度: {result['similarity_score']:.3f}")
            print(f"   内容: {result['content'][:50]}...")
            print(f"   类别: {result['metadata'].get('category', 'N/A')}")
    
    # 显示统计信息
    print(f"\n系统统计: {search_system.get_statistics()}")

if __name__ == "__main__":
    demo_semantic_search()
```

## 重点知识点

### 核心技术概念
1. **向量化技术**
   - 稠密向量 vs 稀疏向量
   - 上下文相关嵌入
   - 多粒度向量化

2. **相似度计算**
   - 余弦相似度
   - 欧氏距离
   - 曼哈顿距离
   - 点积相似度

3. **索引优化**
   - 近似最近邻算法
   - 量化技术
   - 分层索引

### 实际应用技巧
1. **性能优化**
   - 批量处理
   - 缓存策略
   - 索引调优
   - 查询优化

2. **质量提升**
   - 数据预处理
   - 模型选择
   - 结果重排序
   - 多模型融合

## 案例研究

### 案例1：企业文档搜索系统
**需求**：为大型企业构建内部文档搜索系统

**技术方案**：
- 多格式文档处理（PDF、Word、PPT）
- 分层索引结构
- 权限控制集成
- 搜索结果个性化

**关键技术**：
- 文档解析和预处理
- 语义分块策略
- 混合搜索算法
- 用户行为分析

### 案例2：智能客服知识库
**背景**：电商平台客服知识库升级

**实现要点**：
- 问题-答案对向量化
- 多轮对话上下文理解
- 实时知识更新
- 答案质量评估

## 练习项目

### 项目1：语义搜索引擎
**目标**：构建通用的语义搜索引擎

**功能要求**：
- 支持多种文档格式
- 实时索引更新
- 高级查询语法
- 搜索结果解释

### 项目2：文档相似度分析系统
**目标**：分析文档间的相似性关系

**功能要求**：
- 批量文档处理
- 相似度矩阵生成
- 聚类分析
- 可视化展示

## 参考资料

### 核心论文
1. "Efficient Estimation of Word Representations in Vector Space" - Word2Vec
2. "Sentence-BERT: Sentence Embeddings using Siamese BERT-Networks"
3. "Billion-scale similarity search with GPUs" - FAISS

### 技术文档
1. Sentence Transformers Documentation
2. Chroma Database Guide
3. Pinecone Vector Database Docs
4. FAISS Library Documentation

## 常见问题解答

**Q1：如何选择合适的嵌入模型？**
A1：考虑任务类型、语言支持、模型大小和性能要求。一般用途推荐 all-MiniLM-L6-v2，高质量需求用 all-mpnet-base-v2。

**Q2：向量数据库如何选择？**
A2：小规模用 Chroma，大规模用 Pinecone 或 Milvus，本地部署用 FAISS，图数据用 Weaviate。

**Q3：如何提高搜索质量？**
A3：优化数据预处理、选择合适的嵌入模型、调整相似度阈值、实现混合搜索策略。

---

**下一章预告**：第06章将学习 RAG（检索增强生成）与 GraphRAG 技术，掌握为 AI 构建强大知识库的方法。
