# 接口封装和统一管理策略

## 概述

随着AI模型的多样化发展，企业往往需要同时使用多个不同的模型服务。如何设计一个统一、灵活、可扩展的接口封装层，成为了AI应用开发的关键挑战。本章将深入探讨接口封装和统一管理的设计策略与实现方案。

## 设计目标与原则

### 核心设计目标

1. **统一接口**：为不同模型提供一致的调用接口
2. **灵活配置**：支持动态配置和模型切换
3. **高可用性**：实现负载均衡和故障转移
4. **可扩展性**：易于添加新的模型支持
5. **性能优化**：缓存、连接池、异步处理等优化

### 设计原则

- **开闭原则**：对扩展开放，对修改封闭
- **依赖倒置**：依赖抽象而非具体实现
- **单一职责**：每个组件只负责一个功能
- **接口隔离**：提供最小化的接口定义

## 统一接口抽象层设计

### 核心接口定义

```python
# unified_interface.py
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, AsyncGenerator, Union
from dataclasses import dataclass
from enum import Enum
import asyncio
from datetime import datetime

class ModelType(Enum):
    """模型类型枚举"""
    CHAT = "chat"
    COMPLETION = "completion"
    EMBEDDING = "embedding"
    IMAGE_GENERATION = "image_generation"
    SPEECH_TO_TEXT = "speech_to_text"
    TEXT_TO_SPEECH = "text_to_speech"

class ModelCapability(Enum):
    """模型能力枚举"""
    TEXT_GENERATION = "text_generation"
    CONVERSATION = "conversation"
    FUNCTION_CALLING = "function_calling"
    VISION = "vision"
    MULTIMODAL = "multimodal"
    STREAMING = "streaming"

@dataclass
class ModelInfo:
    """模型信息"""
    provider: str  # 提供商
    model_id: str  # 模型ID
    model_type: ModelType  # 模型类型
    capabilities: List[ModelCapability]  # 支持的能力
    max_tokens: int  # 最大token数
    cost_per_1k_tokens: float  # 每1K token成本
    supports_streaming: bool = False
    supports_function_calling: bool = False
    supports_vision: bool = False

@dataclass
class ChatMessage:
    """聊天消息"""
    role: str  # system, user, assistant
    content: str
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class ChatRequest:
    """聊天请求"""
    messages: List[ChatMessage]
    model: Optional[str] = None
    temperature: float = 0.7
    max_tokens: Optional[int] = None
    stream: bool = False
    functions: Optional[List[Dict[str, Any]]] = None
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class ChatResponse:
    """聊天响应"""
    content: str
    model: str
    usage: Dict[str, int]
    finish_reason: Optional[str] = None
    function_call: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None
    timestamp: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()

@dataclass
class EmbeddingRequest:
    """嵌入请求"""
    text: Union[str, List[str]]
    model: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class EmbeddingResponse:
    """嵌入响应"""
    embeddings: List[List[float]]
    model: str
    usage: Dict[str, int]
    metadata: Optional[Dict[str, Any]] = None
    timestamp: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()

class BaseModelProvider(ABC):
    """模型提供商基类"""
    
    def __init__(self, provider_name: str, config: Dict[str, Any]):
        self.provider_name = provider_name
        self.config = config
        self._models: Dict[str, ModelInfo] = {}
    
    @abstractmethod
    async def initialize(self) -> bool:
        """初始化提供商"""
        pass
    
    @abstractmethod
    async def chat_completion(self, request: ChatRequest) -> ChatResponse:
        """聊天完成"""
        pass
    
    @abstractmethod
    async def stream_chat_completion(self, request: ChatRequest) -> AsyncGenerator[str, None]:
        """流式聊天完成"""
        pass
    
    @abstractmethod
    async def create_embedding(self, request: EmbeddingRequest) -> EmbeddingResponse:
        """创建嵌入"""
        pass
    
    @abstractmethod
    async def get_available_models(self) -> List[ModelInfo]:
        """获取可用模型列表"""
        pass
    
    def register_model(self, model_info: ModelInfo):
        """注册模型"""
        self._models[model_info.model_id] = model_info
    
    def get_model_info(self, model_id: str) -> Optional[ModelInfo]:
        """获取模型信息"""
        return self._models.get(model_id)
    
    def supports_capability(self, model_id: str, capability: ModelCapability) -> bool:
        """检查模型是否支持特定能力"""
        model_info = self.get_model_info(model_id)
        return model_info and capability in model_info.capabilities

class UnifiedModelInterface:
    """统一模型接口"""
    
    def __init__(self):
        self.providers: Dict[str, BaseModelProvider] = {}
        self.model_registry: Dict[str, str] = {}  # model_id -> provider_name
        self.default_models: Dict[ModelType, str] = {}
        self.load_balancer = LoadBalancer()
        self.fallback_manager = FallbackManager()
    
    def register_provider(self, provider: BaseModelProvider):
        """注册模型提供商"""
        self.providers[provider.provider_name] = provider
    
    async def initialize_all_providers(self):
        """初始化所有提供商"""
        tasks = []
        for provider in self.providers.values():
            tasks.append(provider.initialize())
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for provider_name, result in zip(self.providers.keys(), results):
            if isinstance(result, Exception):
                print(f"提供商 {provider_name} 初始化失败: {result}")
            else:
                print(f"提供商 {provider_name} 初始化成功")
                await self._register_provider_models(provider_name)
    
    async def _register_provider_models(self, provider_name: str):
        """注册提供商的模型"""
        provider = self.providers[provider_name]
        models = await provider.get_available_models()
        
        for model in models:
            self.model_registry[model.model_id] = provider_name
            provider.register_model(model)
    
    def set_default_model(self, model_type: ModelType, model_id: str):
        """设置默认模型"""
        self.default_models[model_type] = model_id
    
    async def chat_completion(self, request: ChatRequest) -> ChatResponse:
        """统一聊天完成接口"""
        # 确定使用的模型
        model_id = request.model or self.default_models.get(ModelType.CHAT)
        if not model_id:
            raise ValueError("未指定模型且无默认聊天模型")
        
        # 获取提供商
        provider_name = self.model_registry.get(model_id)
        if not provider_name:
            raise ValueError(f"未找到模型 {model_id} 的提供商")
        
        provider = self.providers[provider_name]
        
        try:
            # 执行请求
            response = await provider.chat_completion(request)
            return response
            
        except Exception as e:
            # 尝试故障转移
            fallback_response = await self.fallback_manager.handle_failure(
                request, model_id, str(e), self
            )
            if fallback_response:
                return fallback_response
            raise e
    
    async def stream_chat_completion(self, request: ChatRequest) -> AsyncGenerator[str, None]:
        """统一流式聊天完成接口"""
        model_id = request.model or self.default_models.get(ModelType.CHAT)
        if not model_id:
            raise ValueError("未指定模型且无默认聊天模型")
        
        provider_name = self.model_registry.get(model_id)
        if not provider_name:
            raise ValueError(f"未找到模型 {model_id} 的提供商")
        
        provider = self.providers[provider_name]
        
        async for chunk in provider.stream_chat_completion(request):
            yield chunk
    
    async def create_embedding(self, request: EmbeddingRequest) -> EmbeddingResponse:
        """统一嵌入创建接口"""
        model_id = request.model or self.default_models.get(ModelType.EMBEDDING)
        if not model_id:
            raise ValueError("未指定模型且无默认嵌入模型")
        
        provider_name = self.model_registry.get(model_id)
        if not provider_name:
            raise ValueError(f"未找到模型 {model_id} 的提供商")
        
        provider = self.providers[provider_name]
        return await provider.create_embedding(request)
    
    def get_available_models(self, model_type: Optional[ModelType] = None) -> List[ModelInfo]:
        """获取可用模型列表"""
        all_models = []
        
        for provider in self.providers.values():
            for model_info in provider._models.values():
                if model_type is None or model_info.model_type == model_type:
                    all_models.append(model_info)
        
        return all_models
    
    def get_model_info(self, model_id: str) -> Optional[ModelInfo]:
        """获取模型信息"""
        provider_name = self.model_registry.get(model_id)
        if provider_name:
            provider = self.providers[provider_name]
            return provider.get_model_info(model_id)
        return None

class LoadBalancer:
    """负载均衡器"""
    
    def __init__(self):
        self.model_weights: Dict[str, Dict[str, float]] = {}
        self.request_counts: Dict[str, int] = {}
    
    def set_model_weight(self, model_id: str, provider: str, weight: float):
        """设置模型权重"""
        if model_id not in self.model_weights:
            self.model_weights[model_id] = {}
        self.model_weights[model_id][provider] = weight
    
    def select_provider(self, model_id: str, available_providers: List[str]) -> str:
        """选择提供商"""
        if model_id not in self.model_weights:
            # 简单轮询
            return self._round_robin_select(available_providers)
        
        # 加权选择
        weights = self.model_weights[model_id]
        total_weight = sum(weights.get(p, 1.0) for p in available_providers)
        
        import random
        rand_val = random.uniform(0, total_weight)
        current_weight = 0
        
        for provider in available_providers:
            current_weight += weights.get(provider, 1.0)
            if rand_val <= current_weight:
                return provider
        
        return available_providers[0]
    
    def _round_robin_select(self, providers: List[str]) -> str:
        """轮询选择"""
        if not providers:
            raise ValueError("没有可用的提供商")
        
        # 简化的轮询实现
        total_requests = sum(self.request_counts.values())
        return providers[total_requests % len(providers)]

class FallbackManager:
    """故障转移管理器"""
    
    def __init__(self):
        self.fallback_rules: Dict[str, List[str]] = {}
        self.failure_counts: Dict[str, int] = {}
        self.circuit_breakers: Dict[str, bool] = {}
    
    def set_fallback_chain(self, primary_model: str, fallback_models: List[str]):
        """设置故障转移链"""
        self.fallback_rules[primary_model] = fallback_models
    
    async def handle_failure(self, 
                           request: ChatRequest, 
                           failed_model: str, 
                           error: str,
                           interface: UnifiedModelInterface) -> Optional[ChatResponse]:
        """处理故障"""
        # 记录失败
        self.failure_counts[failed_model] = self.failure_counts.get(failed_model, 0) + 1
        
        # 检查是否有故障转移规则
        if failed_model not in self.fallback_rules:
            return None
        
        # 尝试故障转移模型
        for fallback_model in self.fallback_rules[failed_model]:
            if self.circuit_breakers.get(fallback_model, False):
                continue  # 跳过已熔断的模型
            
            try:
                # 创建新请求
                fallback_request = ChatRequest(
                    messages=request.messages,
                    model=fallback_model,
                    temperature=request.temperature,
                    max_tokens=request.max_tokens,
                    stream=False,  # 故障转移时不使用流式
                    functions=request.functions,
                    metadata=request.metadata
                )
                
                # 直接调用提供商（避免递归）
                provider_name = interface.model_registry.get(fallback_model)
                if provider_name:
                    provider = interface.providers[provider_name]
                    response = await provider.chat_completion(fallback_request)
                    
                    # 添加故障转移信息
                    if response.metadata is None:
                        response.metadata = {}
                    response.metadata["fallback_from"] = failed_model
                    response.metadata["fallback_reason"] = error
                    
                    return response
                    
            except Exception as fallback_error:
                print(f"故障转移模型 {fallback_model} 也失败: {fallback_error}")
                continue
        
        return None
    
    def reset_circuit_breaker(self, model_id: str):
        """重置熔断器"""
        self.circuit_breakers[model_id] = False
        self.failure_counts[model_id] = 0
```

### 具体提供商实现

```python
# openai_provider.py
import openai
from openai import OpenAI
import asyncio
from typing import List, AsyncGenerator

class OpenAIProvider(BaseModelProvider):
    """OpenAI提供商实现"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("openai", config)
        self.client = None
        self.api_key = config.get("api_key")
        self.base_url = config.get("base_url", "https://api.openai.com/v1")
    
    async def initialize(self) -> bool:
        """初始化OpenAI客户端"""
        try:
            self.client = OpenAI(
                api_key=self.api_key,
                base_url=self.base_url
            )
            
            # 注册支持的模型
            self._register_openai_models()
            return True
            
        except Exception as e:
            print(f"OpenAI初始化失败: {e}")
            return False
    
    def _register_openai_models(self):
        """注册OpenAI模型"""
        models = [
            ModelInfo(
                provider="openai",
                model_id="gpt-4",
                model_type=ModelType.CHAT,
                capabilities=[
                    ModelCapability.TEXT_GENERATION,
                    ModelCapability.CONVERSATION,
                    ModelCapability.FUNCTION_CALLING
                ],
                max_tokens=8192,
                cost_per_1k_tokens=0.03,
                supports_streaming=True,
                supports_function_calling=True
            ),
            ModelInfo(
                provider="openai",
                model_id="gpt-3.5-turbo",
                model_type=ModelType.CHAT,
                capabilities=[
                    ModelCapability.TEXT_GENERATION,
                    ModelCapability.CONVERSATION,
                    ModelCapability.FUNCTION_CALLING
                ],
                max_tokens=4096,
                cost_per_1k_tokens=0.002,
                supports_streaming=True,
                supports_function_calling=True
            ),
            ModelInfo(
                provider="openai",
                model_id="text-embedding-ada-002",
                model_type=ModelType.EMBEDDING,
                capabilities=[ModelCapability.TEXT_GENERATION],
                max_tokens=8191,
                cost_per_1k_tokens=0.0001
            )
        ]
        
        for model in models:
            self.register_model(model)
    
    async def chat_completion(self, request: ChatRequest) -> ChatResponse:
        """聊天完成实现"""
        try:
            # 转换消息格式
            messages = [
                {"role": msg.role, "content": msg.content}
                for msg in request.messages
            ]
            
            # 构建请求参数
            params = {
                "model": request.model or "gpt-3.5-turbo",
                "messages": messages,
                "temperature": request.temperature,
                "stream": False
            }
            
            if request.max_tokens:
                params["max_tokens"] = request.max_tokens
            
            if request.functions:
                params["functions"] = request.functions
            
            # 调用API
            response = self.client.chat.completions.create(**params)
            
            # 转换响应格式
            return ChatResponse(
                content=response.choices[0].message.content or "",
                model=response.model,
                usage={
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                },
                finish_reason=response.choices[0].finish_reason,
                function_call=getattr(response.choices[0].message, 'function_call', None)
            )
            
        except Exception as e:
            raise Exception(f"OpenAI聊天完成失败: {e}")
    
    async def stream_chat_completion(self, request: ChatRequest) -> AsyncGenerator[str, None]:
        """流式聊天完成实现"""
        try:
            messages = [
                {"role": msg.role, "content": msg.content}
                for msg in request.messages
            ]
            
            params = {
                "model": request.model or "gpt-3.5-turbo",
                "messages": messages,
                "temperature": request.temperature,
                "stream": True
            }
            
            if request.max_tokens:
                params["max_tokens"] = request.max_tokens
            
            response = self.client.chat.completions.create(**params)
            
            for chunk in response:
                if chunk.choices[0].delta.content is not None:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            raise Exception(f"OpenAI流式聊天失败: {e}")
    
    async def create_embedding(self, request: EmbeddingRequest) -> EmbeddingResponse:
        """创建嵌入实现"""
        try:
            response = self.client.embeddings.create(
                model=request.model or "text-embedding-ada-002",
                input=request.text
            )
            
            embeddings = [data.embedding for data in response.data]
            
            return EmbeddingResponse(
                embeddings=embeddings,
                model=response.model,
                usage={
                    "prompt_tokens": response.usage.prompt_tokens,
                    "total_tokens": response.usage.total_tokens
                }
            )
            
        except Exception as e:
            raise Exception(f"OpenAI嵌入创建失败: {e}")
    
    async def get_available_models(self) -> List[ModelInfo]:
        """获取可用模型列表"""
        return list(self._models.values())

# Claude提供商实现
class ClaudeProvider(BaseModelProvider):
    """Claude提供商实现"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("claude", config)
        self.api_key = config.get("api_key")
        self.client = None
    
    async def initialize(self) -> bool:
        """初始化Claude客户端"""
        try:
            import anthropic
            self.client = anthropic.Anthropic(api_key=self.api_key)
            self._register_claude_models()
            return True
        except Exception as e:
            print(f"Claude初始化失败: {e}")
            return False
    
    def _register_claude_models(self):
        """注册Claude模型"""
        models = [
            ModelInfo(
                provider="claude",
                model_id="claude-3-opus-20240229",
                model_type=ModelType.CHAT,
                capabilities=[
                    ModelCapability.TEXT_GENERATION,
                    ModelCapability.CONVERSATION
                ],
                max_tokens=200000,
                cost_per_1k_tokens=0.015,
                supports_streaming=True
            ),
            ModelInfo(
                provider="claude",
                model_id="claude-3-sonnet-20240229",
                model_type=ModelType.CHAT,
                capabilities=[
                    ModelCapability.TEXT_GENERATION,
                    ModelCapability.CONVERSATION
                ],
                max_tokens=200000,
                cost_per_1k_tokens=0.003,
                supports_streaming=True
            )
        ]
        
        for model in models:
            self.register_model(model)
    
    async def chat_completion(self, request: ChatRequest) -> ChatResponse:
        """Claude聊天完成实现"""
        try:
            messages = [
                {"role": msg.role, "content": msg.content}
                for msg in request.messages
                if msg.role != "system"  # Claude处理system消息的方式不同
            ]
            
            # 提取system消息
            system_message = None
            for msg in request.messages:
                if msg.role == "system":
                    system_message = msg.content
                    break
            
            params = {
                "model": request.model or "claude-3-sonnet-20240229",
                "messages": messages,
                "max_tokens": request.max_tokens or 1000,
                "temperature": request.temperature
            }
            
            if system_message:
                params["system"] = system_message
            
            response = self.client.messages.create(**params)
            
            return ChatResponse(
                content=response.content[0].text if response.content else "",
                model=response.model,
                usage={
                    "prompt_tokens": response.usage.input_tokens,
                    "completion_tokens": response.usage.output_tokens,
                    "total_tokens": response.usage.input_tokens + response.usage.output_tokens
                },
                finish_reason=response.stop_reason
            )
            
        except Exception as e:
            raise Exception(f"Claude聊天完成失败: {e}")
    
    async def stream_chat_completion(self, request: ChatRequest) -> AsyncGenerator[str, None]:
        """Claude流式聊天完成"""
        # Claude流式实现
        yield "Claude流式功能待实现"
    
    async def create_embedding(self, request: EmbeddingRequest) -> EmbeddingResponse:
        """Claude不支持嵌入"""
        raise NotImplementedError("Claude不支持嵌入功能")
    
    async def get_available_models(self) -> List[ModelInfo]:
        """获取可用模型列表"""
        return list(self._models.values())

# 使用示例
async def demo_unified_interface():
    """统一接口使用示例"""
    # 创建统一接口
    interface = UnifiedModelInterface()
    
    # 注册提供商
    openai_provider = OpenAIProvider({
        "api_key": "your-openai-key"
    })
    
    claude_provider = ClaudeProvider({
        "api_key": "your-claude-key"
    })
    
    interface.register_provider(openai_provider)
    interface.register_provider(claude_provider)
    
    # 初始化所有提供商
    await interface.initialize_all_providers()
    
    # 设置默认模型
    interface.set_default_model(ModelType.CHAT, "gpt-3.5-turbo")
    interface.set_default_model(ModelType.EMBEDDING, "text-embedding-ada-002")
    
    # 设置故障转移
    interface.fallback_manager.set_fallback_chain(
        "gpt-4", 
        ["gpt-3.5-turbo", "claude-3-sonnet-20240229"]
    )
    
    # 使用统一接口
    request = ChatRequest(
        messages=[
            ChatMessage(role="user", content="你好，请介绍一下人工智能")
        ],
        temperature=0.7
    )
    
    # 聊天完成
    response = await interface.chat_completion(request)
    print(f"响应: {response.content}")
    print(f"使用模型: {response.model}")
    print(f"Token使用: {response.usage}")
    
    # 流式聊天
    print("\n流式响应:")
    async for chunk in interface.stream_chat_completion(request):
        print(chunk, end="")
    
    # 获取可用模型
    models = interface.get_available_models(ModelType.CHAT)
    print(f"\n可用聊天模型: {[m.model_id for m in models]}")

if __name__ == "__main__":
    asyncio.run(demo_unified_interface())
```

这个统一接口封装设计提供了完整的多模型管理解决方案，包括抽象接口定义、具体提供商实现、负载均衡、故障转移等核心功能，为构建企业级AI应用提供了坚实的基础架构。
