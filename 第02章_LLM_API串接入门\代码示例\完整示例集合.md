# 第02章 完整代码示例集合

## 概述

本文档包含第02章"LLM API串接入门"的所有完整代码示例，按功能模块组织，便于学员参考和实践。

## 目录结构

```
代码示例/
├── 基础配置/
│   ├── config_manager.py          # 配置管理
│   ├── environment_setup.py       # 环境设置
│   └── api_key_manager.py         # API密钥管理
├── API客户端/
│   ├── base_client.py             # 基础客户端
│   ├── openai_client.py           # OpenAI客户端
│   ├── claude_client.py           # Claude客户端
│   ├── gemini_client.py           # Gemini客户端
│   └── multi_provider_client.py   # 多厂商客户端
├── 高级功能/
│   ├── streaming_client.py        # 流式响应
│   ├── function_calling.py        # 函数调用
│   ├── conversation_manager.py    # 对话管理
│   └── token_optimizer.py         # Token优化
├── 工具类/
│   ├── error_handler.py           # 错误处理
│   ├── rate_limiter.py            # 速率限制
│   ├── cache_manager.py           # 缓存管理
│   └── metrics_collector.py       # 指标收集
└── 完整应用/
    ├── chatbot_demo.py            # 聊天机器人演示
    ├── translation_demo.py        # 翻译服务演示
    └── performance_test_demo.py   # 性能测试演示
```

## 1. 基础配置模块

### config_manager.py

```python
"""
配置管理器 - 统一管理所有配置信息
"""
import os
import json
import yaml
from typing import Dict, Any, Optional
from dataclasses import dataclass
from pathlib import Path

@dataclass
class APIConfig:
    """API配置"""
    api_key: str
    base_url: str
    timeout: int = 30
    max_retries: int = 3
    model: str = ""

@dataclass
class AppConfig:
    """应用配置"""
    debug: bool = False
    log_level: str = "INFO"
    cache_enabled: bool = True
    cache_ttl: int = 3600

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or "config.yaml"
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        config_path = Path(self.config_file)
        
        if not config_path.exists():
            return self._create_default_config()
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.suffix.lower() == '.yaml':
                    return yaml.safe_load(f)
                elif config_path.suffix.lower() == '.json':
                    return json.load(f)
                else:
                    raise ValueError(f"不支持的配置文件格式: {config_path.suffix}")
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return self._create_default_config()
    
    def _create_default_config(self) -> Dict[str, Any]:
        """创建默认配置"""
        default_config = {
            'app': {
                'debug': False,
                'log_level': 'INFO',
                'cache_enabled': True,
                'cache_ttl': 3600
            },
            'apis': {
                'openai': {
                    'api_key': os.getenv('OPENAI_API_KEY', ''),
                    'base_url': 'https://api.openai.com/v1',
                    'timeout': 30,
                    'max_retries': 3,
                    'model': 'gpt-3.5-turbo'
                },
                'claude': {
                    'api_key': os.getenv('ANTHROPIC_API_KEY', ''),
                    'base_url': 'https://api.anthropic.com',
                    'timeout': 30,
                    'max_retries': 3,
                    'model': 'claude-3-sonnet-20240229'
                },
                'gemini': {
                    'api_key': os.getenv('GOOGLE_API_KEY', ''),
                    'base_url': 'https://generativelanguage.googleapis.com',
                    'timeout': 30,
                    'max_retries': 3,
                    'model': 'gemini-pro'
                }
            }
        }
        
        # 保存默认配置
        self._save_config(default_config)
        return default_config
    
    def _save_config(self, config: Dict[str, Any]):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def get_api_config(self, provider: str) -> APIConfig:
        """获取API配置"""
        api_configs = self.config.get('apis', {})
        provider_config = api_configs.get(provider, {})
        
        return APIConfig(
            api_key=provider_config.get('api_key', ''),
            base_url=provider_config.get('base_url', ''),
            timeout=provider_config.get('timeout', 30),
            max_retries=provider_config.get('max_retries', 3),
            model=provider_config.get('model', '')
        )
    
    def get_app_config(self) -> AppConfig:
        """获取应用配置"""
        app_config = self.config.get('app', {})
        
        return AppConfig(
            debug=app_config.get('debug', False),
            log_level=app_config.get('log_level', 'INFO'),
            cache_enabled=app_config.get('cache_enabled', True),
            cache_ttl=app_config.get('cache_ttl', 3600)
        )
    
    def update_config(self, section: str, key: str, value: Any):
        """更新配置"""
        if section not in self.config:
            self.config[section] = {}
        
        self.config[section][key] = value
        self._save_config(self.config)
    
    def validate_config(self) -> Dict[str, bool]:
        """验证配置"""
        validation_results = {}
        
        # 验证API配置
        for provider in ['openai', 'claude', 'gemini']:
            api_config = self.get_api_config(provider)
            validation_results[provider] = bool(api_config.api_key)
        
        return validation_results

# 全局配置实例
config_manager = ConfigManager()
```

### environment_setup.py

```python
"""
环境设置工具 - 自动化环境配置和验证
"""
import os
import sys
import subprocess
import importlib
from typing import List, Tuple, Dict
from pathlib import Path

class EnvironmentSetup:
    """环境设置工具"""
    
    def __init__(self):
        self.required_packages = [
            'openai>=1.0.0',
            'anthropic>=0.3.0',
            'google-generativeai>=0.3.0',
            'requests>=2.28.0',
            'python-dotenv>=1.0.0',
            'pydantic>=2.0.0',
            'fastapi>=0.100.0',
            'uvicorn>=0.20.0'
        ]
        
        self.optional_packages = [
            'streamlit>=1.28.0',
            'gradio>=3.50.0',
            'pandas>=1.5.0',
            'matplotlib>=3.6.0',
            'seaborn>=0.12.0'
        ]
    
    def check_python_version(self) -> Tuple[bool, str]:
        """检查Python版本"""
        version = sys.version_info
        if version.major >= 3 and version.minor >= 8:
            return True, f"Python {version.major}.{version.minor}.{version.micro} ✓"
        else:
            return False, f"Python版本过低: {version.major}.{version.minor}.{version.micro} (需要3.8+)"
    
    def check_packages(self, packages: List[str]) -> List[Tuple[str, bool, str]]:
        """检查包安装状态"""
        results = []
        
        for package_spec in packages:
            package_name = package_spec.split('>=')[0].split('==')[0]
            
            try:
                importlib.import_module(package_name.replace('-', '_'))
                results.append((package_name, True, "已安装 ✓"))
            except ImportError:
                results.append((package_name, False, "未安装 ✗"))
        
        return results
    
    def install_packages(self, packages: List[str], force: bool = False) -> bool:
        """安装包"""
        try:
            cmd = [sys.executable, '-m', 'pip', 'install']
            if force:
                cmd.append('--force-reinstall')
            cmd.extend(packages)
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0
        except Exception as e:
            print(f"安装包时出错: {e}")
            return False
    
    def create_env_file(self, env_file: str = '.env') -> bool:
        """创建环境变量文件"""
        env_template = """# LLM API 配置
OPENAI_API_KEY=your-openai-api-key-here
ANTHROPIC_API_KEY=your-anthropic-api-key-here
GOOGLE_API_KEY=your-google-api-key-here

# 应用配置
DEBUG=False
LOG_LEVEL=INFO
CACHE_ENABLED=True
CACHE_TTL=3600

# 服务配置
API_HOST=0.0.0.0
API_PORT=8000
"""
        
        try:
            if not os.path.exists(env_file):
                with open(env_file, 'w', encoding='utf-8') as f:
                    f.write(env_template)
                print(f"已创建环境变量文件: {env_file}")
                return True
            else:
                print(f"环境变量文件已存在: {env_file}")
                return True
        except Exception as e:
            print(f"创建环境变量文件失败: {e}")
            return False
    
    def create_project_structure(self, project_dir: str = ".") -> bool:
        """创建项目目录结构"""
        directories = [
            "src",
            "src/api_clients",
            "src/utils",
            "src/examples",
            "tests",
            "config",
            "logs",
            "data",
            "docs"
        ]
        
        try:
            project_path = Path(project_dir)
            for directory in directories:
                dir_path = project_path / directory
                dir_path.mkdir(parents=True, exist_ok=True)
                
                # 创建__init__.py文件
                if directory.startswith("src"):
                    init_file = dir_path / "__init__.py"
                    if not init_file.exists():
                        init_file.touch()
            
            print(f"项目目录结构已创建: {project_path.absolute()}")
            return True
        except Exception as e:
            print(f"创建项目目录结构失败: {e}")
            return False
    
    def setup_complete_environment(self, project_dir: str = ".", 
                                 install_optional: bool = False) -> Dict[str, bool]:
        """完整环境设置"""
        results = {}
        
        print("=== LLM API 开发环境设置 ===\n")
        
        # 1. 检查Python版本
        python_ok, python_msg = self.check_python_version()
        print(f"Python版本检查: {python_msg}")
        results['python_version'] = python_ok
        
        # 2. 创建项目结构
        print("\n创建项目目录结构...")
        results['project_structure'] = self.create_project_structure(project_dir)
        
        # 3. 创建环境变量文件
        print("\n创建环境变量文件...")
        results['env_file'] = self.create_env_file(os.path.join(project_dir, '.env'))
        
        # 4. 检查必需包
        print("\n检查必需包...")
        required_results = self.check_packages(self.required_packages)
        missing_required = [pkg for pkg, ok, _ in required_results if not ok]
        
        if missing_required:
            print(f"安装缺失的必需包: {missing_required}")
            results['required_packages'] = self.install_packages(missing_required)
        else:
            print("所有必需包已安装 ✓")
            results['required_packages'] = True
        
        # 5. 检查可选包
        if install_optional:
            print("\n检查可选包...")
            optional_results = self.check_packages(self.optional_packages)
            missing_optional = [pkg for pkg, ok, _ in optional_results if not ok]
            
            if missing_optional:
                print(f"安装缺失的可选包: {missing_optional}")
                results['optional_packages'] = self.install_packages(missing_optional)
            else:
                print("所有可选包已安装 ✓")
                results['optional_packages'] = True
        
        # 6. 总结
        print(f"\n=== 环境设置完成 ===")
        all_success = all(results.values())
        if all_success:
            print("✓ 环境设置成功！可以开始开发了。")
        else:
            print("✗ 部分设置失败，请检查错误信息。")
        
        return results

# 使用示例
def setup_development_environment():
    """设置开发环境"""
    setup = EnvironmentSetup()
    results = setup.setup_complete_environment(
        project_dir="llm_api_project",
        install_optional=True
    )
    
    return results

if __name__ == "__main__":
    setup_development_environment()
```

## 2. API客户端模块

### base_client.py

```python
"""
基础API客户端 - 所有API客户端的基类
"""
import time
import json
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModelType(Enum):
    """模型类型"""
    CHAT = "chat"
    COMPLETION = "completion"
    EMBEDDING = "embedding"

@dataclass
class APIResponse:
    """统一API响应格式"""
    content: str
    model: str
    usage: Dict[str, int]
    finish_reason: str
    provider: str
    response_time: float
    metadata: Dict[str, Any] = None

class RateLimiter:
    """简单的速率限制器"""
    
    def __init__(self, max_requests: int = 60, time_window: int = 60):
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = []
    
    def can_make_request(self) -> bool:
        """检查是否可以发起请求"""
        now = time.time()
        
        # 清理过期的请求记录
        self.requests = [req_time for req_time in self.requests 
                        if now - req_time < self.time_window]
        
        return len(self.requests) < self.max_requests
    
    def record_request(self):
        """记录请求"""
        self.requests.append(time.time())
    
    def wait_time(self) -> float:
        """计算需要等待的时间"""
        if self.can_make_request():
            return 0.0
        
        oldest_request = min(self.requests)
        return self.time_window - (time.time() - oldest_request)

class BaseAPIClient(ABC):
    """基础API客户端抽象类"""
    
    def __init__(self, api_key: str, base_url: str, timeout: int = 30, 
                 max_retries: int = 3, **kwargs):
        self.api_key = api_key
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.max_retries = max_retries
        
        # 创建会话
        self.session = self._create_session()
        
        # 速率限制器
        self.rate_limiter = RateLimiter(
            max_requests=kwargs.get('rate_limit', 60),
            time_window=60
        )
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_cost': 0.0,
            'total_tokens': 0
        }
    
    def _create_session(self) -> requests.Session:
        """创建HTTP会话"""
        session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=self.max_retries,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "POST"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session
    
    def _wait_for_rate_limit(self):
        """等待速率限制"""
        if not self.rate_limiter.can_make_request():
            wait_time = self.rate_limiter.wait_time()
            logger.warning(f"速率限制，等待 {wait_time:.1f} 秒")
            time.sleep(wait_time)
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """发起HTTP请求"""
        self._wait_for_rate_limit()
        
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        headers = self._get_headers()
        
        if 'headers' in kwargs:
            headers.update(kwargs.pop('headers'))
        
        start_time = time.time()
        
        try:
            response = self.session.request(
                method=method,
                url=url,
                headers=headers,
                timeout=self.timeout,
                **kwargs
            )
            
            response_time = time.time() - start_time
            
            # 记录请求
            self.rate_limiter.record_request()
            self.stats['total_requests'] += 1
            
            # 处理响应
            if response.status_code == 429:
                retry_after = int(response.headers.get('Retry-After', 60))
                logger.warning(f"API速率限制，等待 {retry_after} 秒")
                time.sleep(retry_after)
                return self._make_request(method, endpoint, **kwargs)
            
            response.raise_for_status()
            self.stats['successful_requests'] += 1
            
            logger.debug(f"请求成功: {method} {url} ({response_time:.2f}s)")
            return response
            
        except requests.exceptions.RequestException as e:
            self.stats['failed_requests'] += 1
            logger.error(f"请求失败: {method} {url} - {str(e)}")
            raise
    
    @abstractmethod
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        pass
    
    @abstractmethod
    def chat(self, messages: List[Dict[str, str]], model: str = None, 
             **kwargs) -> APIResponse:
        """聊天接口"""
        pass
    
    @abstractmethod
    def get_available_models(self) -> List[str]:
        """获取可用模型列表"""
        pass
    
    @abstractmethod
    def estimate_cost(self, input_tokens: int, output_tokens: int, 
                     model: str) -> float:
        """估算成本"""
        pass
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.stats.copy()
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_cost': 0.0,
            'total_tokens': 0
        }
    
    def health_check(self) -> bool:
        """健康检查"""
        try:
            models = self.get_available_models()
            return len(models) > 0
        except Exception:
            return False
```

## 3. 高级功能模块

### conversation_manager.py

```python
"""
对话管理器 - 管理多轮对话和上下文
"""
import json
import hashlib
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from collections import defaultdict

@dataclass
class Message:
    """消息类"""
    role: str  # system, user, assistant
    content: str
    timestamp: datetime
    metadata: Dict[str, Any] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'role': self.role,
            'content': self.content,
            'timestamp': self.timestamp.isoformat(),
            'metadata': self.metadata or {}
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Message':
        """从字典创建"""
        return cls(
            role=data['role'],
            content=data['content'],
            timestamp=datetime.fromisoformat(data['timestamp']),
            metadata=data.get('metadata', {})
        )

@dataclass
class Conversation:
    """对话类"""
    session_id: str
    messages: List[Message]
    created_at: datetime
    updated_at: datetime
    metadata: Dict[str, Any] = None
    
    def add_message(self, message: Message):
        """添加消息"""
        self.messages.append(message)
        self.updated_at = datetime.now()
    
    def get_recent_messages(self, count: int = 10) -> List[Message]:
        """获取最近的消息"""
        return self.messages[-count:] if count > 0 else self.messages
    
    def get_messages_by_role(self, role: str) -> List[Message]:
        """按角色获取消息"""
        return [msg for msg in self.messages if msg.role == role]
    
    def to_api_format(self, max_messages: int = None) -> List[Dict[str, str]]:
        """转换为API格式"""
        messages = self.messages if max_messages is None else self.messages[-max_messages:]
        return [{'role': msg.role, 'content': msg.content} for msg in messages]

class ConversationManager:
    """对话管理器"""
    
    def __init__(self, max_conversations: int = 100, 
                 max_messages_per_conversation: int = 50,
                 conversation_ttl: timedelta = timedelta(hours=24)):
        self.max_conversations = max_conversations
        self.max_messages_per_conversation = max_messages_per_conversation
        self.conversation_ttl = conversation_ttl
        
        self.conversations: Dict[str, Conversation] = {}
        self.user_sessions: Dict[str, List[str]] = defaultdict(list)
    
    def create_session(self, user_id: str = None, 
                      system_prompt: str = None) -> str:
        """创建新会话"""
        session_id = self._generate_session_id(user_id)
        
        messages = []
        if system_prompt:
            messages.append(Message(
                role="system",
                content=system_prompt,
                timestamp=datetime.now()
            ))
        
        conversation = Conversation(
            session_id=session_id,
            messages=messages,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            metadata={'user_id': user_id} if user_id else {}
        )
        
        self.conversations[session_id] = conversation
        
        if user_id:
            self.user_sessions[user_id].append(session_id)
        
        # 清理过期会话
        self._cleanup_expired_conversations()
        
        return session_id
    
    def add_message(self, session_id: str, role: str, content: str, 
                   metadata: Dict[str, Any] = None) -> bool:
        """添加消息到会话"""
        if session_id not in self.conversations:
            return False
        
        conversation = self.conversations[session_id]
        
        message = Message(
            role=role,
            content=content,
            timestamp=datetime.now(),
            metadata=metadata
        )
        
        conversation.add_message(message)
        
        # 限制消息数量
        if len(conversation.messages) > self.max_messages_per_conversation:
            # 保留系统消息和最近的消息
            system_messages = [msg for msg in conversation.messages if msg.role == "system"]
            recent_messages = [msg for msg in conversation.messages if msg.role != "system"][-self.max_messages_per_conversation + len(system_messages):]
            conversation.messages = system_messages + recent_messages
        
        return True
    
    def get_conversation(self, session_id: str) -> Optional[Conversation]:
        """获取会话"""
        return self.conversations.get(session_id)
    
    def get_messages(self, session_id: str, 
                    max_messages: int = None) -> List[Dict[str, str]]:
        """获取会话消息（API格式）"""
        conversation = self.get_conversation(session_id)
        if not conversation:
            return []
        
        return conversation.to_api_format(max_messages)
    
    def delete_session(self, session_id: str) -> bool:
        """删除会话"""
        if session_id in self.conversations:
            conversation = self.conversations[session_id]
            user_id = conversation.metadata.get('user_id')
            
            del self.conversations[session_id]
            
            if user_id and session_id in self.user_sessions[user_id]:
                self.user_sessions[user_id].remove(session_id)
            
            return True
        return False
    
    def get_user_sessions(self, user_id: str) -> List[str]:
        """获取用户的所有会话"""
        return self.user_sessions.get(user_id, [])
    
    def export_conversation(self, session_id: str) -> Optional[str]:
        """导出会话为JSON"""
        conversation = self.get_conversation(session_id)
        if not conversation:
            return None
        
        export_data = {
            'session_id': conversation.session_id,
            'created_at': conversation.created_at.isoformat(),
            'updated_at': conversation.updated_at.isoformat(),
            'metadata': conversation.metadata,
            'messages': [msg.to_dict() for msg in conversation.messages]
        }
        
        return json.dumps(export_data, ensure_ascii=False, indent=2)
    
    def import_conversation(self, data: str) -> bool:
        """从JSON导入会话"""
        try:
            import_data = json.loads(data)
            
            messages = [Message.from_dict(msg_data) for msg_data in import_data['messages']]
            
            conversation = Conversation(
                session_id=import_data['session_id'],
                messages=messages,
                created_at=datetime.fromisoformat(import_data['created_at']),
                updated_at=datetime.fromisoformat(import_data['updated_at']),
                metadata=import_data.get('metadata', {})
            )
            
            self.conversations[conversation.session_id] = conversation
            
            user_id = conversation.metadata.get('user_id')
            if user_id:
                self.user_sessions[user_id].append(conversation.session_id)
            
            return True
        except Exception as e:
            print(f"导入会话失败: {e}")
            return False
    
    def get_conversation_summary(self, session_id: str) -> Dict[str, Any]:
        """获取会话摘要"""
        conversation = self.get_conversation(session_id)
        if not conversation:
            return {}
        
        user_messages = conversation.get_messages_by_role("user")
        assistant_messages = conversation.get_messages_by_role("assistant")
        
        return {
            'session_id': session_id,
            'message_count': len(conversation.messages),
            'user_message_count': len(user_messages),
            'assistant_message_count': len(assistant_messages),
            'created_at': conversation.created_at.isoformat(),
            'updated_at': conversation.updated_at.isoformat(),
            'duration': str(conversation.updated_at - conversation.created_at),
            'metadata': conversation.metadata
        }
    
    def _generate_session_id(self, user_id: str = None) -> str:
        """生成会话ID"""
        timestamp = datetime.now().isoformat()
        content = f"{user_id or 'anonymous'}_{timestamp}"
        return hashlib.md5(content.encode()).hexdigest()[:16]
    
    def _cleanup_expired_conversations(self):
        """清理过期会话"""
        now = datetime.now()
        expired_sessions = []
        
        for session_id, conversation in self.conversations.items():
            if now - conversation.updated_at > self.conversation_ttl:
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            self.delete_session(session_id)
        
        # 限制会话总数
        if len(self.conversations) > self.max_conversations:
            # 删除最旧的会话
            sorted_conversations = sorted(
                self.conversations.items(),
                key=lambda x: x[1].updated_at
            )
            
            excess_count = len(self.conversations) - self.max_conversations
            for session_id, _ in sorted_conversations[:excess_count]:
                self.delete_session(session_id)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        total_messages = sum(len(conv.messages) for conv in self.conversations.values())
        
        return {
            'total_conversations': len(self.conversations),
            'total_messages': total_messages,
            'active_users': len(self.user_sessions),
            'avg_messages_per_conversation': total_messages / len(self.conversations) if self.conversations else 0
        }

# 使用示例
def demo_conversation_manager():
    """对话管理器演示"""
    manager = ConversationManager()
    
    # 创建会话
    session_id = manager.create_session(
        user_id="user123",
        system_prompt="你是一个有用的AI助手。"
    )
    print(f"创建会话: {session_id}")
    
    # 添加消息
    manager.add_message(session_id, "user", "你好！")
    manager.add_message(session_id, "assistant", "你好！我是AI助手，有什么可以帮助你的吗？")
    manager.add_message(session_id, "user", "请介绍一下机器学习")
    manager.add_message(session_id, "assistant", "机器学习是人工智能的一个分支...")
    
    # 获取消息
    messages = manager.get_messages(session_id)
    print(f"会话消息数量: {len(messages)}")
    
    # 获取会话摘要
    summary = manager.get_conversation_summary(session_id)
    print(f"会话摘要: {summary}")
    
    # 导出会话
    exported = manager.export_conversation(session_id)
    print(f"导出会话长度: {len(exported)} 字符")
    
    # 获取统计信息
    stats = manager.get_stats()
    print(f"统计信息: {stats}")

if __name__ == "__main__":
    demo_conversation_manager()
```

## 4. 完整应用示例

### chatbot_demo.py

```python
"""
聊天机器人完整演示 - 集成所有功能的聊天机器人
"""
import asyncio
import streamlit as st
from typing import Dict, Any, Optional
from datetime import datetime

# 导入自定义模块
from config_manager import ConfigManager
from conversation_manager import ConversationManager
from base_client import BaseAPIClient

class ChatbotDemo:
    """聊天机器人演示应用"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.conversation_manager = ConversationManager()
        self.api_clients = self._initialize_api_clients()
        self.current_provider = "openai"
    
    def _initialize_api_clients(self) -> Dict[str, BaseAPIClient]:
        """初始化API客户端"""
        clients = {}
        
        # 这里应该导入具体的客户端实现
        # clients['openai'] = OpenAIClient(...)
        # clients['claude'] = ClaudeClient(...)
        # clients['gemini'] = GeminiClient(...)
        
        return clients
    
    def create_streamlit_app(self):
        """创建Streamlit应用"""
        st.set_page_config(
            page_title="智能聊天机器人",
            page_icon="🤖",
            layout="wide"
        )
        
        st.title("🤖 智能聊天机器人")
        st.markdown("基于多厂商LLM API的智能对话系统")
        
        # 侧边栏配置
        with st.sidebar:
            st.header("⚙️ 配置")
            
            # API提供商选择
            self.current_provider = st.selectbox(
                "选择API提供商",
                options=list(self.api_clients.keys()) if self.api_clients else ["请配置API"],
                index=0
            )
            
            # 模型参数
            temperature = st.slider("Temperature", 0.0, 2.0, 0.7, 0.1)
            max_tokens = st.slider("Max Tokens", 50, 2000, 500, 50)
            
            # 系统提示
            system_prompt = st.text_area(
                "系统提示",
                value="你是一个有用、诚实、无害的AI助手。",
                height=100
            )
            
            # 会话管理
            st.header("💬 会话管理")
            if st.button("新建会话"):
                st.session_state.session_id = self.conversation_manager.create_session(
                    user_id="streamlit_user",
                    system_prompt=system_prompt
                )
                st.success("新会话已创建")
            
            if st.button("清空当前会话"):
                if hasattr(st.session_state, 'session_id'):
                    self.conversation_manager.delete_session(st.session_state.session_id)
                    del st.session_state.session_id
                    st.success("会话已清空")
        
        # 主聊天界面
        self._render_chat_interface(temperature, max_tokens, system_prompt)
        
        # 统计信息
        self._render_stats()
    
    def _render_chat_interface(self, temperature: float, max_tokens: int, 
                             system_prompt: str):
        """渲染聊天界面"""
        # 确保有会话ID
        if 'session_id' not in st.session_state:
            st.session_state.session_id = self.conversation_manager.create_session(
                user_id="streamlit_user",
                system_prompt=system_prompt
            )
        
        session_id = st.session_state.session_id
        
        # 显示聊天历史
        conversation = self.conversation_manager.get_conversation(session_id)
        if conversation:
            for message in conversation.messages:
                if message.role == "system":
                    continue
                
                with st.chat_message(message.role):
                    st.write(message.content)
                    st.caption(f"{message.timestamp.strftime('%H:%M:%S')}")
        
        # 用户输入
        if prompt := st.chat_input("请输入您的问题..."):
            # 显示用户消息
            with st.chat_message("user"):
                st.write(prompt)
            
            # 添加用户消息到会话
            self.conversation_manager.add_message(session_id, "user", prompt)
            
            # 生成AI回复
            with st.chat_message("assistant"):
                with st.spinner("思考中..."):
                    response = self._generate_response(
                        session_id, temperature, max_tokens
                    )
                    st.write(response)
            
            # 添加AI回复到会话
            self.conversation_manager.add_message(session_id, "assistant", response)
    
    def _generate_response(self, session_id: str, temperature: float, 
                          max_tokens: int) -> str:
        """生成AI回复"""
        if not self.api_clients or self.current_provider not in self.api_clients:
            return "抱歉，API客户端未配置或不可用。"
        
        try:
            client = self.api_clients[self.current_provider]
            messages = self.conversation_manager.get_messages(session_id)
            
            response = client.chat(
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            return response.content
            
        except Exception as e:
            return f"抱歉，生成回复时出现错误：{str(e)}"
    
    def _render_stats(self):
        """渲染统计信息"""
        st.header("📊 统计信息")
        
        col1, col2, col3 = st.columns(3)
        
        # 会话统计
        conv_stats = self.conversation_manager.get_stats()
        with col1:
            st.metric("总会话数", conv_stats['total_conversations'])
            st.metric("总消息数", conv_stats['total_messages'])
        
        # API统计
        if self.api_clients and self.current_provider in self.api_clients:
            api_stats = self.api_clients[self.current_provider].get_stats()
            with col2:
                st.metric("API请求数", api_stats['total_requests'])
                st.metric("成功率", f"{api_stats['successful_requests'] / max(1, api_stats['total_requests']) * 100:.1f}%")
        
        # 当前会话信息
        if hasattr(st.session_state, 'session_id'):
            session_summary = self.conversation_manager.get_conversation_summary(
                st.session_state.session_id
            )
            with col3:
                st.metric("当前会话消息", session_summary.get('message_count', 0))
                st.metric("会话时长", session_summary.get('duration', 'N/A'))

def main():
    """主函数"""
    chatbot = ChatbotDemo()
    chatbot.create_streamlit_app()

if __name__ == "__main__":
    main()
```

## 使用说明

### 1. 环境设置

```bash
# 安装依赖
pip install -r requirements.txt

# 设置环境变量
cp .env.example .env
# 编辑 .env 文件，填入你的API密钥
```

### 2. 运行示例

```bash
# 基础配置测试
python 基础配置/config_manager.py

# 环境设置
python 基础配置/environment_setup.py

# 聊天机器人演示
streamlit run 完整应用/chatbot_demo.py
```

### 3. 自定义开发

1. **继承基础客户端**：实现自己的API客户端
2. **扩展对话管理**：添加更多对话功能
3. **集成新功能**：使用提供的工具类构建应用

这些代码示例为LLM API开发提供了完整的基础框架，可以根据具体需求进行扩展和定制。
