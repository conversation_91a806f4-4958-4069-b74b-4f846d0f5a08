# 项目3：多模态RAG应用

## 项目概述

本项目旨在构建一个先进的多模态检索增强生成（RAG）应用，能够处理文本、图像、音频、视频等多种模态的数据，提供统一的智能问答和内容生成服务。这是一个展示前沿AI技术集成应用的综合性项目。

## 项目目标

1. **多模态数据处理**：统一处理文本、图像、音频、视频数据
2. **跨模态检索**：支持跨不同模态的信息检索
3. **多模态生成**：生成包含多种模态的丰富回答
4. **智能理解**：深度理解多模态内容的语义关联
5. **用户体验**：提供直观的多模态交互界面

## 技术架构

```
多模态RAG应用架构
┌─────────────────────────────────────────────────────────┐
│                    用户交互层                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │ 文本输入     │ │ 语音输入     │ │ 图像输入     │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
├─────────────────────────────────────────────────────────┤
│                    模态处理层                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │ 文本处理     │ │ 视觉处理     │ │ 音频处理     │        │
│  │(LlamaIndex) │ │(CLIP/BLIP)  │ │(Whisper)    │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
├─────────────────────────────────────────────────────────┤
│                    融合检索层                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │ 跨模态检索   │ │ 语义对齐     │ │ 相关性计算   │        │
│  │(LangChain)  │ │(Embedding)  │ │(Similarity) │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
├─────────────────────────────────────────────────────────┤
│                    生成合成层                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │ 文本生成     │ │ 图像生成     │ │ 多模态合成   │        │
│  │(LLM)        │ │(DALL-E)     │ │(LangChain)  │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
└─────────────────────────────────────────────────────────┘
```

## 核心功能实现

### 1. 多模态数据处理引擎

```python
# multimodal_rag/core/multimodal_processor.py
from llama_index.core import Document, VectorStoreIndex
from llama_index.core.schema import ImageDocument, AudioDocument
from typing import List, Dict, Any, Optional, Union, Tuple
import torch
import numpy as np
from PIL import Image
import io
import base64
import asyncio
import logging
from datetime import datetime
import json
import os

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MultiModalProcessor:
    """多模态数据处理器"""
    
    def __init__(self, 
                 storage_dir: str = "./multimodal_storage",
                 device: str = "auto"):
        self.storage_dir = storage_dir
        self.device = self._setup_device(device)
        
        # 创建存储目录
        self.text_dir = os.path.join(storage_dir, "text")
        self.image_dir = os.path.join(storage_dir, "images")
        self.audio_dir = os.path.join(storage_dir, "audio")
        self.video_dir = os.path.join(storage_dir, "videos")
        self.embeddings_dir = os.path.join(storage_dir, "embeddings")
        
        for dir_path in [self.text_dir, self.image_dir, self.audio_dir, 
                        self.video_dir, self.embeddings_dir]:
            os.makedirs(dir_path, exist_ok=True)
        
        # 初始化模型
        self.setup_models()
        
        # 数据存储
        self.documents = {
            "text": [],
            "image": [],
            "audio": [],
            "video": []
        }
        
        # 嵌入存储
        self.embeddings = {
            "text": {},
            "image": {},
            "audio": {},
            "video": {}
        }
    
    def _setup_device(self, device: str) -> str:
        """设置计算设备"""
        if device == "auto":
            return "cuda" if torch.cuda.is_available() else "cpu"
        return device
    
    def setup_models(self):
        """设置多模态模型"""
        try:
            # CLIP模型用于图像-文本理解
            import clip
            self.clip_model, self.clip_preprocess = clip.load("ViT-B/32", device=self.device)
            logger.info("CLIP模型加载成功")
        except ImportError:
            logger.warning("CLIP未安装，图像处理功能受限")
            self.clip_model = None
        
        try:
            # Whisper模型用于音频转录
            import whisper
            self.whisper_model = whisper.load_model("base")
            logger.info("Whisper模型加载成功")
        except ImportError:
            logger.warning("Whisper未安装，音频处理功能受限")
            self.whisper_model = None
        
        try:
            # 文本嵌入模型
            from sentence_transformers import SentenceTransformer
            self.text_encoder = SentenceTransformer('all-MiniLM-L6-v2')
            logger.info("文本嵌入模型加载成功")
        except ImportError:
            logger.warning("SentenceTransformers未安装，文本嵌入功能受限")
            self.text_encoder = None
    
    async def process_text_document(self, 
                                  text: str,
                                  metadata: Optional[Dict[str, Any]] = None) -> str:
        """处理文本文档"""
        doc_id = self._generate_doc_id("text")
        
        # 创建文档对象
        document = Document(
            text=text,
            metadata={
                "doc_id": doc_id,
                "modality": "text",
                "processed_at": datetime.now().isoformat(),
                **(metadata or {})
            }
        )
        
        # 生成文本嵌入
        if self.text_encoder:
            embedding = await asyncio.to_thread(
                self.text_encoder.encode, text
            )
            self.embeddings["text"][doc_id] = embedding.tolist()
        
        # 存储文档
        self.documents["text"].append(document)
        
        # 保存到文件
        await self._save_text_document(doc_id, document)
        
        logger.info(f"文本文档已处理: {doc_id}")
        return doc_id
    
    async def process_image_document(self,
                                   image_data: Union[bytes, Image.Image, str],
                                   caption: Optional[str] = None,
                                   metadata: Optional[Dict[str, Any]] = None) -> str:
        """处理图像文档"""
        doc_id = self._generate_doc_id("image")
        
        # 处理图像数据
        if isinstance(image_data, str):
            # Base64编码的图像
            image_bytes = base64.b64decode(image_data)
            image = Image.open(io.BytesIO(image_bytes))
        elif isinstance(image_data, bytes):
            image = Image.open(io.BytesIO(image_data))
        else:
            image = image_data
        
        # 生成图像描述（如果没有提供caption）
        if not caption and self.clip_model:
            caption = await self._generate_image_caption(image)
        
        # 创建图像文档
        document = ImageDocument(
            image=image,
            text=caption or "图像内容",
            metadata={
                "doc_id": doc_id,
                "modality": "image",
                "processed_at": datetime.now().isoformat(),
                "image_size": image.size,
                "image_mode": image.mode,
                **(metadata or {})
            }
        )
        
        # 生成图像嵌入
        if self.clip_model:
            embedding = await self._generate_image_embedding(image)
            self.embeddings["image"][doc_id] = embedding.tolist()
        
        # 存储文档
        self.documents["image"].append(document)
        
        # 保存到文件
        await self._save_image_document(doc_id, image, document.metadata)
        
        logger.info(f"图像文档已处理: {doc_id}")
        return doc_id
    
    async def process_audio_document(self,
                                   audio_data: bytes,
                                   metadata: Optional[Dict[str, Any]] = None) -> str:
        """处理音频文档"""
        doc_id = self._generate_doc_id("audio")
        
        # 保存音频文件
        audio_path = os.path.join(self.audio_dir, f"{doc_id}.wav")
        with open(audio_path, 'wb') as f:
            f.write(audio_data)
        
        # 音频转录
        transcript = ""
        if self.whisper_model:
            transcript = await self._transcribe_audio(audio_path)
        
        # 创建音频文档
        document = AudioDocument(
            audio_path=audio_path,
            text=transcript,
            metadata={
                "doc_id": doc_id,
                "modality": "audio",
                "processed_at": datetime.now().isoformat(),
                "audio_path": audio_path,
                "transcript": transcript,
                **(metadata or {})
            }
        )
        
        # 生成音频嵌入（基于转录文本）
        if transcript and self.text_encoder:
            embedding = await asyncio.to_thread(
                self.text_encoder.encode, transcript
            )
            self.embeddings["audio"][doc_id] = embedding.tolist()
        
        # 存储文档
        self.documents["audio"].append(document)
        
        logger.info(f"音频文档已处理: {doc_id}")
        return doc_id
    
    async def _generate_image_caption(self, image: Image.Image) -> str:
        """生成图像描述"""
        if not self.clip_model:
            return "图像内容描述不可用"
        
        try:
            # 使用CLIP生成图像描述（简化实现）
            # 实际应用中可以使用更专业的图像描述模型
            return "这是一张图像"  # 占位符
        except Exception as e:
            logger.error(f"图像描述生成失败: {str(e)}")
            return "图像描述生成失败"
    
    async def _generate_image_embedding(self, image: Image.Image) -> np.ndarray:
        """生成图像嵌入"""
        def _encode_image():
            image_input = self.clip_preprocess(image).unsqueeze(0).to(self.device)
            with torch.no_grad():
                image_features = self.clip_model.encode_image(image_input)
                return image_features.cpu().numpy().flatten()
        
        return await asyncio.to_thread(_encode_image)
    
    async def _transcribe_audio(self, audio_path: str) -> str:
        """转录音频"""
        if not self.whisper_model:
            return "音频转录不可用"
        
        try:
            def _transcribe():
                result = self.whisper_model.transcribe(audio_path)
                return result["text"]
            
            return await asyncio.to_thread(_transcribe)
        except Exception as e:
            logger.error(f"音频转录失败: {str(e)}")
            return "音频转录失败"
    
    def _generate_doc_id(self, modality: str) -> str:
        """生成文档ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        return f"{modality}_{timestamp}"
    
    async def _save_text_document(self, doc_id: str, document: Document):
        """保存文本文档"""
        file_path = os.path.join(self.text_dir, f"{doc_id}.json")
        doc_data = {
            "text": document.text,
            "metadata": document.metadata
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(doc_data, f, ensure_ascii=False, indent=2)
    
    async def _save_image_document(self, doc_id: str, image: Image.Image, metadata: Dict):
        """保存图像文档"""
        # 保存图像
        image_path = os.path.join(self.image_dir, f"{doc_id}.png")
        image.save(image_path)
        
        # 保存元数据
        metadata_path = os.path.join(self.image_dir, f"{doc_id}_metadata.json")
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)
    
    def get_documents_by_modality(self, modality: str) -> List:
        """按模态获取文档"""
        return self.documents.get(modality, [])
    
    def get_all_documents(self) -> Dict[str, List]:
        """获取所有文档"""
        return self.documents.copy()
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取处理统计"""
        stats = {}
        for modality, docs in self.documents.items():
            stats[modality] = {
                "count": len(docs),
                "has_embeddings": len(self.embeddings[modality])
            }
        
        stats["total_documents"] = sum(stats[mod]["count"] for mod in stats)
        return stats

# 跨模态检索引擎
class CrossModalRetriever:
    """跨模态检索引擎"""
    
    def __init__(self, multimodal_processor: MultiModalProcessor):
        self.processor = multimodal_processor
        self.similarity_threshold = 0.7
    
    async def search_across_modalities(self,
                                     query: str,
                                     query_modality: str = "text",
                                     target_modalities: List[str] = None,
                                     top_k: int = 5) -> Dict[str, List[Dict]]:
        """跨模态搜索"""
        if target_modalities is None:
            target_modalities = ["text", "image", "audio"]
        
        # 生成查询嵌入
        query_embedding = await self._generate_query_embedding(query, query_modality)
        
        results = {}
        
        # 在每个目标模态中搜索
        for modality in target_modalities:
            modality_results = await self._search_in_modality(
                query_embedding, modality, top_k
            )
            results[modality] = modality_results
        
        return results
    
    async def _generate_query_embedding(self, query: str, modality: str) -> np.ndarray:
        """生成查询嵌入"""
        if modality == "text" and self.processor.text_encoder:
            return await asyncio.to_thread(
                self.processor.text_encoder.encode, query
            )
        elif modality == "image" and self.processor.clip_model:
            # 对于图像查询，使用CLIP的文本编码器
            import clip
            text_tokens = clip.tokenize([query]).to(self.processor.device)
            with torch.no_grad():
                text_features = self.processor.clip_model.encode_text(text_tokens)
                return text_features.cpu().numpy().flatten()
        else:
            raise ValueError(f"不支持的查询模态: {modality}")
    
    async def _search_in_modality(self,
                                query_embedding: np.ndarray,
                                modality: str,
                                top_k: int) -> List[Dict]:
        """在特定模态中搜索"""
        modality_embeddings = self.processor.embeddings[modality]
        
        if not modality_embeddings:
            return []
        
        # 计算相似度
        similarities = []
        for doc_id, embedding in modality_embeddings.items():
            similarity = self._cosine_similarity(query_embedding, np.array(embedding))
            if similarity >= self.similarity_threshold:
                similarities.append({
                    "doc_id": doc_id,
                    "similarity": float(similarity),
                    "modality": modality
                })
        
        # 排序并返回top-k
        similarities.sort(key=lambda x: x["similarity"], reverse=True)
        return similarities[:top_k]
    
    def _cosine_similarity(self, a: np.ndarray, b: np.ndarray) -> float:
        """计算余弦相似度"""
        return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))
    
    async def find_related_content(self,
                                 doc_id: str,
                                 source_modality: str,
                                 target_modalities: List[str] = None,
                                 top_k: int = 3) -> Dict[str, List[Dict]]:
        """查找相关内容"""
        if target_modalities is None:
            target_modalities = ["text", "image", "audio"]
        
        # 获取源文档嵌入
        source_embedding = self.processor.embeddings[source_modality].get(doc_id)
        if source_embedding is None:
            return {}
        
        source_embedding = np.array(source_embedding)
        results = {}
        
        # 在目标模态中查找相关内容
        for modality in target_modalities:
            if modality == source_modality:
                continue  # 跳过同模态
            
            modality_results = await self._search_in_modality(
                source_embedding, modality, top_k
            )
            results[modality] = modality_results
        
        return results

# 多模态生成器
class MultiModalGenerator:
    """多模态内容生成器"""
    
    def __init__(self, llm, image_generator=None):
        self.llm = llm
        self.image_generator = image_generator
    
    async def generate_multimodal_response(self,
                                         query: str,
                                         retrieved_content: Dict[str, List[Dict]],
                                         response_modalities: List[str] = None) -> Dict[str, Any]:
        """生成多模态响应"""
        if response_modalities is None:
            response_modalities = ["text"]
        
        response = {}
        
        # 生成文本响应
        if "text" in response_modalities:
            text_response = await self._generate_text_response(query, retrieved_content)
            response["text"] = text_response
        
        # 生成图像响应
        if "image" in response_modalities and self.image_generator:
            image_response = await self._generate_image_response(query, retrieved_content)
            response["image"] = image_response
        
        # 添加源信息
        response["sources"] = self._format_sources(retrieved_content)
        
        return response
    
    async def _generate_text_response(self,
                                    query: str,
                                    retrieved_content: Dict[str, List[Dict]]) -> str:
        """生成文本响应"""
        # 构建上下文
        context_parts = []
        
        for modality, results in retrieved_content.items():
            if results:
                context_parts.append(f"\n{modality.upper()}内容:")
                for i, result in enumerate(results[:3]):  # 限制每个模态最多3个结果
                    context_parts.append(f"{i+1}. 相似度: {result['similarity']:.2f}")
                    # 这里需要根据实际文档内容添加具体信息
        
        context = "\n".join(context_parts)
        
        # 构建提示
        prompt = f"""
        基于以下多模态内容回答问题：
        
        {context}
        
        问题：{query}
        
        请提供综合性的回答，考虑所有模态的信息：
        """
        
        # 生成响应
        response = await asyncio.to_thread(self.llm, prompt)
        return response
    
    async def _generate_image_response(self,
                                     query: str,
                                     retrieved_content: Dict[str, List[Dict]]) -> Optional[str]:
        """生成图像响应"""
        if not self.image_generator:
            return None
        
        # 基于查询和检索内容生成图像提示
        image_prompt = f"Generate an image related to: {query}"
        
        try:
            # 这里需要集成实际的图像生成模型
            # image_url = await self.image_generator.generate(image_prompt)
            # return image_url
            return "图像生成功能需要集成图像生成模型"
        except Exception as e:
            logger.error(f"图像生成失败: {str(e)}")
            return None
    
    def _format_sources(self, retrieved_content: Dict[str, List[Dict]]) -> List[Dict]:
        """格式化源信息"""
        sources = []
        
        for modality, results in retrieved_content.items():
            for result in results:
                sources.append({
                    "doc_id": result["doc_id"],
                    "modality": result["modality"],
                    "similarity": result["similarity"]
                })
        
        return sources

# 使用示例
async def demo_multimodal_rag():
    """演示多模态RAG应用"""
    print("=== 多模态RAG应用演示 ===")
    
    # 初始化处理器
    processor = MultiModalProcessor("./demo_multimodal")
    
    # 添加示例文本文档
    text_docs = [
        "人工智能是计算机科学的一个分支，致力于创建智能机器。",
        "深度学习是机器学习的一个子集，使用神经网络进行学习。",
        "计算机视觉使机器能够理解和解释视觉信息。"
    ]
    
    text_doc_ids = []
    for text in text_docs:
        doc_id = await processor.process_text_document(text)
        text_doc_ids.append(doc_id)
    
    # 创建示例图像（实际应用中会有真实图像）
    sample_image = Image.new('RGB', (100, 100), color='blue')
    image_doc_id = await processor.process_image_document(
        sample_image, 
        caption="蓝色方块图像"
    )
    
    # 显示统计信息
    stats = processor.get_statistics()
    print(f"处理统计: {stats}")
    
    # 创建检索器
    retriever = CrossModalRetriever(processor)
    
    # 测试跨模态搜索
    query = "什么是人工智能？"
    search_results = await retriever.search_across_modalities(
        query=query,
        target_modalities=["text", "image"]
    )
    
    print(f"\n查询: {query}")
    print(f"搜索结果: {search_results}")
    
    # 创建生成器（使用模拟LLM）
    class MockLLM:
        def __call__(self, prompt):
            return "这是基于多模态内容的综合回答。"
    
    generator = MultiModalGenerator(MockLLM())
    
    # 生成多模态响应
    response = await generator.generate_multimodal_response(
        query=query,
        retrieved_content=search_results,
        response_modalities=["text"]
    )
    
    print(f"\n生成的响应: {response}")

if __name__ == "__main__":
    asyncio.run(demo_multimodal_rag())
```

### 2. 智能用户界面

```python
# multimodal_rag/ui/streamlit_app.py
import streamlit as st
import asyncio
from typing import Dict, Any, List
import base64
import io
from PIL import Image
import plotly.express as px
import plotly.graph_objects as go

class MultiModalRAGUI:
    """多模态RAG用户界面"""
    
    def __init__(self, processor, retriever, generator):
        self.processor = processor
        self.retriever = retriever
        self.generator = generator
    
    def render_main_interface(self):
        """渲染主界面"""
        st.set_page_config(
            page_title="多模态RAG应用",
            page_icon="🌟",
            layout="wide"
        )
        
        st.title("🌟 多模态RAG应用")
        st.markdown("支持文本、图像、音频的智能问答系统")
        
        # 侧边栏
        self.render_sidebar()
        
        # 主要内容
        tab1, tab2, tab3, tab4 = st.tabs([
            "🤖 智能问答", 
            "📁 内容管理", 
            "🔍 跨模态搜索", 
            "📊 系统分析"
        ])
        
        with tab1:
            self.render_qa_interface()
        
        with tab2:
            self.render_content_management()
        
        with tab3:
            self.render_cross_modal_search()
        
        with tab4:
            self.render_system_analytics()
    
    def render_sidebar(self):
        """渲染侧边栏"""
        with st.sidebar:
            st.header("📊 系统状态")
            
            # 显示统计信息
            stats = self.processor.get_statistics()
            
            col1, col2 = st.columns(2)
            with col1:
                st.metric("文本文档", stats.get("text", {}).get("count", 0))
                st.metric("图像文档", stats.get("image", {}).get("count", 0))
            with col2:
                st.metric("音频文档", stats.get("audio", {}).get("count", 0))
                st.metric("总文档数", stats.get("total_documents", 0))
            
            st.divider()
            
            # 模型状态
            st.header("🔧 模型状态")
            
            model_status = {
                "文本编码器": "✅" if self.processor.text_encoder else "❌",
                "CLIP模型": "✅" if self.processor.clip_model else "❌",
                "Whisper模型": "✅" if self.processor.whisper_model else "❌"
            }
            
            for model, status in model_status.items():
                st.write(f"{status} {model}")
    
    def render_qa_interface(self):
        """渲染问答界面"""
        st.header("🤖 多模态智能问答")
        
        # 查询输入区域
        col1, col2 = st.columns([3, 1])
        
        with col1:
            query_text = st.text_input(
                "输入您的问题：",
                placeholder="例如：描述一下上传的图像内容"
            )
        
        with col2:
            query_button = st.button("🔍 提问", type="primary")
        
        # 多模态输入选项
        st.subheader("📎 多模态输入")
        
        input_tabs = st.tabs(["📝 文本", "🖼️ 图像", "🎵 音频"])
        
        with input_tabs[0]:
            additional_text = st.text_area(
                "补充文本信息",
                placeholder="可以提供额外的文本上下文..."
            )
        
        with input_tabs[1]:
            uploaded_image = st.file_uploader(
                "上传图像",
                type=["png", "jpg", "jpeg", "gif"],
                help="支持PNG、JPG、JPEG、GIF格式"
            )
            
            if uploaded_image:
                image = Image.open(uploaded_image)
                st.image(image, caption="上传的图像", use_column_width=True)
        
        with input_tabs[2]:
            uploaded_audio = st.file_uploader(
                "上传音频",
                type=["wav", "mp3", "m4a"],
                help="支持WAV、MP3、M4A格式"
            )
            
            if uploaded_audio:
                st.audio(uploaded_audio.read())
        
        # 高级选项
        with st.expander("⚙️ 高级选项"):
            col1, col2, col3 = st.columns(3)
            
            with col1:
                search_modalities = st.multiselect(
                    "搜索模态",
                    ["text", "image", "audio"],
                    default=["text", "image"]
                )
            
            with col2:
                response_modalities = st.multiselect(
                    "响应模态",
                    ["text", "image"],
                    default=["text"]
                )
            
            with col3:
                top_k = st.slider("检索数量", 1, 10, 5)
        
        # 处理查询
        if query_button and query_text:
            with st.spinner("正在处理您的查询..."):
                # 这里需要实际的异步处理
                # 由于Streamlit的限制，这里使用模拟结果
                
                st.subheader("📝 回答")
                st.write("这是基于多模态内容的智能回答。系统分析了文本、图像和音频内容，为您提供综合性的答案。")
                
                # 显示置信度
                st.metric("回答置信度", "85%")
                
                # 显示来源
                st.subheader("📚 信息来源")
                
                source_data = [
                    {"模态": "文本", "文档ID": "text_001", "相似度": 0.92},
                    {"模态": "图像", "文档ID": "image_001", "相似度": 0.78},
                    {"模态": "音频", "文档ID": "audio_001", "相似度": 0.65}
                ]
                
                for source in source_data:
                    with st.expander(f"{source['模态']} - {source['文档ID']} (相似度: {source['相似度']:.2f})"):
                        if source['模态'] == '文本':
                            st.write("这是相关的文本内容片段...")
                        elif source['模态'] == '图像':
                            st.write("这是相关的图像内容...")
                        elif source['模态'] == '音频':
                            st.write("这是相关的音频转录内容...")
    
    def render_content_management(self):
        """渲染内容管理界面"""
        st.header("📁 多模态内容管理")
        
        # 内容上传
        st.subheader("📤 上传内容")
        
        upload_tabs = st.tabs(["📝 文本", "🖼️ 图像", "🎵 音频"])
        
        with upload_tabs[0]:
            st.write("**添加文本文档**")
            text_content = st.text_area("文本内容", height=200)
            text_metadata = st.text_input("元数据（JSON格式）", placeholder='{"category": "技术文档"}')
            
            if st.button("添加文本文档"):
                if text_content:
                    # 这里需要实际的处理逻辑
                    st.success("文本文档已添加！")
                else:
                    st.warning("请输入文本内容")
        
        with upload_tabs[1]:
            st.write("**添加图像文档**")
            image_file = st.file_uploader("选择图像", type=["png", "jpg", "jpeg"])
            image_caption = st.text_input("图像描述")
            
            if st.button("添加图像文档"):
                if image_file:
                    st.success("图像文档已添加！")
                else:
                    st.warning("请选择图像文件")
        
        with upload_tabs[2]:
            st.write("**添加音频文档**")
            audio_file = st.file_uploader("选择音频", type=["wav", "mp3"])
            
            if st.button("添加音频文档"):
                if audio_file:
                    st.success("音频文档已添加！")
                else:
                    st.warning("请选择音频文件")
        
        # 内容浏览
        st.subheader("📋 内容浏览")
        
        # 模态选择
        selected_modality = st.selectbox(
            "选择模态",
            ["全部", "文本", "图像", "音频"]
        )
        
        # 显示内容列表
        if selected_modality == "全部":
            stats = self.processor.get_statistics()
            
            for modality in ["text", "image", "audio"]:
                count = stats.get(modality, {}).get("count", 0)
                if count > 0:
                    st.write(f"**{modality.upper()}文档**: {count} 个")
        
        else:
            st.write(f"显示 {selected_modality} 文档列表...")
            # 这里需要实际的文档列表
    
    def render_cross_modal_search(self):
        """渲染跨模态搜索界面"""
        st.header("🔍 跨模态搜索")
        
        # 搜索配置
        col1, col2 = st.columns(2)
        
        with col1:
            search_query = st.text_input("搜索查询", placeholder="输入搜索关键词...")
            query_modality = st.selectbox("查询模态", ["text", "image"])
        
        with col2:
            target_modalities = st.multiselect(
                "目标模态",
                ["text", "image", "audio"],
                default=["text", "image", "audio"]
            )
            search_k = st.slider("返回结果数", 1, 20, 10)
        
        # 执行搜索
        if st.button("🔍 执行跨模态搜索"):
            if search_query:
                with st.spinner("正在搜索..."):
                    # 模拟搜索结果
                    st.subheader("🎯 搜索结果")
                    
                    for modality in target_modalities:
                        st.write(f"**{modality.upper()}模态结果:**")
                        
                        # 模拟结果
                        for i in range(min(3, search_k)):
                            with st.expander(f"{modality}_doc_{i+1} (相似度: {0.9-i*0.1:.2f})"):
                                st.write(f"这是{modality}模态的搜索结果内容...")
            else:
                st.warning("请输入搜索查询")
        
        # 相关性分析
        st.subheader("📊 相关性分析")
        
        # 创建相关性热力图
        import numpy as np
        
        modalities = ["文本", "图像", "音频"]
        correlation_matrix = np.random.rand(3, 3)
        
        fig = px.imshow(
            correlation_matrix,
            x=modalities,
            y=modalities,
            color_continuous_scale="Blues",
            title="跨模态相关性矩阵"
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    def render_system_analytics(self):
        """渲染系统分析界面"""
        st.header("📊 系统分析")
        
        # 性能指标
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("平均查询时间", "1.2秒", delta="-0.3秒")
        with col2:
            st.metric("系统准确率", "87%", delta="2%")
        with col3:
            st.metric("用户满意度", "4.5/5", delta="0.2")
        with col4:
            st.metric("日活跃用户", "156", delta="12")
        
        # 使用统计
        st.subheader("📈 使用统计")
        
        # 模态使用分布
        col1, col2 = st.columns(2)
        
        with col1:
            modality_usage = {
                "文本": 45,
                "图像": 35,
                "音频": 20
            }
            
            fig = px.pie(
                values=list(modality_usage.values()),
                names=list(modality_usage.keys()),
                title="模态使用分布"
            )
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            # 查询类型分布
            query_types = {
                "事实性查询": 40,
                "分析性查询": 30,
                "比较性查询": 20,
                "创意性查询": 10
            }
            
            fig = px.bar(
                x=list(query_types.keys()),
                y=list(query_types.values()),
                title="查询类型分布"
            )
            st.plotly_chart(fig, use_container_width=True)
        
        # 系统性能趋势
        st.subheader("📉 性能趋势")
        
        # 模拟时间序列数据
        import pandas as pd
        from datetime import datetime, timedelta
        
        dates = [datetime.now() - timedelta(days=i) for i in range(30, 0, -1)]
        response_times = np.random.normal(1.2, 0.3, 30)
        accuracy_scores = np.random.normal(0.87, 0.05, 30)
        
        df = pd.DataFrame({
            "日期": dates,
            "响应时间": response_times,
            "准确率": accuracy_scores
        })
        
        fig = go.Figure()
        
        fig.add_trace(go.Scatter(
            x=df["日期"],
            y=df["响应时间"],
            mode='lines+markers',
            name='响应时间(秒)',
            yaxis='y'
        ))
        
        fig.add_trace(go.Scatter(
            x=df["日期"],
            y=df["准确率"],
            mode='lines+markers',
            name='准确率',
            yaxis='y2'
        ))
        
        fig.update_layout(
            title="系统性能趋势",
            xaxis_title="日期",
            yaxis=dict(title="响应时间(秒)", side="left"),
            yaxis2=dict(title="准确率", side="right", overlaying="y")
        )
        
        st.plotly_chart(fig, use_container_width=True)

def main():
    """主函数"""
    st.title("🌟 多模态RAG应用")
    st.info("这是多模态RAG应用的演示界面。要运行完整功能，请配置相应的模型和API。")
    
    # 显示功能特性
    st.header("✨ 核心功能")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.subheader("🔄 多模态处理")
        st.write("- 文本文档处理")
        st.write("- 图像内容理解")
        st.write("- 音频转录分析")
        st.write("- 视频内容提取")
    
    with col2:
        st.subheader("🔍 跨模态检索")
        st.write("- 语义相似度计算")
        st.write("- 跨模态关联发现")
        st.write("- 智能内容推荐")
        st.write("- 多维度搜索")
    
    with col3:
        st.subheader("🎨 多模态生成")
        st.write("- 智能文本生成")
        st.write("- 图像内容生成")
        st.write("- 多媒体报告")
        st.write("- 个性化回答")

if __name__ == "__main__":
    main()
```

## 项目价值

1. **技术前沿性**：展示最新的多模态AI技术
2. **实用价值**：解决复杂的多媒体信息处理需求
3. **学习价值**：提供完整的多模态系统开发经验
4. **扩展性**：支持多种模态和应用场景
5. **创新性**：探索跨模态理解和生成的可能性

这个多模态RAG应用项目代表了AI技术的前沿应用，为学员提供了接触和实践最新技术的机会，是掌握现代AI系统开发的重要项目。
