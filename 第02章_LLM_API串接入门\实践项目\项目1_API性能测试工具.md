# 项目1：API性能测试工具

## 项目概述

本项目旨在开发一个全面的LLM API性能测试工具，能够测试多个厂商的API性能，包括响应时间、准确性、成本效益等指标，并生成详细的性能报告。

## 项目目标

1. **多厂商支持**：支持OpenAI、Claude、Gemini等主流API
2. **全面测试**：测试响应时间、质量、成本等多个维度
3. **并发测试**：支持高并发压力测试
4. **报告生成**：生成详细的性能分析报告
5. **可视化展示**：提供图表和可视化分析

## 技术架构

```
API性能测试工具
├── 测试引擎
│   ├── 单API测试器
│   ├── 并发测试器
│   └── 压力测试器
├── 指标收集器
│   ├── 性能指标
│   ├── 质量指标
│   └── 成本指标
├── 报告生成器
│   ├── 数据分析
│   ├── 图表生成
│   └── 报告导出
└── 配置管理
    ├── API配置
    ├── 测试配置
    └── 输出配置
```

## 核心功能实现

### 1. 测试引擎核心

```python
# performance_tester/core/test_engine.py
import asyncio
import time
import statistics
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
import json

@dataclass
class TestCase:
    """测试用例"""
    id: str
    prompt: str
    expected_keywords: List[str] = None
    max_tokens: int = 150
    temperature: float = 0.7
    category: str = "general"

@dataclass
class TestResult:
    """测试结果"""
    test_case_id: str
    provider: str
    model: str
    response: str
    response_time: float
    token_usage: Dict[str, int]
    cost: float
    quality_score: float
    error: Optional[str] = None
    timestamp: float = None

class PerformanceTester:
    """API性能测试器"""
    
    def __init__(self, providers: Dict[str, Any]):
        self.providers = providers
        self.test_results = []
        
    def create_test_cases(self) -> List[TestCase]:
        """创建测试用例集"""
        return [
            TestCase(
                id="creative_writing",
                prompt="写一个关于未来城市的短故事（100字以内）",
                expected_keywords=["未来", "城市", "科技"],
                category="创意写作"
            ),
            TestCase(
                id="factual_qa",
                prompt="请解释什么是机器学习，包括其主要类型",
                expected_keywords=["机器学习", "监督学习", "无监督学习"],
                category="事实问答"
            ),
            TestCase(
                id="code_generation",
                prompt="用Python写一个计算斐波那契数列的函数",
                expected_keywords=["def", "fibonacci", "return"],
                category="代码生成"
            ),
            TestCase(
                id="translation",
                prompt="将以下英文翻译成中文：'Artificial Intelligence is transforming our world.'",
                expected_keywords=["人工智能", "改变", "世界"],
                category="翻译"
            ),
            TestCase(
                id="summarization",
                prompt="请总结以下内容的要点：人工智能技术正在快速发展，深度学习、自然语言处理等领域取得了重大突破。",
                expected_keywords=["人工智能", "发展", "突破"],
                category="摘要"
            )
        ]
    
    def calculate_quality_score(self, test_case: TestCase, response: str) -> float:
        """计算质量分数"""
        if not test_case.expected_keywords:
            return 0.5  # 默认分数
        
        score = 0.0
        keyword_count = 0
        
        for keyword in test_case.expected_keywords:
            if keyword.lower() in response.lower():
                keyword_count += 1
        
        # 关键词覆盖率
        keyword_coverage = keyword_count / len(test_case.expected_keywords)
        score += keyword_coverage * 0.4
        
        # 响应长度合理性
        response_length = len(response)
        if 50 <= response_length <= 500:
            score += 0.3
        elif response_length > 500:
            score += 0.2
        else:
            score += 0.1
        
        # 语言流畅性（简化评估）
        if response and not response.startswith("错误"):
            score += 0.3
        
        return min(score, 1.0)
    
    def test_single_api(self, provider_name: str, provider_client: Any, 
                       test_case: TestCase, model: str = None) -> TestResult:
        """测试单个API"""
        start_time = time.time()
        
        try:
            # 构建消息
            messages = [{"role": "user", "content": test_case.prompt}]
            
            # 调用API
            if hasattr(provider_client, 'chat'):
                response = provider_client.chat(
                    messages=messages,
                    model=model,
                    max_tokens=test_case.max_tokens,
                    temperature=test_case.temperature
                )
                response_text = response.content
                token_usage = response.usage
                actual_model = response.model
            else:
                # 兼容不同的API接口
                response_text = "API接口不兼容"
                token_usage = {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0}
                actual_model = model or "unknown"
            
            response_time = time.time() - start_time
            
            # 计算成本
            cost = provider_client.estimate_cost(
                token_usage.get("prompt_tokens", 0),
                token_usage.get("completion_tokens", 0),
                actual_model
            ) if hasattr(provider_client, 'estimate_cost') else 0.0
            
            # 计算质量分数
            quality_score = self.calculate_quality_score(test_case, response_text)
            
            return TestResult(
                test_case_id=test_case.id,
                provider=provider_name,
                model=actual_model,
                response=response_text,
                response_time=response_time,
                token_usage=token_usage,
                cost=cost,
                quality_score=quality_score,
                timestamp=time.time()
            )
            
        except Exception as e:
            return TestResult(
                test_case_id=test_case.id,
                provider=provider_name,
                model=model or "unknown",
                response="",
                response_time=time.time() - start_time,
                token_usage={"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0},
                cost=0.0,
                quality_score=0.0,
                error=str(e),
                timestamp=time.time()
            )
    
    def run_sequential_tests(self, test_cases: List[TestCase]) -> List[TestResult]:
        """运行顺序测试"""
        results = []
        
        for provider_name, provider_client in self.providers.items():
            print(f"测试提供商: {provider_name}")
            
            for test_case in test_cases:
                print(f"  测试用例: {test_case.id}")
                result = self.test_single_api(provider_name, provider_client, test_case)
                results.append(result)
                
                # 避免请求过于频繁
                time.sleep(1)
        
        return results
    
    def run_concurrent_tests(self, test_cases: List[TestCase], 
                           max_workers: int = 5) -> List[TestResult]:
        """运行并发测试"""
        results = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = []
            
            for provider_name, provider_client in self.providers.items():
                for test_case in test_cases:
                    future = executor.submit(
                        self.test_single_api, 
                        provider_name, 
                        provider_client, 
                        test_case
                    )
                    futures.append(future)
            
            for future in as_completed(futures):
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    print(f"并发测试出错: {e}")
        
        return results
    
    def run_stress_test(self, test_case: TestCase, provider_name: str, 
                       requests_count: int = 10, concurrent_users: int = 3) -> Dict[str, Any]:
        """运行压力测试"""
        provider_client = self.providers[provider_name]
        results = []
        
        def stress_test_worker():
            worker_results = []
            for _ in range(requests_count // concurrent_users):
                result = self.test_single_api(provider_name, provider_client, test_case)
                worker_results.append(result)
                time.sleep(0.1)  # 短暂延迟
            return worker_results
        
        with ThreadPoolExecutor(max_workers=concurrent_users) as executor:
            futures = [executor.submit(stress_test_worker) for _ in range(concurrent_users)]
            
            for future in as_completed(futures):
                try:
                    worker_results = future.result()
                    results.extend(worker_results)
                except Exception as e:
                    print(f"压力测试工作线程出错: {e}")
        
        # 分析压力测试结果
        response_times = [r.response_time for r in results if r.error is None]
        error_count = len([r for r in results if r.error is not None])
        
        if response_times:
            stress_analysis = {
                "total_requests": len(results),
                "successful_requests": len(response_times),
                "error_count": error_count,
                "error_rate": error_count / len(results),
                "avg_response_time": statistics.mean(response_times),
                "min_response_time": min(response_times),
                "max_response_time": max(response_times),
                "median_response_time": statistics.median(response_times),
                "std_response_time": statistics.stdev(response_times) if len(response_times) > 1 else 0
            }
        else:
            stress_analysis = {
                "total_requests": len(results),
                "successful_requests": 0,
                "error_count": error_count,
                "error_rate": 1.0,
                "avg_response_time": 0,
                "min_response_time": 0,
                "max_response_time": 0,
                "median_response_time": 0,
                "std_response_time": 0
            }
        
        return stress_analysis

# 使用示例
def demo_performance_tester():
    """性能测试器演示"""
    # 注意：需要配置真实的API客户端
    providers = {
        # "openai": OpenAIClient("your-api-key"),
        # "claude": ClaudeClient("your-api-key"),
        # "gemini": GeminiClient("your-api-key")
    }
    
    if not providers:
        print("请配置API提供商后运行测试")
        return
    
    tester = PerformanceTester(providers)
    test_cases = tester.create_test_cases()
    
    print("=== API性能测试开始 ===")
    
    # 顺序测试
    print("\n1. 顺序测试")
    sequential_results = tester.run_sequential_tests(test_cases)
    
    # 并发测试
    print("\n2. 并发测试")
    concurrent_results = tester.run_concurrent_tests(test_cases, max_workers=3)
    
    # 压力测试
    print("\n3. 压力测试")
    if providers:
        provider_name = list(providers.keys())[0]
        stress_result = tester.run_stress_test(
            test_cases[0], 
            provider_name, 
            requests_count=20, 
            concurrent_users=5
        )
        print(f"压力测试结果: {stress_result}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    demo_performance_tester()
```

### 2. 报告生成器

```python
# performance_tester/reporting/report_generator.py
import json
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import List, Dict, Any
from datetime import datetime
import os

class ReportGenerator:
    """性能测试报告生成器"""
    
    def __init__(self, results: List[TestResult]):
        self.results = results
        self.df = self._create_dataframe()
    
    def _create_dataframe(self) -> pd.DataFrame:
        """将测试结果转换为DataFrame"""
        data = []
        for result in self.results:
            data.append({
                'test_case_id': result.test_case_id,
                'provider': result.provider,
                'model': result.model,
                'response_time': result.response_time,
                'prompt_tokens': result.token_usage.get('prompt_tokens', 0),
                'completion_tokens': result.token_usage.get('completion_tokens', 0),
                'total_tokens': result.token_usage.get('total_tokens', 0),
                'cost': result.cost,
                'quality_score': result.quality_score,
                'has_error': result.error is not None,
                'timestamp': result.timestamp
            })
        return pd.DataFrame(data)
    
    def generate_summary_stats(self) -> Dict[str, Any]:
        """生成汇总统计"""
        if self.df.empty:
            return {}
        
        summary = {}
        
        # 按提供商分组统计
        for provider in self.df['provider'].unique():
            provider_data = self.df[self.df['provider'] == provider]
            
            summary[provider] = {
                'total_tests': len(provider_data),
                'success_rate': (1 - provider_data['has_error'].mean()) * 100,
                'avg_response_time': provider_data['response_time'].mean(),
                'avg_cost': provider_data['cost'].mean(),
                'avg_quality_score': provider_data['quality_score'].mean(),
                'total_cost': provider_data['cost'].sum(),
                'avg_tokens': provider_data['total_tokens'].mean()
            }
        
        return summary
    
    def create_performance_charts(self, output_dir: str = "reports"):
        """创建性能图表"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 设置图表样式
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
        # 1. 响应时间对比
        plt.figure(figsize=(12, 6))
        sns.boxplot(data=self.df, x='provider', y='response_time')
        plt.title('API响应时间对比')
        plt.ylabel('响应时间 (秒)')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(f'{output_dir}/response_time_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 2. 成本对比
        plt.figure(figsize=(12, 6))
        cost_by_provider = self.df.groupby('provider')['cost'].sum().reset_index()
        sns.barplot(data=cost_by_provider, x='provider', y='cost')
        plt.title('API成本对比')
        plt.ylabel('总成本 ($)')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(f'{output_dir}/cost_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 3. 质量分数对比
        plt.figure(figsize=(12, 6))
        sns.boxplot(data=self.df, x='provider', y='quality_score')
        plt.title('API质量分数对比')
        plt.ylabel('质量分数')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(f'{output_dir}/quality_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 4. 综合性能雷达图
        self._create_radar_chart(output_dir)
        
        print(f"图表已保存到 {output_dir} 目录")
    
    def _create_radar_chart(self, output_dir: str):
        """创建雷达图"""
        import numpy as np
        
        # 计算各提供商的标准化指标
        providers = self.df['provider'].unique()
        metrics = ['response_time', 'cost', 'quality_score']
        
        # 标准化数据（响应时间和成本越低越好，质量分数越高越好）
        normalized_data = {}
        for provider in providers:
            provider_data = self.df[self.df['provider'] == provider]
            normalized_data[provider] = []
            
            # 响应时间（倒数标准化）
            avg_response_time = provider_data['response_time'].mean()
            normalized_response = 1 / (1 + avg_response_time) * 10
            normalized_data[provider].append(normalized_response)
            
            # 成本（倒数标准化）
            avg_cost = provider_data['cost'].mean()
            normalized_cost = 1 / (1 + avg_cost * 1000) * 10  # 乘以1000放大差异
            normalized_data[provider].append(normalized_cost)
            
            # 质量分数（直接使用）
            avg_quality = provider_data['quality_score'].mean()
            normalized_data[provider].append(avg_quality * 10)
        
        # 创建雷达图
        angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
        angles += angles[:1]  # 闭合图形
        
        fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
        
        for provider in providers:
            values = normalized_data[provider]
            values += values[:1]  # 闭合图形
            ax.plot(angles, values, 'o-', linewidth=2, label=provider)
            ax.fill(angles, values, alpha=0.25)
        
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(['响应速度', '成本效益', '质量分数'])
        ax.set_ylim(0, 10)
        ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        ax.set_title('API综合性能对比', size=16, pad=20)
        
        plt.tight_layout()
        plt.savefig(f'{output_dir}/performance_radar.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def generate_html_report(self, output_file: str = "reports/performance_report.html"):
        """生成HTML报告"""
        summary_stats = self.generate_summary_stats()
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>LLM API性能测试报告</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                .header {{ text-align: center; color: #333; }}
                .summary {{ background-color: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0; }}
                .provider-section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
                .metric {{ display: inline-block; margin: 10px; padding: 10px; background-color: #e9e9e9; border-radius: 3px; }}
                .chart {{ text-align: center; margin: 20px 0; }}
                table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>LLM API性能测试报告</h1>
                <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
            
            <div class="summary">
                <h2>测试概览</h2>
                <p>总测试数量: {len(self.df)}</p>
                <p>测试提供商: {', '.join(self.df['provider'].unique())}</p>
                <p>测试用例: {', '.join(self.df['test_case_id'].unique())}</p>
            </div>
        """
        
        # 添加各提供商的详细统计
        for provider, stats in summary_stats.items():
            html_content += f"""
            <div class="provider-section">
                <h3>{provider}</h3>
                <div class="metric">成功率: {stats['success_rate']:.1f}%</div>
                <div class="metric">平均响应时间: {stats['avg_response_time']:.2f}s</div>
                <div class="metric">平均成本: ${stats['avg_cost']:.4f}</div>
                <div class="metric">平均质量分数: {stats['avg_quality_score']:.2f}</div>
                <div class="metric">总成本: ${stats['total_cost']:.4f}</div>
            </div>
            """
        
        # 添加图表
        html_content += """
            <div class="chart">
                <h2>性能图表</h2>
                <img src="response_time_comparison.png" alt="响应时间对比" style="max-width: 100%; margin: 10px;">
                <img src="cost_comparison.png" alt="成本对比" style="max-width: 100%; margin: 10px;">
                <img src="quality_comparison.png" alt="质量对比" style="max-width: 100%; margin: 10px;">
                <img src="performance_radar.png" alt="综合性能雷达图" style="max-width: 100%; margin: 10px;">
            </div>
        """
        
        html_content += """
        </body>
        </html>
        """
        
        # 保存HTML文件
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"HTML报告已生成: {output_file}")
    
    def export_raw_data(self, output_file: str = "reports/raw_data.json"):
        """导出原始数据"""
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # 转换为可序列化的格式
        export_data = []
        for result in self.results:
            export_data.append({
                'test_case_id': result.test_case_id,
                'provider': result.provider,
                'model': result.model,
                'response': result.response,
                'response_time': result.response_time,
                'token_usage': result.token_usage,
                'cost': result.cost,
                'quality_score': result.quality_score,
                'error': result.error,
                'timestamp': result.timestamp
            })
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        print(f"原始数据已导出: {output_file}")

# 完整的测试流程示例
def run_complete_performance_test():
    """运行完整的性能测试流程"""
    print("=== LLM API性能测试工具 ===")
    print("注意：需要配置真实的API密钥才能运行完整测试")
    
    # 1. 初始化提供商（示例）
    providers = {
        # "openai": OpenAIClient("your-openai-key"),
        # "claude": ClaudeClient("your-claude-key"),
        # "gemini": GeminiClient("your-gemini-key")
    }
    
    if not providers:
        print("请配置API提供商")
        return
    
    # 2. 运行测试
    tester = PerformanceTester(providers)
    test_cases = tester.create_test_cases()
    
    print("开始性能测试...")
    results = tester.run_sequential_tests(test_cases)
    
    # 3. 生成报告
    print("生成测试报告...")
    report_generator = ReportGenerator(results)
    
    # 创建图表
    report_generator.create_performance_charts()
    
    # 生成HTML报告
    report_generator.generate_html_report()
    
    # 导出原始数据
    report_generator.export_raw_data()
    
    # 显示汇总统计
    summary = report_generator.generate_summary_stats()
    print("\n=== 测试结果汇总 ===")
    for provider, stats in summary.items():
        print(f"\n{provider}:")
        for metric, value in stats.items():
            print(f"  {metric}: {value}")
    
    print("\n测试完成！请查看 reports 目录中的详细报告。")

if __name__ == "__main__":
    run_complete_performance_test()
```

## 项目扩展功能

### 1. 配置文件支持

```yaml
# config/test_config.yaml
test_settings:
  concurrent_users: 5
  requests_per_user: 10
  timeout: 30
  retry_attempts: 3

providers:
  openai:
    models: ["gpt-3.5-turbo", "gpt-4"]
    default_model: "gpt-3.5-turbo"
  claude:
    models: ["claude-3-sonnet-20240229"]
    default_model: "claude-3-sonnet-20240229"
  gemini:
    models: ["gemini-pro"]
    default_model: "gemini-pro"

test_cases:
  - id: "creative_writing"
    prompt: "写一个关于未来城市的短故事"
    category: "创意写作"
    expected_keywords: ["未来", "城市", "科技"]
  - id: "factual_qa"
    prompt: "请解释什么是机器学习"
    category: "事实问答"
    expected_keywords: ["机器学习", "算法", "数据"]
```

### 2. 实时监控界面

```python
# monitoring/real_time_monitor.py
import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import pandas as pd

def create_monitoring_dashboard():
    """创建实时监控仪表板"""
    st.title("LLM API实时性能监控")
    
    # 侧边栏配置
    st.sidebar.header("监控配置")
    refresh_interval = st.sidebar.slider("刷新间隔(秒)", 5, 60, 10)
    selected_providers = st.sidebar.multiselect(
        "选择提供商", 
        ["OpenAI", "Claude", "Gemini"],
        default=["OpenAI", "Claude"]
    )
    
    # 主要指标卡片
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("总请求数", "1,234", "12")
    with col2:
        st.metric("平均响应时间", "1.2s", "-0.1s")
    with col3:
        st.metric("成功率", "98.5%", "0.5%")
    with col4:
        st.metric("总成本", "$12.34", "$1.23")
    
    # 实时图表
    st.subheader("实时性能图表")
    
    # 响应时间趋势
    fig_response_time = go.Figure()
    # 添加数据...
    st.plotly_chart(fig_response_time, use_container_width=True)
    
    # 成本趋势
    fig_cost = go.Figure()
    # 添加数据...
    st.plotly_chart(fig_cost, use_container_width=True)
    
    # 自动刷新
    time.sleep(refresh_interval)
    st.experimental_rerun()
```

## 使用指南

### 1. 安装依赖

```bash
pip install pandas matplotlib seaborn plotly streamlit
pip install openai anthropic google-generativeai
```

### 2. 配置API密钥

```bash
export OPENAI_API_KEY="your-openai-key"
export ANTHROPIC_API_KEY="your-claude-key"
export GOOGLE_API_KEY="your-gemini-key"
```

### 3. 运行测试

```bash
python performance_tester/main.py
```

### 4. 查看报告

测试完成后，在 `reports/` 目录中查看：
- `performance_report.html` - 详细HTML报告
- `*.png` - 性能图表
- `raw_data.json` - 原始测试数据

## 项目价值

1. **客观评估**：提供多维度的API性能评估
2. **成本优化**：帮助选择最具性价比的API
3. **质量保证**：确保API输出质量满足要求
4. **监控告警**：实时监控API性能变化
5. **决策支持**：为技术选型提供数据支持

这个项目为LLM API的选择和使用提供了科学的评估工具，是企业级应用的重要基础设施。
