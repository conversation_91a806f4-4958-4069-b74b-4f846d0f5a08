# 2.3 多厂商API对比与整合

## 学习目标

完成本节学习后，学员将能够：
1. 理解主流LLM API提供商的特点和差异
2. 掌握多厂商API的统一调用方法
3. 实现智能的API选择和负载均衡策略
4. 构建容错性强的多厂商集成系统
5. 优化多厂商使用的成本和性能

## 主流LLM API提供商对比

### 厂商概览对比表

| 厂商 | 主要模型 | 优势 | 劣势 | 定价策略 | 适用场景 |
|------|----------|------|------|----------|----------|
| OpenAI | GPT-4, GPT-3.5 | 性能强、生态好 | 成本高、依赖性强 | 按token计费 | 通用应用 |
| Anthropic | Claude-3 | 安全性高、长上下文 | 可用性有限 | 按token计费 | 企业应用 |
| Google | Gemini Pro | 多模态、免费额度 | 新产品、稳定性待验证 | 免费+付费 | 多模态应用 |
| Cohere | Command | 企业级、可定制 | 知名度低 | 企业定价 | 企业NLP |
| 百度 | 文心一言 | 中文优化、合规 | 国际化程度低 | 按次计费 | 中文应用 |
| 阿里 | 通义千问 | 中文优化、生态整合 | 相对较新 | 按token计费 | 阿里云生态 |

### 详细技术对比

#### API接口设计对比

**OpenAI风格（事实标准）**
```python
# OpenAI API调用格式
{
    "model": "gpt-3.5-turbo",
    "messages": [
        {"role": "user", "content": "Hello"}
    ],
    "temperature": 0.7,
    "max_tokens": 150
}
```

**Anthropic Claude风格**
```python
# Claude API调用格式
{
    "model": "claude-3-sonnet-20240229",
    "max_tokens": 1024,
    "messages": [
        {"role": "user", "content": "Hello"}
    ]
}
```

**Google Gemini风格**
```python
# Gemini API调用格式
{
    "contents": [
        {"parts": [{"text": "Hello"}]}
    ],
    "generationConfig": {
        "temperature": 0.7,
        "maxOutputTokens": 150
    }
}
```

### 性能基准测试图表

**制作说明**：创建雷达图展示各厂商在不同维度的表现

```
雷达图维度：
1. 响应速度 (0-10分)
2. 输出质量 (0-10分)  
3. 成本效益 (0-10分)
4. 可用性 (0-10分)
5. 文档质量 (0-10分)
6. 生态支持 (0-10分)

数据示例：
OpenAI: [8, 9, 6, 9, 9, 10]
Claude: [7, 9, 7, 7, 8, 6]
Gemini: [9, 8, 9, 8, 7, 7]
```

## 统一API接口设计

### 抽象基类设计

```python
# src/api_clients/base_llm_client.py
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Iterator
from dataclasses import dataclass
from enum import Enum

class ModelType(Enum):
    """模型类型枚举"""
    CHAT = "chat"
    COMPLETION = "completion"
    EMBEDDING = "embedding"

@dataclass
class LLMResponse:
    """统一的LLM响应格式"""
    content: str
    model: str
    usage: Dict[str, int]
    finish_reason: str
    metadata: Dict[str, Any] = None

@dataclass
class LLMMessage:
    """统一的消息格式"""
    role: str  # system, user, assistant
    content: str
    metadata: Dict[str, Any] = None

class BaseLLMClient(ABC):
    """LLM客户端抽象基类"""
    
    def __init__(self, api_key: str, **kwargs):
        self.api_key = api_key
        self.config = kwargs
    
    @abstractmethod
    def chat(self, messages: List[LLMMessage], model: str = None, 
             **kwargs) -> LLMResponse:
        """聊天接口"""
        pass
    
    @abstractmethod
    def stream_chat(self, messages: List[LLMMessage], model: str = None,
                   **kwargs) -> Iterator[str]:
        """流式聊天接口"""
        pass
    
    @abstractmethod
    def get_available_models(self) -> List[str]:
        """获取可用模型列表"""
        pass
    
    @abstractmethod
    def estimate_cost(self, input_tokens: int, output_tokens: int, 
                     model: str) -> float:
        """估算成本"""
        pass
    
    def validate_config(self) -> bool:
        """验证配置"""
        return bool(self.api_key)
```

### OpenAI客户端实现

```python
# src/api_clients/openai_client.py
import openai
from typing import List, Iterator
from .base_llm_client import BaseLLMClient, LLMResponse, LLMMessage

class OpenAIClient(BaseLLMClient):
    """OpenAI API客户端"""
    
    def __init__(self, api_key: str, **kwargs):
        super().__init__(api_key, **kwargs)
        self.client = openai.OpenAI(api_key=api_key)
        self.default_model = kwargs.get('default_model', 'gpt-3.5-turbo')
    
    def _convert_messages(self, messages: List[LLMMessage]) -> List[Dict]:
        """转换消息格式"""
        return [{"role": msg.role, "content": msg.content} for msg in messages]
    
    def chat(self, messages: List[LLMMessage], model: str = None, 
             **kwargs) -> LLMResponse:
        """聊天接口实现"""
        model = model or self.default_model
        openai_messages = self._convert_messages(messages)
        
        try:
            response = self.client.chat.completions.create(
                model=model,
                messages=openai_messages,
                **kwargs
            )
            
            choice = response.choices[0]
            return LLMResponse(
                content=choice.message.content,
                model=response.model,
                usage={
                    'prompt_tokens': response.usage.prompt_tokens,
                    'completion_tokens': response.usage.completion_tokens,
                    'total_tokens': response.usage.total_tokens
                },
                finish_reason=choice.finish_reason,
                metadata={'provider': 'openai'}
            )
            
        except Exception as e:
            raise Exception(f"OpenAI API调用失败: {str(e)}")
    
    def stream_chat(self, messages: List[LLMMessage], model: str = None,
                   **kwargs) -> Iterator[str]:
        """流式聊天实现"""
        model = model or self.default_model
        openai_messages = self._convert_messages(messages)
        
        try:
            stream = self.client.chat.completions.create(
                model=model,
                messages=openai_messages,
                stream=True,
                **kwargs
            )
            
            for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            yield f"错误: {str(e)}"
    
    def get_available_models(self) -> List[str]:
        """获取可用模型"""
        try:
            models = self.client.models.list()
            return [model.id for model in models.data 
                   if 'gpt' in model.id.lower()]
        except Exception:
            return ['gpt-3.5-turbo', 'gpt-4']
    
    def estimate_cost(self, input_tokens: int, output_tokens: int, 
                     model: str) -> float:
        """估算成本"""
        pricing = {
            'gpt-3.5-turbo': {'input': 0.0015, 'output': 0.002},
            'gpt-4': {'input': 0.03, 'output': 0.06},
            'gpt-4-turbo': {'input': 0.01, 'output': 0.03}
        }
        
        model_pricing = pricing.get(model, pricing['gpt-3.5-turbo'])
        return (input_tokens / 1000 * model_pricing['input'] + 
                output_tokens / 1000 * model_pricing['output'])
```

### Anthropic Claude客户端实现

```python
# src/api_clients/claude_client.py
import anthropic
from typing import List, Iterator
from .base_llm_client import BaseLLMClient, LLMResponse, LLMMessage

class ClaudeClient(BaseLLMClient):
    """Anthropic Claude API客户端"""
    
    def __init__(self, api_key: str, **kwargs):
        super().__init__(api_key, **kwargs)
        self.client = anthropic.Anthropic(api_key=api_key)
        self.default_model = kwargs.get('default_model', 'claude-3-sonnet-20240229')
    
    def _convert_messages(self, messages: List[LLMMessage]) -> List[Dict]:
        """转换消息格式"""
        converted = []
        for msg in messages:
            if msg.role == 'system':
                # Claude的系统消息需要特殊处理
                continue
            converted.append({"role": msg.role, "content": msg.content})
        return converted
    
    def _extract_system_message(self, messages: List[LLMMessage]) -> str:
        """提取系统消息"""
        for msg in messages:
            if msg.role == 'system':
                return msg.content
        return ""
    
    def chat(self, messages: List[LLMMessage], model: str = None, 
             **kwargs) -> LLMResponse:
        """聊天接口实现"""
        model = model or self.default_model
        claude_messages = self._convert_messages(messages)
        system_message = self._extract_system_message(messages)
        
        try:
            # Claude API参数
            api_params = {
                'model': model,
                'max_tokens': kwargs.get('max_tokens', 1024),
                'messages': claude_messages
            }
            
            if system_message:
                api_params['system'] = system_message
            
            # 参数映射
            if 'temperature' in kwargs:
                api_params['temperature'] = kwargs['temperature']
            
            response = self.client.messages.create(**api_params)
            
            return LLMResponse(
                content=response.content[0].text,
                model=response.model,
                usage={
                    'prompt_tokens': response.usage.input_tokens,
                    'completion_tokens': response.usage.output_tokens,
                    'total_tokens': response.usage.input_tokens + response.usage.output_tokens
                },
                finish_reason=response.stop_reason,
                metadata={'provider': 'anthropic'}
            )
            
        except Exception as e:
            raise Exception(f"Claude API调用失败: {str(e)}")
    
    def stream_chat(self, messages: List[LLMMessage], model: str = None,
                   **kwargs) -> Iterator[str]:
        """流式聊天实现"""
        model = model or self.default_model
        claude_messages = self._convert_messages(messages)
        system_message = self._extract_system_message(messages)
        
        try:
            api_params = {
                'model': model,
                'max_tokens': kwargs.get('max_tokens', 1024),
                'messages': claude_messages,
                'stream': True
            }
            
            if system_message:
                api_params['system'] = system_message
            
            with self.client.messages.stream(**api_params) as stream:
                for text in stream.text_stream:
                    yield text
                    
        except Exception as e:
            yield f"错误: {str(e)}"
    
    def get_available_models(self) -> List[str]:
        """获取可用模型"""
        return [
            'claude-3-opus-20240229',
            'claude-3-sonnet-20240229', 
            'claude-3-haiku-20240307'
        ]
    
    def estimate_cost(self, input_tokens: int, output_tokens: int, 
                     model: str) -> float:
        """估算成本"""
        pricing = {
            'claude-3-opus-20240229': {'input': 0.015, 'output': 0.075},
            'claude-3-sonnet-20240229': {'input': 0.003, 'output': 0.015},
            'claude-3-haiku-20240307': {'input': 0.00025, 'output': 0.00125}
        }
        
        model_pricing = pricing.get(model, pricing['claude-3-sonnet-20240229'])
        return (input_tokens / 1000 * model_pricing['input'] + 
                output_tokens / 1000 * model_pricing['output'])
```

### Google Gemini客户端实现

```python
# src/api_clients/gemini_client.py
import google.generativeai as genai
from typing import List, Iterator
from .base_llm_client import BaseLLMClient, LLMResponse, LLMMessage

class GeminiClient(BaseLLMClient):
    """Google Gemini API客户端"""
    
    def __init__(self, api_key: str, **kwargs):
        super().__init__(api_key, **kwargs)
        genai.configure(api_key=api_key)
        self.default_model = kwargs.get('default_model', 'gemini-pro')
    
    def _convert_messages(self, messages: List[LLMMessage]) -> List[Dict]:
        """转换消息格式为Gemini格式"""
        converted = []
        for msg in messages:
            if msg.role == 'system':
                # Gemini通过instruction处理系统消息
                continue
            
            # Gemini的角色映射
            role = 'user' if msg.role in ['user', 'system'] else 'model'
            converted.append({
                'role': role,
                'parts': [{'text': msg.content}]
            })
        return converted
    
    def _extract_system_instruction(self, messages: List[LLMMessage]) -> str:
        """提取系统指令"""
        for msg in messages:
            if msg.role == 'system':
                return msg.content
        return ""
    
    def chat(self, messages: List[LLMMessage], model: str = None, 
             **kwargs) -> LLMResponse:
        """聊天接口实现"""
        model_name = model or self.default_model
        system_instruction = self._extract_system_instruction(messages)
        
        try:
            # 创建模型实例
            if system_instruction:
                model = genai.GenerativeModel(
                    model_name=model_name,
                    system_instruction=system_instruction
                )
            else:
                model = genai.GenerativeModel(model_name=model_name)
            
            # 转换消息格式
            gemini_messages = self._convert_messages(messages)
            
            # 构建对话历史
            if len(gemini_messages) > 1:
                chat = model.start_chat(history=gemini_messages[:-1])
                response = chat.send_message(gemini_messages[-1]['parts'][0]['text'])
            else:
                response = model.generate_content(gemini_messages[-1]['parts'][0]['text'])
            
            return LLMResponse(
                content=response.text,
                model=model_name,
                usage={
                    'prompt_tokens': response.usage_metadata.prompt_token_count if hasattr(response, 'usage_metadata') else 0,
                    'completion_tokens': response.usage_metadata.candidates_token_count if hasattr(response, 'usage_metadata') else 0,
                    'total_tokens': response.usage_metadata.total_token_count if hasattr(response, 'usage_metadata') else 0
                },
                finish_reason=str(response.candidates[0].finish_reason) if response.candidates else 'unknown',
                metadata={'provider': 'google'}
            )
            
        except Exception as e:
            raise Exception(f"Gemini API调用失败: {str(e)}")
    
    def stream_chat(self, messages: List[LLMMessage], model: str = None,
                   **kwargs) -> Iterator[str]:
        """流式聊天实现"""
        model_name = model or self.default_model
        system_instruction = self._extract_system_instruction(messages)
        
        try:
            if system_instruction:
                model = genai.GenerativeModel(
                    model_name=model_name,
                    system_instruction=system_instruction
                )
            else:
                model = genai.GenerativeModel(model_name=model_name)
            
            gemini_messages = self._convert_messages(messages)
            
            # 流式生成
            if len(gemini_messages) > 1:
                chat = model.start_chat(history=gemini_messages[:-1])
                response = chat.send_message(
                    gemini_messages[-1]['parts'][0]['text'],
                    stream=True
                )
            else:
                response = model.generate_content(
                    gemini_messages[-1]['parts'][0]['text'],
                    stream=True
                )
            
            for chunk in response:
                if chunk.text:
                    yield chunk.text
                    
        except Exception as e:
            yield f"错误: {str(e)}"
    
    def get_available_models(self) -> List[str]:
        """获取可用模型"""
        try:
            models = genai.list_models()
            return [model.name.split('/')[-1] for model in models 
                   if 'generateContent' in model.supported_generation_methods]
        except Exception:
            return ['gemini-pro', 'gemini-pro-vision']
    
    def estimate_cost(self, input_tokens: int, output_tokens: int, 
                     model: str) -> float:
        """估算成本（Gemini有免费额度）"""
        # Gemini Pro有免费额度，这里返回付费价格
        pricing = {
            'gemini-pro': {'input': 0.0005, 'output': 0.0015},
            'gemini-pro-vision': {'input': 0.0005, 'output': 0.0015}
        }
        
        model_pricing = pricing.get(model, pricing['gemini-pro'])
        return (input_tokens / 1000 * model_pricing['input'] + 
                output_tokens / 1000 * model_pricing['output'])
```

## 多厂商管理器

```python
# src/api_clients/multi_provider_manager.py
from typing import Dict, List, Optional, Any
from enum import Enum
import random
import time
from .base_llm_client import BaseLLMClient, LLMResponse, LLMMessage
from .openai_client import OpenAIClient
from .claude_client import ClaudeClient
from .gemini_client import GeminiClient

class ProviderType(Enum):
    OPENAI = "openai"
    CLAUDE = "claude"
    GEMINI = "gemini"

class LoadBalanceStrategy(Enum):
    ROUND_ROBIN = "round_robin"
    RANDOM = "random"
    COST_OPTIMIZED = "cost_optimized"
    PERFORMANCE_OPTIMIZED = "performance_optimized"

class MultiProviderManager:
    """多厂商LLM管理器"""
    
    def __init__(self):
        self.providers: Dict[ProviderType, BaseLLMClient] = {}
        self.provider_configs = {}
        self.current_provider_index = 0
        self.provider_stats = {}
        self.fallback_order = [ProviderType.OPENAI, ProviderType.CLAUDE, ProviderType.GEMINI]
    
    def add_provider(self, provider_type: ProviderType, api_key: str, **kwargs):
        """添加API提供商"""
        try:
            if provider_type == ProviderType.OPENAI:
                client = OpenAIClient(api_key, **kwargs)
            elif provider_type == ProviderType.CLAUDE:
                client = ClaudeClient(api_key, **kwargs)
            elif provider_type == ProviderType.GEMINI:
                client = GeminiClient(api_key, **kwargs)
            else:
                raise ValueError(f"不支持的提供商类型: {provider_type}")
            
            if client.validate_config():
                self.providers[provider_type] = client
                self.provider_configs[provider_type] = kwargs
                self.provider_stats[provider_type] = {
                    'success_count': 0,
                    'error_count': 0,
                    'total_cost': 0.0,
                    'avg_response_time': 0.0
                }
                print(f"成功添加提供商: {provider_type.value}")
            else:
                print(f"提供商配置验证失败: {provider_type.value}")
                
        except Exception as e:
            print(f"添加提供商失败 {provider_type.value}: {str(e)}")
    
    def remove_provider(self, provider_type: ProviderType):
        """移除API提供商"""
        if provider_type in self.providers:
            del self.providers[provider_type]
            del self.provider_configs[provider_type]
            del self.provider_stats[provider_type]
            print(f"已移除提供商: {provider_type.value}")
    
    def get_provider(self, strategy: LoadBalanceStrategy = LoadBalanceStrategy.ROUND_ROBIN,
                    preferred_provider: Optional[ProviderType] = None) -> Optional[BaseLLMClient]:
        """根据策略获取提供商"""
        if not self.providers:
            return None
        
        if preferred_provider and preferred_provider in self.providers:
            return self.providers[preferred_provider]
        
        available_providers = list(self.providers.keys())
        
        if strategy == LoadBalanceStrategy.ROUND_ROBIN:
            provider_type = available_providers[self.current_provider_index % len(available_providers)]
            self.current_provider_index += 1
            
        elif strategy == LoadBalanceStrategy.RANDOM:
            provider_type = random.choice(available_providers)
            
        elif strategy == LoadBalanceStrategy.COST_OPTIMIZED:
            # 选择成本最低的提供商（简化实现）
            cost_ranking = {
                ProviderType.GEMINI: 1,
                ProviderType.OPENAI: 2,
                ProviderType.CLAUDE: 3
            }
            provider_type = min(available_providers, key=lambda p: cost_ranking.get(p, 999))
            
        elif strategy == LoadBalanceStrategy.PERFORMANCE_OPTIMIZED:
            # 选择性能最好的提供商
            provider_type = min(available_providers, 
                              key=lambda p: self.provider_stats[p]['avg_response_time'])
        
        return self.providers[provider_type]
    
    def chat_with_fallback(self, messages: List[LLMMessage], 
                          strategy: LoadBalanceStrategy = LoadBalanceStrategy.ROUND_ROBIN,
                          preferred_provider: Optional[ProviderType] = None,
                          **kwargs) -> LLMResponse:
        """带容错的聊天接口"""
        
        # 确定尝试顺序
        if preferred_provider and preferred_provider in self.providers:
            try_order = [preferred_provider] + [p for p in self.fallback_order if p != preferred_provider and p in self.providers]
        else:
            primary_provider = None
            for provider_type in self.providers.keys():
                if strategy == LoadBalanceStrategy.ROUND_ROBIN:
                    primary_provider = list(self.providers.keys())[self.current_provider_index % len(self.providers)]
                    self.current_provider_index += 1
                    break
                elif strategy == LoadBalanceStrategy.RANDOM:
                    primary_provider = random.choice(list(self.providers.keys()))
                    break
            
            try_order = [primary_provider] + [p for p in self.fallback_order if p != primary_provider and p in self.providers]
        
        last_error = None
        
        for provider_type in try_order:
            if provider_type not in self.providers:
                continue
                
            provider = self.providers[provider_type]
            start_time = time.time()
            
            try:
                response = provider.chat(messages, **kwargs)
                
                # 更新统计信息
                response_time = time.time() - start_time
                stats = self.provider_stats[provider_type]
                stats['success_count'] += 1
                stats['avg_response_time'] = (
                    (stats['avg_response_time'] * (stats['success_count'] - 1) + response_time) / 
                    stats['success_count']
                )
                
                # 估算并记录成本
                if response.usage:
                    cost = provider.estimate_cost(
                        response.usage['prompt_tokens'],
                        response.usage['completion_tokens'],
                        response.model
                    )
                    stats['total_cost'] += cost
                
                return response
                
            except Exception as e:
                last_error = e
                self.provider_stats[provider_type]['error_count'] += 1
                print(f"提供商 {provider_type.value} 调用失败: {str(e)}")
                continue
        
        raise Exception(f"所有提供商都调用失败，最后错误: {str(last_error)}")
    
    def get_provider_stats(self) -> Dict[str, Any]:
        """获取提供商统计信息"""
        stats_summary = {}
        
        for provider_type, stats in self.provider_stats.items():
            total_requests = stats['success_count'] + stats['error_count']
            success_rate = stats['success_count'] / total_requests if total_requests > 0 else 0
            
            stats_summary[provider_type.value] = {
                'success_count': stats['success_count'],
                'error_count': stats['error_count'],
                'success_rate': f"{success_rate:.2%}",
                'total_cost': f"${stats['total_cost']:.4f}",
                'avg_response_time': f"{stats['avg_response_time']:.2f}s"
            }
        
        return stats_summary
    
    def health_check(self) -> Dict[str, bool]:
        """健康检查"""
        health_status = {}
        
        test_messages = [LLMMessage(role="user", content="Hello")]
        
        for provider_type, provider in self.providers.items():
            try:
                response = provider.chat(test_messages, max_tokens=10)
                health_status[provider_type.value] = True
            except Exception:
                health_status[provider_type.value] = False
        
        return health_status

# 使用示例
def demo_multi_provider():
    """多厂商管理器演示"""
    manager = MultiProviderManager()
    
    # 添加提供商（需要真实的API密钥）
    # manager.add_provider(ProviderType.OPENAI, "your-openai-key")
    # manager.add_provider(ProviderType.CLAUDE, "your-claude-key")
    # manager.add_provider(ProviderType.GEMINI, "your-gemini-key")
    
    # 模拟添加（用于演示）
    print("=== 多厂商LLM管理器演示 ===")
    print("注意：需要配置真实的API密钥才能运行")
    
    # 测试消息
    messages = [
        LLMMessage(role="user", content="请简单介绍一下人工智能")
    ]
    
    # 不同策略的调用
    strategies = [
        LoadBalanceStrategy.ROUND_ROBIN,
        LoadBalanceStrategy.RANDOM,
        LoadBalanceStrategy.COST_OPTIMIZED
    ]
    
    for strategy in strategies:
        print(f"\n使用策略: {strategy.value}")
        try:
            # response = manager.chat_with_fallback(messages, strategy=strategy)
            # print(f"响应: {response.content[:100]}...")
            print("（需要配置API密钥）")
        except Exception as e:
            print(f"调用失败: {str(e)}")
    
    # 显示统计信息
    print(f"\n=== 提供商统计 ===")
    stats = manager.get_provider_stats()
    for provider, stat in stats.items():
        print(f"{provider}: {stat}")
    
    # 健康检查
    print(f"\n=== 健康检查 ===")
    health = manager.health_check()
    for provider, status in health.items():
        print(f"{provider}: {'✓' if status else '✗'}")

if __name__ == "__main__":
    demo_multi_provider()
```

## 实践练习

### 练习1：智能API选择器

**任务描述**：实现一个智能的API选择器，根据不同的任务类型自动选择最适合的API提供商。

**要求**：
- 根据任务类型（创意写作、事实问答、代码生成等）选择API
- 考虑成本、性能、质量等因素
- 实现动态调整策略

### 练习2：多厂商成本优化器

**任务描述**：开发一个成本优化器，在保证质量的前提下最小化API使用成本。

**要求**：
- 实时监控各厂商的成本和性能
- 动态调整使用策略
- 提供成本分析报告

## 评估标准

### 系统设计（40%）
- [ ] 抽象设计合理，易于扩展
- [ ] 错误处理完善
- [ ] 性能优化到位
- [ ] 代码质量高

### 功能实现（40%）
- [ ] 多厂商集成正确
- [ ] 负载均衡策略有效
- [ ] 容错机制可靠
- [ ] 统计监控完整

### 实用性（20%）
- [ ] 解决实际问题
- [ ] 用户体验良好
- [ ] 配置简单
- [ ] 文档完善

## 常见问题解答

### Q1: 如何选择合适的负载均衡策略？

**A1**: 
- **轮询**：适合提供商性能相近的情况
- **随机**：简单有效，适合大多数场景
- **成本优化**：预算有限时优先考虑
- **性能优化**：对响应时间要求高的场景

### Q2: 如何处理不同厂商的API差异？

**A2**:
1. **统一抽象层**：定义通用接口
2. **参数映射**：转换不同的参数格式
3. **响应标准化**：统一响应格式
4. **错误处理**：统一错误处理机制

### Q3: 多厂商集成的最佳实践是什么？

**A3**:
1. **配置管理**：集中管理API密钥和配置
2. **监控告警**：实时监控API状态
3. **成本控制**：设置使用限额和预警
4. **质量保证**：定期评估输出质量

## 下一步学习

完成本章后，建议：
1. 深入学习特定厂商的高级功能
2. 探索多模态API的集成
3. 学习API网关和微服务架构
4. 准备进入框架学习（LangChain、LlamaIndex）
