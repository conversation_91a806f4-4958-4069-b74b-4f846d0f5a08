# 4.2 LlamaIndex数据处理与检索

## 学习目标

完成本节学习后，学员将能够：
1. 深入理解LlamaIndex的数据处理架构和核心概念
2. 熟练掌握各种数据连接器和文档加载器的使用
3. 理解不同索引类型的特点和适用场景
4. 能够构建高效的查询引擎和检索系统
5. 掌握RAG（检索增强生成）系统的完整实现

## LlamaIndex核心架构深度解析

### 数据处理流水线架构

LlamaIndex采用模块化的数据处理流水线，将复杂的数据处理任务分解为可组合的步骤：

**制作说明**：创建LlamaIndex数据处理流程图

```
LlamaIndex数据处理流水线
┌─────────────────────────────────────────────────────────┐
│                   Data Ingestion Layer                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │ Document    │ │ Web         │ │ Database    │        │
│  │ Loaders     │ │ Scrapers    │ │ Connectors  │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
├─────────────────────────────────────────────────────────┤
│                 Document Processing Layer               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │ Text        │ │ Node        │ │ Metadata    │        │
│  │ Splitters   │ │ Parsers     │ │ Extractors  │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
├─────────────────────────────────────────────────────────┤
│                    Indexing Layer                       │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │ Vector      │ │ Tree        │ │ Keyword     │        │
│  │ Index       │ │ Index       │ │ Index       │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
├─────────────────────────────────────────────────────────┤
│                    Query Layer                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │ Query       │ │ Retrieval   │ │ Response    │        │
│  │ Engines     │ │ Strategies  │ │ Synthesis   │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
└─────────────────────────────────────────────────────────┘
```

### 核心概念详解

#### 1. 文档和节点（Documents & Nodes）

```python
# llamaindex_examples/core/document_processing.py
from llama_index.core import Document, SimpleDirectoryReader
from llama_index.core.node_parser import SimpleNodeParser, SentenceWindowNodeParser
from llama_index.core.text_splitter import TokenTextSplitter, SentenceSplitter
from llama_index.core.extractors import (
    TitleExtractor, 
    QuestionsAnsweredExtractor,
    SummaryExtractor,
    KeywordExtractor
)
from typing import List, Dict, Any, Optional
import asyncio

class DocumentProcessor:
    """文档处理器 - 处理各种格式的文档"""
    
    def __init__(self, chunk_size: int = 1024, chunk_overlap: int = 200):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.setup_processors()
    
    def setup_processors(self):
        """设置各种处理器"""
        # 文本分割器
        self.text_splitter = TokenTextSplitter(
            chunk_size=self.chunk_size,
            chunk_overlap=self.chunk_overlap
        )
        
        # 节点解析器
        self.node_parser = SimpleNodeParser.from_defaults(
            text_splitter=self.text_splitter
        )
        
        # 句子窗口解析器（用于更好的上下文保持）
        self.sentence_window_parser = SentenceWindowNodeParser.from_defaults(
            window_size=3,  # 前后各3个句子
            window_metadata_key="window",
            original_text_metadata_key="original_text"
        )
        
        # 元数据提取器
        self.extractors = [
            TitleExtractor(nodes=5),  # 从前5个节点提取标题
            QuestionsAnsweredExtractor(questions=3),  # 生成3个问题
            SummaryExtractor(summaries=["prev", "self"]),  # 生成摘要
            KeywordExtractor(keywords=10)  # 提取10个关键词
        ]
    
    def load_documents(self, input_dir: str, 
                      file_extractor: Optional[Dict] = None) -> List[Document]:
        """加载文档"""
        # 支持多种文件格式
        if file_extractor is None:
            file_extractor = {
                ".pdf": "PDFReader",
                ".docx": "DocxReader", 
                ".txt": "SimpleReader",
                ".md": "MarkdownReader",
                ".html": "HTMLTagReader"
            }
        
        reader = SimpleDirectoryReader(
            input_dir=input_dir,
            file_extractor=file_extractor,
            recursive=True
        )
        
        documents = reader.load_data()
        return documents
    
    def process_documents(self, documents: List[Document], 
                         use_sentence_window: bool = False,
                         extract_metadata: bool = True) -> List:
        """处理文档，生成节点"""
        # 选择解析器
        parser = self.sentence_window_parser if use_sentence_window else self.node_parser
        
        # 解析文档为节点
        nodes = parser.get_nodes_from_documents(documents)
        
        # 提取元数据
        if extract_metadata:
            nodes = self.extract_metadata(nodes)
        
        return nodes
    
    def extract_metadata(self, nodes: List) -> List:
        """提取节点元数据"""
        for extractor in self.extractors:
            nodes = extractor.extract(nodes)
        
        return nodes
    
    async def process_documents_async(self, documents: List[Document]) -> List:
        """异步处理文档"""
        # 将文档分批处理
        batch_size = 10
        all_nodes = []
        
        for i in range(0, len(documents), batch_size):
            batch = documents[i:i + batch_size]
            batch_nodes = await asyncio.to_thread(
                self.process_documents, batch
            )
            all_nodes.extend(batch_nodes)
        
        return all_nodes

# 自定义文档加载器
class CustomDocumentLoader:
    """自定义文档加载器 - 支持更多数据源"""
    
    def __init__(self):
        self.loaders = {}
        self.setup_loaders()
    
    def setup_loaders(self):
        """设置各种加载器"""
        # 数据库加载器
        self.loaders['database'] = self.load_from_database
        
        # API加载器
        self.loaders['api'] = self.load_from_api
        
        # 网页加载器
        self.loaders['web'] = self.load_from_web
        
        # 邮件加载器
        self.loaders['email'] = self.load_from_email
    
    def load_from_database(self, connection_string: str, 
                          query: str) -> List[Document]:
        """从数据库加载数据"""
        import sqlite3
        
        try:
            conn = sqlite3.connect(connection_string)
            cursor = conn.cursor()
            cursor.execute(query)
            
            documents = []
            for row in cursor.fetchall():
                # 假设第一列是文本内容，其他列是元数据
                text = str(row[0])
                metadata = {f"field_{i}": str(value) for i, value in enumerate(row[1:])}
                
                doc = Document(text=text, metadata=metadata)
                documents.append(doc)
            
            conn.close()
            return documents
            
        except Exception as e:
            print(f"数据库加载错误: {e}")
            return []
    
    def load_from_api(self, api_url: str, headers: Dict = None) -> List[Document]:
        """从API加载数据"""
        import requests
        
        try:
            response = requests.get(api_url, headers=headers or {})
            response.raise_for_status()
            
            data = response.json()
            documents = []
            
            # 根据API响应结构调整
            if isinstance(data, list):
                for item in data:
                    if isinstance(item, dict):
                        text = item.get('content', str(item))
                        metadata = {k: v for k, v in item.items() if k != 'content'}
                    else:
                        text = str(item)
                        metadata = {}
                    
                    doc = Document(text=text, metadata=metadata)
                    documents.append(doc)
            
            return documents
            
        except Exception as e:
            print(f"API加载错误: {e}")
            return []
    
    def load_from_web(self, urls: List[str]) -> List[Document]:
        """从网页加载数据"""
        from bs4 import BeautifulSoup
        import requests
        
        documents = []
        
        for url in urls:
            try:
                response = requests.get(url)
                response.raise_for_status()
                
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # 提取文本内容
                text = soup.get_text(strip=True)
                
                # 提取元数据
                metadata = {
                    'url': url,
                    'title': soup.title.string if soup.title else '',
                    'description': ''
                }
                
                # 提取描述
                desc_tag = soup.find('meta', attrs={'name': 'description'})
                if desc_tag:
                    metadata['description'] = desc_tag.get('content', '')
                
                doc = Document(text=text, metadata=metadata)
                documents.append(doc)
                
            except Exception as e:
                print(f"网页加载错误 {url}: {e}")
        
        return documents
    
    def load_from_email(self, email_config: Dict) -> List[Document]:
        """从邮件加载数据"""
        # 这里是示例实现，实际需要根据邮件服务配置
        documents = []
        
        # 模拟邮件数据
        sample_emails = [
            {
                'subject': '项目进度更新',
                'body': '本周项目进展顺利，已完成核心功能开发...',
                'sender': '<EMAIL>',
                'date': '2024-01-15'
            }
        ]
        
        for email in sample_emails:
            text = f"主题: {email['subject']}\n内容: {email['body']}"
            metadata = {
                'sender': email['sender'],
                'date': email['date'],
                'type': 'email'
            }
            
            doc = Document(text=text, metadata=metadata)
            documents.append(doc)
        
        return documents
```

#### 2. 索引系统深度解析

```python
# llamaindex_examples/core/indexing_system.py
from llama_index.core import (
    VectorStoreIndex, 
    TreeIndex, 
    KeywordTableIndex,
    ListIndex,
    KnowledgeGraphIndex
)
from llama_index.core.storage.storage_context import StorageContext
from llama_index.core.storage.docstore import SimpleDocumentStore
from llama_index.core.storage.index_store import SimpleIndexStore
from llama_index.core.vector_stores import SimpleVectorStore
from llama_index.embeddings.openai import OpenAIEmbedding
from typing import List, Dict, Any, Optional, Union
import pickle
import os

class IndexManager:
    """索引管理器 - 管理多种类型的索引"""
    
    def __init__(self, storage_dir: str = "./storage"):
        self.storage_dir = storage_dir
        self.indexes: Dict[str, Any] = {}
        self.storage_context = self.setup_storage_context()
        self.embedding_model = OpenAIEmbedding()
    
    def setup_storage_context(self) -> StorageContext:
        """设置存储上下文"""
        os.makedirs(self.storage_dir, exist_ok=True)
        
        # 创建存储组件
        docstore = SimpleDocumentStore()
        index_store = SimpleIndexStore()
        vector_store = SimpleVectorStore()
        
        storage_context = StorageContext.from_defaults(
            docstore=docstore,
            index_store=index_store,
            vector_store=vector_store
        )
        
        return storage_context
    
    def create_vector_index(self, name: str, nodes: List, 
                          embed_model: Optional[Any] = None) -> VectorStoreIndex:
        """创建向量索引"""
        embed_model = embed_model or self.embedding_model
        
        index = VectorStoreIndex(
            nodes=nodes,
            storage_context=self.storage_context,
            embed_model=embed_model,
            show_progress=True
        )
        
        self.indexes[name] = index
        return index
    
    def create_tree_index(self, name: str, nodes: List) -> TreeIndex:
        """创建树索引"""
        index = TreeIndex(
            nodes=nodes,
            storage_context=self.storage_context,
            show_progress=True
        )
        
        self.indexes[name] = index
        return index
    
    def create_keyword_index(self, name: str, nodes: List) -> KeywordTableIndex:
        """创建关键词索引"""
        index = KeywordTableIndex(
            nodes=nodes,
            storage_context=self.storage_context,
            show_progress=True
        )
        
        self.indexes[name] = index
        return index
    
    def create_list_index(self, name: str, nodes: List) -> ListIndex:
        """创建列表索引"""
        index = ListIndex(
            nodes=nodes,
            storage_context=self.storage_context,
            show_progress=True
        )
        
        self.indexes[name] = index
        return index
    
    def create_knowledge_graph_index(self, name: str, nodes: List) -> KnowledgeGraphIndex:
        """创建知识图谱索引"""
        index = KnowledgeGraphIndex(
            nodes=nodes,
            storage_context=self.storage_context,
            show_progress=True,
            max_triplets_per_chunk=10
        )
        
        self.indexes[name] = index
        return index
    
    def save_index(self, name: str, index_path: Optional[str] = None):
        """保存索引"""
        if name not in self.indexes:
            raise ValueError(f"索引 '{name}' 不存在")
        
        index_path = index_path or os.path.join(self.storage_dir, f"{name}_index")
        self.indexes[name].storage_context.persist(persist_dir=index_path)
        
        print(f"索引 '{name}' 已保存到 {index_path}")
    
    def load_index(self, name: str, index_type: str, index_path: str):
        """加载索引"""
        storage_context = StorageContext.from_defaults(persist_dir=index_path)
        
        if index_type == "vector":
            index = VectorStoreIndex.load_from_storage(storage_context)
        elif index_type == "tree":
            index = TreeIndex.load_from_storage(storage_context)
        elif index_type == "keyword":
            index = KeywordTableIndex.load_from_storage(storage_context)
        elif index_type == "list":
            index = ListIndex.load_from_storage(storage_context)
        else:
            raise ValueError(f"不支持的索引类型: {index_type}")
        
        self.indexes[name] = index
        print(f"索引 '{name}' 已从 {index_path} 加载")
        return index
    
    def get_index(self, name: str):
        """获取索引"""
        return self.indexes.get(name)
    
    def list_indexes(self) -> List[str]:
        """列出所有索引"""
        return list(self.indexes.keys())

# 高级索引策略
class AdvancedIndexingStrategy:
    """高级索引策略 - 智能选择和组合索引"""
    
    def __init__(self, index_manager: IndexManager):
        self.index_manager = index_manager
    
    def analyze_data_characteristics(self, nodes: List) -> Dict[str, Any]:
        """分析数据特征"""
        total_nodes = len(nodes)
        total_text_length = sum(len(node.text) for node in nodes)
        avg_text_length = total_text_length / total_nodes if total_nodes > 0 else 0
        
        # 分析文本类型
        has_structured_data = any(
            node.metadata.get('type') in ['table', 'list', 'structured'] 
            for node in nodes
        )
        
        has_long_documents = avg_text_length > 1000
        has_many_documents = total_nodes > 1000
        
        return {
            'total_nodes': total_nodes,
            'avg_text_length': avg_text_length,
            'has_structured_data': has_structured_data,
            'has_long_documents': has_long_documents,
            'has_many_documents': has_many_documents
        }
    
    def recommend_index_strategy(self, nodes: List) -> Dict[str, Any]:
        """推荐索引策略"""
        characteristics = self.analyze_data_characteristics(nodes)
        
        recommendations = {
            'primary_index': None,
            'secondary_indexes': [],
            'reasoning': []
        }
        
        # 基于数据特征推荐索引
        if characteristics['has_many_documents']:
            recommendations['primary_index'] = 'vector'
            recommendations['reasoning'].append("大量文档适合使用向量索引进行语义搜索")
            
            if characteristics['has_structured_data']:
                recommendations['secondary_indexes'].append('keyword')
                recommendations['reasoning'].append("结构化数据适合关键词索引")
        
        elif characteristics['has_long_documents']:
            recommendations['primary_index'] = 'tree'
            recommendations['reasoning'].append("长文档适合使用树索引进行层次化检索")
            
            recommendations['secondary_indexes'].append('vector')
            recommendations['reasoning'].append("向量索引作为辅助进行语义搜索")
        
        else:
            recommendations['primary_index'] = 'vector'
            recommendations['secondary_indexes'].append('keyword')
            recommendations['reasoning'].append("默认使用向量索引和关键词索引组合")
        
        return recommendations
    
    def create_hybrid_index(self, name: str, nodes: List) -> Dict[str, Any]:
        """创建混合索引"""
        strategy = self.recommend_index_strategy(nodes)
        
        indexes = {}
        
        # 创建主索引
        primary_type = strategy['primary_index']
        if primary_type == 'vector':
            indexes['primary'] = self.index_manager.create_vector_index(
                f"{name}_vector", nodes
            )
        elif primary_type == 'tree':
            indexes['primary'] = self.index_manager.create_tree_index(
                f"{name}_tree", nodes
            )
        
        # 创建辅助索引
        for secondary_type in strategy['secondary_indexes']:
            if secondary_type == 'keyword':
                indexes['keyword'] = self.index_manager.create_keyword_index(
                    f"{name}_keyword", nodes
                )
            elif secondary_type == 'vector' and primary_type != 'vector':
                indexes['vector'] = self.index_manager.create_vector_index(
                    f"{name}_vector", nodes
                )
        
        return {
            'indexes': indexes,
            'strategy': strategy
        }

# 使用示例
def demo_indexing_system():
    """演示索引系统的使用"""
    # 创建索引管理器
    index_manager = IndexManager("./demo_storage")
    
    # 模拟一些节点数据
    from llama_index.core import Document
    
    documents = [
        Document(text="人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。"),
        Document(text="机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习和改进。"),
        Document(text="深度学习是机器学习的一个分支，它使用神经网络来模拟人脑的学习过程。")
    ]
    
    # 处理文档
    processor = DocumentProcessor()
    nodes = processor.process_documents(documents)
    
    # 创建不同类型的索引
    vector_index = index_manager.create_vector_index("demo_vector", nodes)
    keyword_index = index_manager.create_keyword_index("demo_keyword", nodes)
    
    # 使用高级索引策略
    strategy_manager = AdvancedIndexingStrategy(index_manager)
    hybrid_result = strategy_manager.create_hybrid_index("demo_hybrid", nodes)
    
    print("索引创建完成:")
    print(f"可用索引: {index_manager.list_indexes()}")
    print(f"推荐策略: {hybrid_result['strategy']}")
    
    return index_manager

if __name__ == "__main__":
    demo_indexing_system()
```

#### 3. 查询引擎系统

```python
# llamaindex_examples/core/query_engines.py
from llama_index.core.query_engine import (
    RetrieverQueryEngine,
    SubQuestionQueryEngine,
    RouterQueryEngine,
    MultiStepQueryEngine
)
from llama_index.core.retrievers import VectorIndexRetriever
from llama_index.core.postprocessor import SimilarityPostprocessor, KeywordNodePostprocessor
from llama_index.core.response_synthesizers import ResponseMode
from llama_index.core.tools import QueryEngineTool, ToolMetadata
from typing import List, Dict, Any, Optional
import asyncio

class QueryEngineManager:
    """查询引擎管理器"""
    
    def __init__(self, index_manager):
        self.index_manager = index_manager
        self.query_engines: Dict[str, Any] = {}
        self.retrievers: Dict[str, Any] = {}
    
    def create_basic_query_engine(self, name: str, index_name: str,
                                 similarity_top_k: int = 3,
                                 response_mode: str = "compact") -> RetrieverQueryEngine:
        """创建基础查询引擎"""
        index = self.index_manager.get_index(index_name)
        if not index:
            raise ValueError(f"索引 '{index_name}' 不存在")
        
        # 创建检索器
        retriever = VectorIndexRetriever(
            index=index,
            similarity_top_k=similarity_top_k
        )
        
        # 创建后处理器
        postprocessors = [
            SimilarityPostprocessor(similarity_cutoff=0.7),
            KeywordNodePostprocessor(required_keywords=[], exclude_keywords=[])
        ]
        
        # 创建查询引擎
        query_engine = RetrieverQueryEngine(
            retriever=retriever,
            response_synthesizer=index.as_response_synthesizer(
                response_mode=ResponseMode(response_mode)
            ),
            node_postprocessors=postprocessors
        )
        
        self.query_engines[name] = query_engine
        self.retrievers[name] = retriever
        
        return query_engine
    
    def create_sub_question_engine(self, name: str, 
                                  query_engine_tools: List[QueryEngineTool]) -> SubQuestionQueryEngine:
        """创建子问题查询引擎"""
        sub_question_engine = SubQuestionQueryEngine.from_defaults(
            query_engine_tools=query_engine_tools,
            verbose=True
        )
        
        self.query_engines[name] = sub_question_engine
        return sub_question_engine
    
    def create_router_engine(self, name: str, 
                           query_engine_choices: Dict[str, Any]) -> RouterQueryEngine:
        """创建路由查询引擎"""
        router_engine = RouterQueryEngine.from_defaults(
            query_engine_choices=query_engine_choices,
            verbose=True
        )
        
        self.query_engines[name] = router_engine
        return router_engine
    
    def create_multi_step_engine(self, name: str, index_name: str,
                               num_steps: int = 3) -> MultiStepQueryEngine:
        """创建多步查询引擎"""
        index = self.index_manager.get_index(index_name)
        if not index:
            raise ValueError(f"索引 '{index_name}' 不存在")
        
        multi_step_engine = MultiStepQueryEngine(
            query_engine=index.as_query_engine(),
            num_steps=num_steps,
            verbose=True
        )
        
        self.query_engines[name] = multi_step_engine
        return multi_step_engine
    
    async def query_async(self, engine_name: str, query: str) -> str:
        """异步查询"""
        if engine_name not in self.query_engines:
            raise ValueError(f"查询引擎 '{engine_name}' 不存在")
        
        engine = self.query_engines[engine_name]
        
        # 检查是否支持异步
        if hasattr(engine, 'aquery'):
            response = await engine.aquery(query)
        else:
            # 使用线程池执行同步查询
            response = await asyncio.to_thread(engine.query, query)
        
        return str(response)
    
    def query(self, engine_name: str, query: str) -> str:
        """同步查询"""
        if engine_name not in self.query_engines:
            raise ValueError(f"查询引擎 '{engine_name}' 不存在")
        
        engine = self.query_engines[engine_name]
        response = engine.query(query)
        
        return str(response)

# 高级查询策略
class AdvancedQueryStrategy:
    """高级查询策略"""
    
    def __init__(self, query_engine_manager: QueryEngineManager):
        self.query_engine_manager = query_engine_manager
    
    def analyze_query_complexity(self, query: str) -> Dict[str, Any]:
        """分析查询复杂度"""
        # 简单的查询复杂度分析
        word_count = len(query.split())
        has_multiple_questions = '?' in query and query.count('?') > 1
        has_comparison = any(word in query.lower() for word in ['比较', 'compare', 'vs', '对比'])
        has_aggregation = any(word in query.lower() for word in ['总结', 'summary', '汇总', 'aggregate'])
        
        complexity_score = 0
        if word_count > 20:
            complexity_score += 2
        if has_multiple_questions:
            complexity_score += 3
        if has_comparison:
            complexity_score += 2
        if has_aggregation:
            complexity_score += 2
        
        return {
            'word_count': word_count,
            'has_multiple_questions': has_multiple_questions,
            'has_comparison': has_comparison,
            'has_aggregation': has_aggregation,
            'complexity_score': complexity_score,
            'complexity_level': 'high' if complexity_score > 5 else 'medium' if complexity_score > 2 else 'low'
        }
    
    def recommend_query_strategy(self, query: str) -> str:
        """推荐查询策略"""
        analysis = self.analyze_query_complexity(query)
        
        if analysis['complexity_level'] == 'high':
            if analysis['has_multiple_questions']:
                return 'sub_question'
            elif analysis['has_comparison']:
                return 'multi_step'
            else:
                return 'router'
        elif analysis['complexity_level'] == 'medium':
            return 'basic_with_postprocessing'
        else:
            return 'basic'
    
    async def smart_query(self, query: str, available_engines: List[str]) -> Dict[str, Any]:
        """智能查询 - 自动选择最佳策略"""
        strategy = self.recommend_query_strategy(query)
        
        results = {}
        
        if strategy == 'sub_question' and 'sub_question' in available_engines:
            result = await self.query_engine_manager.query_async('sub_question', query)
            results['primary'] = result
            results['strategy'] = 'sub_question'
        
        elif strategy == 'multi_step' and 'multi_step' in available_engines:
            result = await self.query_engine_manager.query_async('multi_step', query)
            results['primary'] = result
            results['strategy'] = 'multi_step'
        
        elif strategy == 'router' and 'router' in available_engines:
            result = await self.query_engine_manager.query_async('router', query)
            results['primary'] = result
            results['strategy'] = 'router'
        
        else:
            # 使用基础查询引擎
            basic_engine = available_engines[0] if available_engines else None
            if basic_engine:
                result = await self.query_engine_manager.query_async(basic_engine, query)
                results['primary'] = result
                results['strategy'] = 'basic'
        
        return results

# RAG系统实现
class RAGSystem:
    """检索增强生成系统"""
    
    def __init__(self, index_manager, query_engine_manager):
        self.index_manager = index_manager
        self.query_engine_manager = query_engine_manager
        self.setup_rag_pipeline()
    
    def setup_rag_pipeline(self):
        """设置RAG流水线"""
        # 创建多个专门的查询引擎
        self.engines = {}
        
        # 如果有索引，创建对应的查询引擎
        for index_name in self.index_manager.list_indexes():
            engine_name = f"{index_name}_engine"
            try:
                self.engines[engine_name] = self.query_engine_manager.create_basic_query_engine(
                    engine_name, index_name
                )
            except Exception as e:
                print(f"创建查询引擎失败 {engine_name}: {e}")
    
    def retrieve_and_generate(self, query: str, 
                            context_window: int = 3,
                            temperature: float = 0.1) -> Dict[str, Any]:
        """检索并生成回答"""
        results = {}
        
        # 从所有可用引擎检索
        for engine_name, engine in self.engines.items():
            try:
                response = engine.query(query)
                results[engine_name] = {
                    'response': str(response),
                    'source_nodes': getattr(response, 'source_nodes', [])
                }
            except Exception as e:
                results[engine_name] = {'error': str(e)}
        
        # 合并和排序结果
        best_result = self.select_best_result(results)
        
        return {
            'query': query,
            'best_result': best_result,
            'all_results': results,
            'metadata': {
                'engines_used': list(self.engines.keys()),
                'context_window': context_window,
                'temperature': temperature
            }
        }
    
    def select_best_result(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """选择最佳结果"""
        # 简单的结果选择逻辑
        valid_results = {k: v for k, v in results.items() if 'error' not in v}
        
        if not valid_results:
            return {'response': '抱歉，无法找到相关信息。', 'confidence': 0.0}
        
        # 选择第一个有效结果（实际应用中可以使用更复杂的评分机制）
        best_engine = list(valid_results.keys())[0]
        best_result = valid_results[best_engine]
        
        return {
            'response': best_result['response'],
            'source_engine': best_engine,
            'source_nodes': best_result.get('source_nodes', []),
            'confidence': 0.8  # 简化的置信度
        }

# 使用示例
async def demo_query_system():
    """演示查询系统的使用"""
    # 假设已经有了索引管理器
    from llamaindex_examples.core.indexing_system import demo_indexing_system
    
    index_manager = demo_indexing_system()
    
    # 创建查询引擎管理器
    query_manager = QueryEngineManager(index_manager)
    
    # 创建基础查询引擎
    basic_engine = query_manager.create_basic_query_engine(
        "basic_qa", "demo_vector"
    )
    
    # 创建RAG系统
    rag_system = RAGSystem(index_manager, query_manager)
    
    # 测试查询
    test_queries = [
        "什么是人工智能？",
        "机器学习和深度学习有什么区别？",
        "请比较人工智能、机器学习和深度学习的关系"
    ]
    
    for query in test_queries:
        print(f"\n查询: {query}")
        
        # 使用RAG系统
        result = rag_system.retrieve_and_generate(query)
        print(f"回答: {result['best_result']['response'][:200]}...")
        
        # 使用高级查询策略
        strategy_manager = AdvancedQueryStrategy(query_manager)
        analysis = strategy_manager.analyze_query_complexity(query)
        print(f"查询复杂度: {analysis['complexity_level']}")

if __name__ == "__main__":
    asyncio.run(demo_query_system())
```

## 实践练习

### 练习1：构建多模态文档处理系统

**任务描述**：创建一个支持文本、图像、表格等多模态数据的文档处理系统。

**实现要求**：
```python
class MultiModalDocumentProcessor:
    def __init__(self):
        # 初始化多模态处理器
        pass
    
    def process_mixed_content(self, file_path: str) -> List:
        # 处理包含多种模态的文档
        pass
```

### 练习2：开发智能查询路由系统

**任务描述**：基于查询内容自动选择最适合的索引和查询策略。

## 评估标准

### 技术实现（50%）
- [ ] 正确使用LlamaIndex的核心组件
- [ ] 实现高效的数据处理流水线
- [ ] 索引选择和优化合理
- [ ] 查询引擎配置恰当

### 功能完整性（30%）
- [ ] 支持多种数据源和格式
- [ ] 检索效果良好
- [ ] 响应质量高
- [ ] 系统稳定可靠

### 创新性（20%）
- [ ] 有独特的优化策略
- [ ] 解决实际问题
- [ ] 技术应用巧妙
- [ ] 具备扩展性

## 常见问题解答

### Q1: 如何选择合适的索引类型？

**A1**: 根据数据特征选择：
- **大量短文档**：向量索引
- **长文档**：树索引
- **结构化数据**：关键词索引
- **知识图谱**：图索引

### Q2: 如何优化检索性能？

**A2**:
1. **合理分块**：根据内容特点调整分块大小
2. **元数据利用**：充分利用元数据进行过滤
3. **混合检索**：结合多种检索策略
4. **缓存机制**：缓存常用查询结果

### Q3: 如何处理大规模数据？

**A3**:
1. **分批处理**：使用异步和批量处理
2. **增量更新**：支持增量索引更新
3. **分布式存储**：使用分布式向量数据库
4. **内存优化**：合理管理内存使用

## 下一步学习

完成本节后，建议：
1. 深入学习向量数据库的使用
2. 探索多模态数据处理
3. 学习分布式索引架构
4. 准备学习框架对比和选择策略
