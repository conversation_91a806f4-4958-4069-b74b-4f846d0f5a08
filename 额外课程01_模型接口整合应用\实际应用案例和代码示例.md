# 实际应用案例和代码示例

## 概述

本章将通过具体的应用案例，展示如何在实际项目中应用模型接口整合技术。我们将构建几个完整的应用示例，从简单的聊天机器人到复杂的企业级AI服务平台，涵盖不同的应用场景和技术要求。

## 案例1：智能客服系统

### 系统架构

```
智能客服系统架构
┌─────────────────────────────────────────────────────────┐
│                    前端界面                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │   Web UI    │ │  Mobile App │ │   API       │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
├─────────────────────────────────────────────────────────┤
│                    业务逻辑层                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │ 意图识别     │ │ 对话管理     │ │ 知识检索     │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
├─────────────────────────────────────────────────────────┤
│                    模型服务层                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │ 统一接口     │ │ 负载均衡     │ │ 故障转移     │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
├─────────────────────────────────────────────────────────┤
│                    数据存储层                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │ 对话历史     │ │ 知识库       │ │ 用户画像     │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
└─────────────────────────────────────────────────────────┘
```

### 核心实现

```python
# intelligent_customer_service.py
import asyncio
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
import sqlite3
import aiofiles
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class IntentType(Enum):
    """意图类型"""
    GREETING = "greeting"
    QUESTION = "question"
    COMPLAINT = "complaint"
    REQUEST = "request"
    GOODBYE = "goodbye"
    UNKNOWN = "unknown"

class ConversationStatus(Enum):
    """对话状态"""
    ACTIVE = "active"
    WAITING = "waiting"
    RESOLVED = "resolved"
    ESCALATED = "escalated"

@dataclass
class UserProfile:
    """用户画像"""
    user_id: str
    name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    vip_level: int = 0
    preferred_language: str = "zh"
    interaction_count: int = 0
    satisfaction_score: float = 0.0
    last_interaction: Optional[str] = None

@dataclass
class ConversationContext:
    """对话上下文"""
    conversation_id: str
    user_id: str
    status: ConversationStatus
    intent_history: List[IntentType]
    messages: List[Dict[str, Any]]
    metadata: Dict[str, Any]
    created_at: str
    updated_at: str

class IntentClassifier:
    """意图分类器"""
    
    def __init__(self, model_interface):
        self.model_interface = model_interface
        self.intent_examples = {
            IntentType.GREETING: [
                "你好", "hello", "hi", "早上好", "下午好", "晚上好"
            ],
            IntentType.QUESTION: [
                "怎么", "如何", "什么", "为什么", "哪里", "什么时候"
            ],
            IntentType.COMPLAINT: [
                "投诉", "问题", "故障", "不满意", "错误", "bug"
            ],
            IntentType.REQUEST: [
                "申请", "办理", "开通", "关闭", "修改", "更新"
            ],
            IntentType.GOODBYE: [
                "再见", "拜拜", "goodbye", "bye", "结束", "谢谢"
            ]
        }
    
    async def classify_intent(self, message: str, context: ConversationContext) -> IntentType:
        """分类用户意图"""
        try:
            # 构建分类提示
            examples_text = self._build_examples_text()
            
            prompt = f"""
            请分析用户消息的意图类型。

            意图类型和示例：
            {examples_text}

            对话历史：
            {self._format_conversation_history(context.messages[-3:])}

            当前用户消息："{message}"

            请返回最匹配的意图类型（greeting/question/complaint/request/goodbye/unknown）：
            """
            
            from接口封装和统一管理策略 import ChatRequest, ChatMessage
            
            request = ChatRequest(
                messages=[ChatMessage(role="user", content=prompt)],
                temperature=0.1,
                max_tokens=50
            )
            
            response = await self.model_interface.chat_completion(request)
            
            # 解析响应
            intent_text = response.content.strip().lower()
            
            for intent in IntentType:
                if intent.value in intent_text:
                    return intent
            
            return IntentType.UNKNOWN
            
        except Exception as e:
            logger.error(f"意图分类失败: {e}")
            return IntentType.UNKNOWN
    
    def _build_examples_text(self) -> str:
        """构建示例文本"""
        examples = []
        for intent, keywords in self.intent_examples.items():
            examples.append(f"{intent.value}: {', '.join(keywords[:3])}")
        return "\n".join(examples)
    
    def _format_conversation_history(self, messages: List[Dict[str, Any]]) -> str:
        """格式化对话历史"""
        if not messages:
            return "无对话历史"
        
        formatted = []
        for msg in messages:
            role = msg.get("role", "unknown")
            content = msg.get("content", "")
            formatted.append(f"{role}: {content}")
        
        return "\n".join(formatted)

class KnowledgeBase:
    """知识库"""
    
    def __init__(self, model_interface):
        self.model_interface = model_interface
        self.knowledge_items = []
        self.embeddings_cache = {}
    
    async def initialize(self):
        """初始化知识库"""
        # 加载知识库数据
        await self._load_knowledge_items()
        
        # 生成嵌入向量
        await self._generate_embeddings()
    
    async def _load_knowledge_items(self):
        """加载知识库条目"""
        # 示例知识库数据
        self.knowledge_items = [
            {
                "id": "kb_001",
                "title": "账户登录问题",
                "content": "如果无法登录账户，请检查用户名和密码是否正确，或尝试重置密码。",
                "category": "账户管理",
                "keywords": ["登录", "密码", "账户"]
            },
            {
                "id": "kb_002", 
                "title": "订单查询方法",
                "content": "您可以在个人中心的订单管理页面查看所有订单信息，包括订单状态和物流信息。",
                "category": "订单管理",
                "keywords": ["订单", "查询", "状态"]
            },
            {
                "id": "kb_003",
                "title": "退款申请流程",
                "content": "申请退款请在订单详情页点击退款按钮，填写退款原因，客服会在24小时内处理。",
                "category": "售后服务",
                "keywords": ["退款", "申请", "流程"]
            }
        ]
    
    async def _generate_embeddings(self):
        """生成知识库嵌入向量"""
        from接口封装和统一管理策略 import EmbeddingRequest
        
        for item in self.knowledge_items:
            text = f"{item['title']} {item['content']}"
            
            request = EmbeddingRequest(text=text)
            response = await self.model_interface.create_embedding(request)
            
            if response.embeddings:
                self.embeddings_cache[item['id']] = response.embeddings[0]
    
    async def search_knowledge(self, query: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """搜索知识库"""
        try:
            # 生成查询嵌入
            from接口封装和统一管理策略 import EmbeddingRequest
            
            request = EmbeddingRequest(text=query)
            response = await self.model_interface.create_embedding(request)
            
            if not response.embeddings:
                return []
            
            query_embedding = response.embeddings[0]
            
            # 计算相似度
            similarities = []
            for item in self.knowledge_items:
                item_embedding = self.embeddings_cache.get(item['id'])
                if item_embedding:
                    similarity = self._cosine_similarity(query_embedding, item_embedding)
                    similarities.append((item, similarity))
            
            # 排序并返回top-k
            similarities.sort(key=lambda x: x[1], reverse=True)
            return [item for item, _ in similarities[:top_k]]
            
        except Exception as e:
            logger.error(f"知识库搜索失败: {e}")
            return []
    
    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """计算余弦相似度"""
        import math
        
        dot_product = sum(a * b for a, b in zip(vec1, vec2))
        magnitude1 = math.sqrt(sum(a * a for a in vec1))
        magnitude2 = math.sqrt(sum(a * a for a in vec2))
        
        if magnitude1 == 0 or magnitude2 == 0:
            return 0
        
        return dot_product / (magnitude1 * magnitude2)

class ConversationManager:
    """对话管理器"""
    
    def __init__(self, model_interface, knowledge_base: KnowledgeBase):
        self.model_interface = model_interface
        self.knowledge_base = knowledge_base
        self.intent_classifier = IntentClassifier(model_interface)
        self.conversations: Dict[str, ConversationContext] = {}
        self.user_profiles: Dict[str, UserProfile] = {}
    
    async def start_conversation(self, user_id: str) -> str:
        """开始新对话"""
        conversation_id = str(uuid.uuid4())
        
        # 获取或创建用户画像
        user_profile = self.user_profiles.get(user_id)
        if not user_profile:
            user_profile = UserProfile(user_id=user_id)
            self.user_profiles[user_id] = user_profile
        
        # 创建对话上下文
        context = ConversationContext(
            conversation_id=conversation_id,
            user_id=user_id,
            status=ConversationStatus.ACTIVE,
            intent_history=[],
            messages=[],
            metadata={"user_profile": asdict(user_profile)},
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )
        
        self.conversations[conversation_id] = context
        
        # 更新用户画像
        user_profile.interaction_count += 1
        user_profile.last_interaction = datetime.now().isoformat()
        
        return conversation_id
    
    async def process_message(self, conversation_id: str, message: str) -> Dict[str, Any]:
        """处理用户消息"""
        context = self.conversations.get(conversation_id)
        if not context:
            return {"error": "对话不存在"}
        
        try:
            # 添加用户消息到历史
            context.messages.append({
                "role": "user",
                "content": message,
                "timestamp": datetime.now().isoformat()
            })
            
            # 分类意图
            intent = await self.intent_classifier.classify_intent(message, context)
            context.intent_history.append(intent)
            
            # 根据意图生成响应
            response = await self._generate_response(context, message, intent)
            
            # 添加助手响应到历史
            context.messages.append({
                "role": "assistant",
                "content": response["content"],
                "timestamp": datetime.now().isoformat(),
                "intent": intent.value,
                "metadata": response.get("metadata", {})
            })
            
            # 更新对话状态
            context.updated_at = datetime.now().isoformat()
            
            return {
                "conversation_id": conversation_id,
                "response": response["content"],
                "intent": intent.value,
                "status": context.status.value,
                "metadata": response.get("metadata", {})
            }
            
        except Exception as e:
            logger.error(f"消息处理失败: {e}")
            return {"error": f"处理失败: {str(e)}"}
    
    async def _generate_response(self, context: ConversationContext, 
                               message: str, intent: IntentType) -> Dict[str, Any]:
        """生成响应"""
        if intent == IntentType.GREETING:
            return await self._handle_greeting(context)
        elif intent == IntentType.QUESTION:
            return await self._handle_question(context, message)
        elif intent == IntentType.COMPLAINT:
            return await self._handle_complaint(context, message)
        elif intent == IntentType.REQUEST:
            return await self._handle_request(context, message)
        elif intent == IntentType.GOODBYE:
            return await self._handle_goodbye(context)
        else:
            return await self._handle_unknown(context, message)
    
    async def _handle_greeting(self, context: ConversationContext) -> Dict[str, Any]:
        """处理问候"""
        user_profile = self.user_profiles.get(context.user_id)
        
        if user_profile and user_profile.name:
            content = f"您好，{user_profile.name}！很高兴为您服务。请问有什么可以帮助您的吗？"
        else:
            content = "您好！欢迎使用智能客服系统。请问有什么可以帮助您的吗？"
        
        return {
            "content": content,
            "metadata": {"response_type": "greeting"}
        }
    
    async def _handle_question(self, context: ConversationContext, message: str) -> Dict[str, Any]:
        """处理问题"""
        # 搜索知识库
        knowledge_items = await self.knowledge_base.search_knowledge(message)
        
        if knowledge_items:
            # 基于知识库生成回答
            knowledge_text = "\n".join([
                f"- {item['title']}: {item['content']}"
                for item in knowledge_items
            ])
            
            prompt = f"""
            基于以下知识库信息回答用户问题：

            知识库信息：
            {knowledge_text}

            用户问题：{message}

            请提供准确、有帮助的回答：
            """
            
            from接口封装和统一管理策略 import ChatRequest, ChatMessage
            
            request = ChatRequest(
                messages=[ChatMessage(role="user", content=prompt)],
                temperature=0.3,
                max_tokens=500
            )
            
            response = await self.model_interface.chat_completion(request)
            
            return {
                "content": response.content,
                "metadata": {
                    "response_type": "knowledge_based",
                    "knowledge_sources": [item['id'] for item in knowledge_items]
                }
            }
        else:
            # 通用回答
            return {
                "content": "抱歉，我暂时无法回答您的问题。请您稍等，我为您转接人工客服。",
                "metadata": {"response_type": "escalation_needed"}
            }
    
    async def _handle_complaint(self, context: ConversationContext, message: str) -> Dict[str, Any]:
        """处理投诉"""
        # 标记为需要升级
        context.status = ConversationStatus.ESCALATED
        
        return {
            "content": "非常抱歉给您带来不便。我已经记录了您的投诉，并将为您安排专门的客服人员跟进处理。请您留下联系方式，我们会在24小时内与您联系。",
            "metadata": {
                "response_type": "complaint_escalation",
                "escalation_reason": "customer_complaint"
            }
        }
    
    async def _handle_request(self, context: ConversationContext, message: str) -> Dict[str, Any]:
        """处理请求"""
        return {
            "content": "我了解您的需求。为了更好地为您办理相关业务，请您提供一些必要的信息，或者我可以为您转接到相应的业务部门。",
            "metadata": {"response_type": "request_handling"}
        }
    
    async def _handle_goodbye(self, context: ConversationContext) -> Dict[str, Any]:
        """处理告别"""
        context.status = ConversationStatus.RESOLVED
        
        return {
            "content": "感谢您使用我们的服务！如果还有其他问题，随时欢迎您再次咨询。祝您生活愉快！",
            "metadata": {"response_type": "goodbye"}
        }
    
    async def _handle_unknown(self, context: ConversationContext, message: str) -> Dict[str, Any]:
        """处理未知意图"""
        return {
            "content": "抱歉，我没有完全理解您的意思。能否请您换个方式描述一下您的问题？或者我可以为您转接人工客服。",
            "metadata": {"response_type": "clarification_needed"}
        }
    
    def get_conversation_summary(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """获取对话摘要"""
        context = self.conversations.get(conversation_id)
        if not context:
            return None
        
        return {
            "conversation_id": conversation_id,
            "user_id": context.user_id,
            "status": context.status.value,
            "message_count": len(context.messages),
            "intent_distribution": self._calculate_intent_distribution(context.intent_history),
            "duration": self._calculate_duration(context.created_at, context.updated_at),
            "created_at": context.created_at,
            "updated_at": context.updated_at
        }
    
    def _calculate_intent_distribution(self, intent_history: List[IntentType]) -> Dict[str, int]:
        """计算意图分布"""
        distribution = {}
        for intent in intent_history:
            distribution[intent.value] = distribution.get(intent.value, 0) + 1
        return distribution
    
    def _calculate_duration(self, start_time: str, end_time: str) -> float:
        """计算对话持续时间（分钟）"""
        try:
            start = datetime.fromisoformat(start_time)
            end = datetime.fromisoformat(end_time)
            duration = (end - start).total_seconds() / 60
            return round(duration, 2)
        except:
            return 0.0

class CustomerServiceSystem:
    """智能客服系统主类"""
    
    def __init__(self, model_interface):
        self.model_interface = model_interface
        self.knowledge_base = KnowledgeBase(model_interface)
        self.conversation_manager = ConversationManager(model_interface, self.knowledge_base)
        self.analytics = CustomerServiceAnalytics()
    
    async def initialize(self):
        """初始化系统"""
        await self.knowledge_base.initialize()
        logger.info("智能客服系统初始化完成")
    
    async def start_chat(self, user_id: str) -> str:
        """开始聊天"""
        conversation_id = await self.conversation_manager.start_conversation(user_id)
        self.analytics.record_conversation_start(conversation_id, user_id)
        return conversation_id
    
    async def send_message(self, conversation_id: str, message: str) -> Dict[str, Any]:
        """发送消息"""
        response = await self.conversation_manager.process_message(conversation_id, message)
        self.analytics.record_message(conversation_id, message, response)
        return response
    
    def get_conversation_summary(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """获取对话摘要"""
        return self.conversation_manager.get_conversation_summary(conversation_id)
    
    def get_system_analytics(self) -> Dict[str, Any]:
        """获取系统分析数据"""
        return self.analytics.get_analytics()

class CustomerServiceAnalytics:
    """客服系统分析"""
    
    def __init__(self):
        self.conversation_stats = {}
        self.message_stats = {}
        self.intent_stats = {}
    
    def record_conversation_start(self, conversation_id: str, user_id: str):
        """记录对话开始"""
        self.conversation_stats[conversation_id] = {
            "user_id": user_id,
            "start_time": datetime.now().isoformat(),
            "message_count": 0,
            "intents": []
        }
    
    def record_message(self, conversation_id: str, message: str, response: Dict[str, Any]):
        """记录消息"""
        if conversation_id in self.conversation_stats:
            self.conversation_stats[conversation_id]["message_count"] += 1
            
            intent = response.get("intent")
            if intent:
                self.conversation_stats[conversation_id]["intents"].append(intent)
                self.intent_stats[intent] = self.intent_stats.get(intent, 0) + 1
    
    def get_analytics(self) -> Dict[str, Any]:
        """获取分析数据"""
        total_conversations = len(self.conversation_stats)
        total_messages = sum(stats["message_count"] for stats in self.conversation_stats.values())
        
        return {
            "total_conversations": total_conversations,
            "total_messages": total_messages,
            "avg_messages_per_conversation": total_messages / total_conversations if total_conversations > 0 else 0,
            "intent_distribution": self.intent_stats,
            "active_conversations": len([
                conv for conv in self.conversation_stats.values()
                if datetime.now() - datetime.fromisoformat(conv["start_time"]) < timedelta(hours=1)
            ])
        }

# 使用示例
async def demo_customer_service():
    """智能客服系统演示"""
    # 这里需要实际的模型接口
    from接口封装和统一管理策略 import UnifiedModelInterface
    
    # 创建模拟接口
    class MockModelInterface:
        async def chat_completion(self, request):
            from接口封装和统一管理策略 import ChatResponse
            return ChatResponse(
                content="这是模拟的AI回答",
                model="mock-model",
                usage={"total_tokens": 100}
            )
        
        async def create_embedding(self, request):
            from接口封装和统一管理策略 import EmbeddingResponse
            import random
            return EmbeddingResponse(
                embeddings=[[random.random() for _ in range(1536)]],
                model="mock-embedding",
                usage={"total_tokens": 50}
            )
    
    # 初始化系统
    model_interface = MockModelInterface()
    customer_service = CustomerServiceSystem(model_interface)
    await customer_service.initialize()
    
    # 模拟对话
    user_id = "user_123"
    conversation_id = await customer_service.start_chat(user_id)
    
    print(f"对话开始，ID: {conversation_id}")
    
    # 模拟用户消息
    messages = [
        "你好",
        "我想查询订单状态",
        "订单号是12345",
        "谢谢，再见"
    ]
    
    for message in messages:
        print(f"\n用户: {message}")
        response = await customer_service.send_message(conversation_id, message)
        print(f"客服: {response.get('response', '无响应')}")
        print(f"意图: {response.get('intent', '未知')}")
    
    # 获取对话摘要
    summary = customer_service.get_conversation_summary(conversation_id)
    print(f"\n对话摘要: {summary}")
    
    # 获取系统分析
    analytics = customer_service.get_system_analytics()
    print(f"系统分析: {analytics}")

if __name__ == "__main__":
    asyncio.run(demo_customer_service())
```

## 案例2：多模型内容生成平台

### 平台架构

```python
# content_generation_platform.py
import asyncio
import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum
import aiofiles
import logging

logger = logging.getLogger(__name__)

class ContentType(Enum):
    """内容类型"""
    ARTICLE = "article"
    SUMMARY = "summary"
    TRANSLATION = "translation"
    CODE = "code"
    CREATIVE = "creative"
    TECHNICAL = "technical"

class ContentStyle(Enum):
    """内容风格"""
    FORMAL = "formal"
    CASUAL = "casual"
    PROFESSIONAL = "professional"
    CREATIVE = "creative"
    TECHNICAL = "technical"

@dataclass
class ContentRequest:
    """内容生成请求"""
    request_id: str
    content_type: ContentType
    style: ContentStyle
    prompt: str
    target_length: Optional[int] = None
    language: str = "zh"
    model_preference: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    created_at: str = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now().isoformat()

@dataclass
class ContentResponse:
    """内容生成响应"""
    request_id: str
    content: str
    model_used: str
    generation_time: float
    quality_score: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None
    created_at: str = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now().isoformat()

class ContentGenerator:
    """内容生成器"""
    
    def __init__(self, model_interface):
        self.model_interface = model_interface
        self.style_templates = self._load_style_templates()
        self.quality_evaluator = ContentQualityEvaluator(model_interface)
    
    def _load_style_templates(self) -> Dict[ContentStyle, Dict[str, str]]:
        """加载风格模板"""
        return {
            ContentStyle.FORMAL: {
                "prefix": "请以正式、专业的语调",
                "suffix": "确保内容准确、逻辑清晰。"
            },
            ContentStyle.CASUAL: {
                "prefix": "请以轻松、友好的语调",
                "suffix": "让内容易于理解和接受。"
            },
            ContentStyle.PROFESSIONAL: {
                "prefix": "请以专业、权威的语调",
                "suffix": "确保内容具有专业性和可信度。"
            },
            ContentStyle.CREATIVE: {
                "prefix": "请以创意、生动的语调",
                "suffix": "让内容富有想象力和吸引力。"
            },
            ContentStyle.TECHNICAL: {
                "prefix": "请以技术、精确的语调",
                "suffix": "确保技术细节准确无误。"
            }
        }
    
    async def generate_content(self, request: ContentRequest) -> ContentResponse:
        """生成内容"""
        start_time = datetime.now()
        
        try:
            # 构建生成提示
            prompt = self._build_generation_prompt(request)
            
            # 选择模型
            model = self._select_model(request)
            
            # 生成内容
            from接口封装和统一管理策略 import ChatRequest, ChatMessage
            
            chat_request = ChatRequest(
                messages=[ChatMessage(role="user", content=prompt)],
                model=model,
                temperature=self._get_temperature(request.content_type),
                max_tokens=self._get_max_tokens(request.target_length)
            )
            
            response = await self.model_interface.chat_completion(chat_request)
            
            # 计算生成时间
            generation_time = (datetime.now() - start_time).total_seconds()
            
            # 评估质量
            quality_score = await self.quality_evaluator.evaluate_content(
                response.content, request
            )
            
            return ContentResponse(
                request_id=request.request_id,
                content=response.content,
                model_used=response.model,
                generation_time=generation_time,
                quality_score=quality_score,
                metadata={
                    "token_usage": response.usage,
                    "content_type": request.content_type.value,
                    "style": request.style.value
                }
            )
            
        except Exception as e:
            logger.error(f"内容生成失败: {e}")
            raise Exception(f"内容生成失败: {str(e)}")
    
    def _build_generation_prompt(self, request: ContentRequest) -> str:
        """构建生成提示"""
        style_template = self.style_templates.get(request.style, {})
        prefix = style_template.get("prefix", "请")
        suffix = style_template.get("suffix", "")
        
        # 基础提示
        prompt_parts = [prefix]
        
        # 内容类型特定指令
        if request.content_type == ContentType.ARTICLE:
            prompt_parts.append("撰写一篇文章")
        elif request.content_type == ContentType.SUMMARY:
            prompt_parts.append("生成内容摘要")
        elif request.content_type == ContentType.TRANSLATION:
            prompt_parts.append(f"翻译为{request.language}")
        elif request.content_type == ContentType.CODE:
            prompt_parts.append("编写代码")
        elif request.content_type == ContentType.CREATIVE:
            prompt_parts.append("创作内容")
        elif request.content_type == ContentType.TECHNICAL:
            prompt_parts.append("撰写技术文档")
        
        # 长度要求
        if request.target_length:
            prompt_parts.append(f"，目标长度约{request.target_length}字")
        
        # 主要内容
        prompt_parts.append(f"：\n\n{request.prompt}")
        
        # 后缀
        if suffix:
            prompt_parts.append(f"\n\n{suffix}")
        
        return "".join(prompt_parts)
    
    def _select_model(self, request: ContentRequest) -> Optional[str]:
        """选择合适的模型"""
        if request.model_preference:
            return request.model_preference
        
        # 根据内容类型选择模型
        model_mapping = {
            ContentType.ARTICLE: "gpt-4",
            ContentType.SUMMARY: "gpt-3.5-turbo",
            ContentType.TRANSLATION: "gpt-4",
            ContentType.CODE: "gpt-4",
            ContentType.CREATIVE: "claude-3-opus-20240229",
            ContentType.TECHNICAL: "gpt-4"
        }
        
        return model_mapping.get(request.content_type, "gpt-3.5-turbo")
    
    def _get_temperature(self, content_type: ContentType) -> float:
        """获取温度参数"""
        temperature_mapping = {
            ContentType.ARTICLE: 0.7,
            ContentType.SUMMARY: 0.3,
            ContentType.TRANSLATION: 0.1,
            ContentType.CODE: 0.1,
            ContentType.CREATIVE: 0.9,
            ContentType.TECHNICAL: 0.3
        }
        
        return temperature_mapping.get(content_type, 0.7)
    
    def _get_max_tokens(self, target_length: Optional[int]) -> int:
        """获取最大token数"""
        if target_length:
            # 中文约1.5字符/token，英文约4字符/token
            return min(int(target_length / 1.5 * 1.5), 4000)  # 添加缓冲
        
        return 2000

class ContentQualityEvaluator:
    """内容质量评估器"""
    
    def __init__(self, model_interface):
        self.model_interface = model_interface
    
    async def evaluate_content(self, content: str, request: ContentRequest) -> float:
        """评估内容质量"""
        try:
            evaluation_prompt = f"""
            请评估以下内容的质量，从以下维度打分（1-10分）：
            1. 相关性：内容是否符合要求
            2. 准确性：信息是否准确可靠
            3. 流畅性：语言是否流畅自然
            4. 完整性：内容是否完整充实
            5. 风格一致性：是否符合要求的风格

            内容类型：{request.content_type.value}
            要求风格：{request.style.value}
            原始要求：{request.prompt}

            生成内容：
            {content}

            请返回总体质量分数（1-10，保留一位小数）：
            """
            
            from接口封装和统一管理策略 import ChatRequest, ChatMessage
            
            chat_request = ChatRequest(
                messages=[ChatMessage(role="user", content=evaluation_prompt)],
                temperature=0.1,
                max_tokens=100
            )
            
            response = await self.model_interface.chat_completion(chat_request)
            
            # 解析分数
            score_text = response.content.strip()
            try:
                score = float(score_text)
                return max(1.0, min(10.0, score))  # 确保在1-10范围内
            except ValueError:
                return 5.0  # 默认分数
                
        except Exception as e:
            logger.error(f"质量评估失败: {e}")
            return 5.0

class ContentGenerationPlatform:
    """内容生成平台"""
    
    def __init__(self, model_interface):
        self.model_interface = model_interface
        self.content_generator = ContentGenerator(model_interface)
        self.request_history: Dict[str, ContentRequest] = {}
        self.response_history: Dict[str, ContentResponse] = {}
        self.analytics = ContentAnalytics()
    
    async def create_content(self, 
                           content_type: ContentType,
                           style: ContentStyle,
                           prompt: str,
                           **kwargs) -> ContentResponse:
        """创建内容"""
        # 生成请求ID
        request_id = str(uuid.uuid4())
        
        # 创建请求对象
        request = ContentRequest(
            request_id=request_id,
            content_type=content_type,
            style=style,
            prompt=prompt,
            **kwargs
        )
        
        # 保存请求历史
        self.request_history[request_id] = request
        
        # 生成内容
        response = await self.content_generator.generate_content(request)
        
        # 保存响应历史
        self.response_history[request_id] = response
        
        # 记录分析数据
        self.analytics.record_generation(request, response)
        
        return response
    
    def get_generation_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取生成历史"""
        history = []
        
        for request_id in list(self.response_history.keys())[-limit:]:
            request = self.request_history.get(request_id)
            response = self.response_history.get(request_id)
            
            if request and response:
                history.append({
                    "request_id": request_id,
                    "content_type": request.content_type.value,
                    "style": request.style.value,
                    "prompt": request.prompt[:100] + "..." if len(request.prompt) > 100 else request.prompt,
                    "model_used": response.model_used,
                    "quality_score": response.quality_score,
                    "generation_time": response.generation_time,
                    "created_at": response.created_at
                })
        
        return history
    
    def get_platform_analytics(self) -> Dict[str, Any]:
        """获取平台分析数据"""
        return self.analytics.get_analytics()

class ContentAnalytics:
    """内容分析"""
    
    def __init__(self):
        self.generation_stats = {
            "total_generations": 0,
            "content_type_distribution": {},
            "style_distribution": {},
            "model_usage": {},
            "avg_quality_score": 0.0,
            "avg_generation_time": 0.0
        }
        self.quality_scores = []
        self.generation_times = []
    
    def record_generation(self, request: ContentRequest, response: ContentResponse):
        """记录生成数据"""
        self.generation_stats["total_generations"] += 1
        
        # 内容类型分布
        content_type = request.content_type.value
        self.generation_stats["content_type_distribution"][content_type] = \
            self.generation_stats["content_type_distribution"].get(content_type, 0) + 1
        
        # 风格分布
        style = request.style.value
        self.generation_stats["style_distribution"][style] = \
            self.generation_stats["style_distribution"].get(style, 0) + 1
        
        # 模型使用
        model = response.model_used
        self.generation_stats["model_usage"][model] = \
            self.generation_stats["model_usage"].get(model, 0) + 1
        
        # 质量分数
        if response.quality_score:
            self.quality_scores.append(response.quality_score)
            self.generation_stats["avg_quality_score"] = sum(self.quality_scores) / len(self.quality_scores)
        
        # 生成时间
        self.generation_times.append(response.generation_time)
        self.generation_stats["avg_generation_time"] = sum(self.generation_times) / len(self.generation_times)
    
    def get_analytics(self) -> Dict[str, Any]:
        """获取分析数据"""
        return self.generation_stats.copy()

# 使用示例
async def demo_content_generation_platform():
    """内容生成平台演示"""
    # 创建模拟接口
    class MockModelInterface:
        async def chat_completion(self, request):
            from接口封装和统一管理策略 import ChatResponse
            
            # 根据请求生成模拟内容
            if "文章" in request.messages[0].content:
                content = "这是一篇关于人工智能发展的专业文章。人工智能技术正在快速发展，为各行各业带来了革命性的变化..."
            elif "摘要" in request.messages[0].content:
                content = "本文主要讨论了人工智能的发展现状和未来趋势，重点分析了技术突破和应用前景。"
            elif "代码" in request.messages[0].content:
                content = "```python\ndef hello_world():\n    print('Hello, World!')\n```"
            else:
                content = "这是生成的内容示例。"
            
            return ChatResponse(
                content=content,
                model="mock-model",
                usage={"total_tokens": 200}
            )
    
    # 初始化平台
    model_interface = MockModelInterface()
    platform = ContentGenerationPlatform(model_interface)
    
    print("=== 内容生成平台演示 ===")
    
    # 生成不同类型的内容
    test_cases = [
        {
            "content_type": ContentType.ARTICLE,
            "style": ContentStyle.PROFESSIONAL,
            "prompt": "人工智能在医疗领域的应用",
            "target_length": 1000
        },
        {
            "content_type": ContentType.SUMMARY,
            "style": ContentStyle.FORMAL,
            "prompt": "总结以下技术报告的主要内容：AI技术发展报告2024"
        },
        {
            "content_type": ContentType.CODE,
            "style": ContentStyle.TECHNICAL,
            "prompt": "编写一个Python函数来计算斐波那契数列"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n--- 测试案例 {i} ---")
        print(f"类型: {case['content_type'].value}")
        print(f"风格: {case['style'].value}")
        print(f"提示: {case['prompt']}")
        
        response = await platform.create_content(**case)
        
        print(f"生成内容: {response.content[:200]}...")
        print(f"使用模型: {response.model_used}")
        print(f"质量分数: {response.quality_score}")
        print(f"生成时间: {response.generation_time:.2f}秒")
    
    # 获取历史记录
    history = platform.get_generation_history()
    print(f"\n生成历史记录数: {len(history)}")
    
    # 获取平台分析
    analytics = platform.get_platform_analytics()
    print(f"平台分析数据: {analytics}")

if __name__ == "__main__":
    asyncio.run(demo_content_generation_platform())
```

这些实际应用案例展示了如何将模型接口整合技术应用到具体的业务场景中，包括智能客服系统和内容生成平台。每个案例都包含了完整的架构设计、核心功能实现和使用示例，为实际项目开发提供了有价值的参考。
