# 项目1：个人知识助手

## 项目概述

本项目旨在构建一个智能的个人知识助手，能够管理用户的文档、笔记、邮件等个人知识资产，并提供智能问答、知识发现和内容生成功能。系统将深度集成LangChain和LlamaIndex，展示两个框架的协同优势。

## 项目目标

1. **知识管理**：自动化的文档导入、分类和索引
2. **智能问答**：基于个人知识库的精准问答
3. **知识发现**：主动发现知识间的关联和模式
4. **内容生成**：基于已有知识生成新内容
5. **个性化服务**：学习用户偏好，提供个性化体验

## 技术架构

```
个人知识助手架构
┌─────────────────────────────────────────────────────────┐
│                    用户界面层                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │   Web UI    │ │  Mobile App │ │   API       │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
├─────────────────────────────────────────────────────────┤
│                    服务层                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │ 问答服务     │ │ 知识发现     │ │ 内容生成     │        │
│  │(LangChain)  │ │(混合)       │ │(LangChain)  │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
├─────────────────────────────────────────────────────────┤
│                    数据处理层                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │ 文档处理     │ │ 索引管理     │ │ 向量存储     │        │
│  │(LlamaIndex) │ │(LlamaIndex) │ │(LlamaIndex) │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
├─────────────────────────────────────────────────────────┤
│                    存储层                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │ 文档存储     │ │ 向量数据库   │ │ 元数据库     │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
└─────────────────────────────────────────────────────────┘
```

## 核心功能实现

### 1. 知识管理系统

```python
# personal_knowledge_assistant/core/knowledge_manager.py
from llama_index.core import Document, VectorStoreIndex, SimpleDirectoryReader
from llama_index.core.node_parser import SimpleNodeParser
from llama_index.core.extractors import TitleExtractor, KeywordExtractor, SummaryExtractor
from langchain.text_splitter import RecursiveCharacterTextSplitter
from typing import List, Dict, Any, Optional
import os
import hashlib
import json
from datetime import datetime
import asyncio

class KnowledgeManager:
    """知识管理器 - 核心知识管理功能"""
    
    def __init__(self, storage_dir: str = "./knowledge_base"):
        self.storage_dir = storage_dir
        self.documents_dir = os.path.join(storage_dir, "documents")
        self.indexes_dir = os.path.join(storage_dir, "indexes")
        self.metadata_file = os.path.join(storage_dir, "metadata.json")
        
        # 创建目录
        os.makedirs(self.documents_dir, exist_ok=True)
        os.makedirs(self.indexes_dir, exist_ok=True)
        
        # 初始化组件
        self.node_parser = SimpleNodeParser.from_defaults()
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200
        )
        
        # 元数据提取器
        self.extractors = [
            TitleExtractor(nodes=5),
            KeywordExtractor(keywords=10),
            SummaryExtractor(summaries=["prev", "self"])
        ]
        
        # 加载现有元数据
        self.metadata = self._load_metadata()
        
        # 索引管理
        self.indexes = {}
        self._load_indexes()
    
    def _load_metadata(self) -> Dict[str, Any]:
        """加载元数据"""
        if os.path.exists(self.metadata_file):
            with open(self.metadata_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {
            "documents": {},
            "categories": {},
            "tags": {},
            "created_at": datetime.now().isoformat()
        }
    
    def _save_metadata(self):
        """保存元数据"""
        with open(self.metadata_file, 'w', encoding='utf-8') as f:
            json.dump(self.metadata, f, ensure_ascii=False, indent=2)
    
    def _load_indexes(self):
        """加载现有索引"""
        # 这里简化实现，实际应用中需要持久化索引
        pass
    
    def _generate_doc_id(self, content: str, source: str) -> str:
        """生成文档ID"""
        content_hash = hashlib.md5(content.encode()).hexdigest()
        return f"{source}_{content_hash[:8]}"
    
    def add_document(self, content: str, source: str, 
                    category: str = "general", 
                    tags: List[str] = None,
                    metadata: Dict[str, Any] = None) -> str:
        """添加文档到知识库"""
        # 生成文档ID
        doc_id = self._generate_doc_id(content, source)
        
        # 检查是否已存在
        if doc_id in self.metadata["documents"]:
            print(f"文档已存在: {doc_id}")
            return doc_id
        
        # 创建文档对象
        doc_metadata = {
            "source": source,
            "category": category,
            "tags": tags or [],
            "added_at": datetime.now().isoformat(),
            "doc_id": doc_id
        }
        
        if metadata:
            doc_metadata.update(metadata)
        
        document = Document(text=content, metadata=doc_metadata)
        
        # 处理文档
        nodes = self._process_document(document)
        
        # 更新索引
        self._update_indexes(nodes, category)
        
        # 保存元数据
        self.metadata["documents"][doc_id] = doc_metadata
        self._update_category_stats(category)
        self._update_tag_stats(tags or [])
        self._save_metadata()
        
        print(f"文档已添加: {doc_id} (类别: {category})")
        return doc_id
    
    def _process_document(self, document: Document) -> List:
        """处理文档，提取节点和元数据"""
        # 解析节点
        nodes = self.node_parser.get_nodes_from_documents([document])
        
        # 提取元数据
        for extractor in self.extractors:
            nodes = extractor.extract(nodes)
        
        return nodes
    
    def _update_indexes(self, nodes: List, category: str):
        """更新索引"""
        # 全局索引
        if "global" not in self.indexes:
            self.indexes["global"] = VectorStoreIndex([])
        
        # 类别索引
        if category not in self.indexes:
            self.indexes[category] = VectorStoreIndex([])
        
        # 添加节点到索引
        for node in nodes:
            self.indexes["global"].insert(node)
            self.indexes[category].insert(node)
    
    def _update_category_stats(self, category: str):
        """更新类别统计"""
        if category not in self.metadata["categories"]:
            self.metadata["categories"][category] = {
                "count": 0,
                "created_at": datetime.now().isoformat()
            }
        self.metadata["categories"][category]["count"] += 1
    
    def _update_tag_stats(self, tags: List[str]):
        """更新标签统计"""
        for tag in tags:
            if tag not in self.metadata["tags"]:
                self.metadata["tags"][tag] = {
                    "count": 0,
                    "created_at": datetime.now().isoformat()
                }
            self.metadata["tags"][tag]["count"] += 1
    
    def batch_import(self, directory: str, 
                    file_extensions: List[str] = None) -> Dict[str, Any]:
        """批量导入文档"""
        if file_extensions is None:
            file_extensions = [".txt", ".md", ".pdf", ".docx"]
        
        # 使用LlamaIndex的目录读取器
        reader = SimpleDirectoryReader(
            input_dir=directory,
            file_extractor={ext: "SimpleReader" for ext in file_extensions},
            recursive=True
        )
        
        documents = reader.load_data()
        
        import_results = {
            "total_files": len(documents),
            "successful": 0,
            "failed": 0,
            "errors": []
        }
        
        for doc in documents:
            try:
                # 从文件路径推断类别
                file_path = doc.metadata.get("file_path", "")
                category = self._infer_category_from_path(file_path)
                
                # 添加文档
                doc_id = self.add_document(
                    content=doc.text,
                    source=file_path,
                    category=category,
                    metadata=doc.metadata
                )
                
                import_results["successful"] += 1
                
            except Exception as e:
                import_results["failed"] += 1
                import_results["errors"].append({
                    "file": doc.metadata.get("file_path", "unknown"),
                    "error": str(e)
                })
        
        return import_results
    
    def _infer_category_from_path(self, file_path: str) -> str:
        """从文件路径推断类别"""
        path_lower = file_path.lower()
        
        if "work" in path_lower or "project" in path_lower:
            return "work"
        elif "personal" in path_lower or "diary" in path_lower:
            return "personal"
        elif "research" in path_lower or "paper" in path_lower:
            return "research"
        elif "note" in path_lower:
            return "notes"
        else:
            return "general"
    
    def search_documents(self, query: str, 
                        category: Optional[str] = None,
                        tags: Optional[List[str]] = None,
                        limit: int = 10) -> List[Dict[str, Any]]:
        """搜索文档"""
        # 选择索引
        if category and category in self.indexes:
            index = self.indexes[category]
        else:
            index = self.indexes.get("global")
        
        if not index:
            return []
        
        # 执行搜索
        query_engine = index.as_query_engine(similarity_top_k=limit)
        response = query_engine.query(query)
        
        # 处理结果
        results = []
        if hasattr(response, 'source_nodes'):
            for node in response.source_nodes:
                result = {
                    "content": node.node.text,
                    "metadata": node.node.metadata,
                    "score": node.score
                }
                
                # 标签过滤
                if tags:
                    node_tags = node.node.metadata.get("tags", [])
                    if not any(tag in node_tags for tag in tags):
                        continue
                
                results.append(result)
        
        return results
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取知识库统计信息"""
        return {
            "total_documents": len(self.metadata["documents"]),
            "categories": dict(self.metadata["categories"]),
            "tags": dict(self.metadata["tags"]),
            "indexes": list(self.indexes.keys()),
            "storage_size": self._calculate_storage_size()
        }
    
    def _calculate_storage_size(self) -> str:
        """计算存储大小"""
        total_size = 0
        for root, dirs, files in os.walk(self.storage_dir):
            for file in files:
                file_path = os.path.join(root, file)
                total_size += os.path.getsize(file_path)
        
        # 转换为可读格式
        for unit in ['B', 'KB', 'MB', 'GB']:
            if total_size < 1024.0:
                return f"{total_size:.1f} {unit}"
            total_size /= 1024.0
        return f"{total_size:.1f} TB"
    
    async def async_batch_import(self, directory: str) -> Dict[str, Any]:
        """异步批量导入"""
        return await asyncio.to_thread(self.batch_import, directory)

# 智能问答系统
class IntelligentQA:
    """智能问答系统 - 基于LangChain的问答功能"""
    
    def __init__(self, knowledge_manager: KnowledgeManager, llm):
        self.knowledge_manager = knowledge_manager
        self.llm = llm
        self.conversation_history = []
        self.setup_qa_chain()
    
    def setup_qa_chain(self):
        """设置问答链"""
        from langchain.chains import ConversationalRetrievalChain
        from langchain.memory import ConversationBufferMemory
        
        # 创建记忆
        self.memory = ConversationBufferMemory(
            memory_key="chat_history",
            return_messages=True
        )
        
        # 这里简化实现，实际需要集成检索器
        # self.qa_chain = ConversationalRetrievalChain.from_llm(
        #     llm=self.llm,
        #     retriever=self._create_retriever(),
        #     memory=self.memory
        # )
    
    def _create_retriever(self):
        """创建检索器"""
        # 集成LlamaIndex的检索器
        global_index = self.knowledge_manager.indexes.get("global")
        if global_index:
            return global_index.as_retriever(similarity_top_k=5)
        return None
    
    def ask(self, question: str, context: Optional[str] = None) -> Dict[str, Any]:
        """提问"""
        # 搜索相关文档
        relevant_docs = self.knowledge_manager.search_documents(
            query=question,
            limit=5
        )
        
        # 构建上下文
        context_text = ""
        if relevant_docs:
            context_text = "\n\n".join([
                f"文档片段 {i+1}:\n{doc['content']}"
                for i, doc in enumerate(relevant_docs)
            ])
        
        # 构建提示
        prompt = f"""基于以下上下文信息回答问题：

上下文：
{context_text}

问题：{question}

请提供准确、详细的回答，如果上下文中没有相关信息，请说明。"""
        
        # 调用LLM
        try:
            response = self.llm(prompt)
            
            result = {
                "question": question,
                "answer": response,
                "sources": relevant_docs,
                "confidence": self._calculate_confidence(relevant_docs)
            }
            
            # 记录对话历史
            self.conversation_history.append(result)
            
            return result
            
        except Exception as e:
            return {
                "question": question,
                "answer": f"抱歉，处理问题时出现错误：{str(e)}",
                "sources": [],
                "confidence": 0.0
            }
    
    def _calculate_confidence(self, sources: List[Dict]) -> float:
        """计算回答置信度"""
        if not sources:
            return 0.0
        
        # 基于检索分数计算置信度
        avg_score = sum(doc.get("score", 0) for doc in sources) / len(sources)
        return min(avg_score, 1.0)
    
    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """获取对话历史"""
        return self.conversation_history
    
    def clear_history(self):
        """清空对话历史"""
        self.conversation_history.clear()
        if hasattr(self, 'memory'):
            self.memory.clear()

# 知识发现系统
class KnowledgeDiscovery:
    """知识发现系统 - 发现知识间的关联"""
    
    def __init__(self, knowledge_manager: KnowledgeManager):
        self.knowledge_manager = knowledge_manager
    
    def find_related_documents(self, doc_id: str, 
                             similarity_threshold: float = 0.7) -> List[Dict[str, Any]]:
        """查找相关文档"""
        # 获取文档内容
        doc_metadata = self.knowledge_manager.metadata["documents"].get(doc_id)
        if not doc_metadata:
            return []
        
        # 使用文档的关键词进行搜索
        keywords = doc_metadata.get("keywords", [])
        if not keywords:
            return []
        
        # 搜索相关文档
        query = " ".join(keywords[:5])  # 使用前5个关键词
        related_docs = self.knowledge_manager.search_documents(
            query=query,
            limit=10
        )
        
        # 过滤掉自身和低相似度文档
        filtered_docs = []
        for doc in related_docs:
            if (doc["metadata"].get("doc_id") != doc_id and 
                doc.get("score", 0) >= similarity_threshold):
                filtered_docs.append(doc)
        
        return filtered_docs
    
    def discover_knowledge_clusters(self) -> Dict[str, List[str]]:
        """发现知识聚类"""
        # 简化实现：基于类别和标签进行聚类
        clusters = {}
        
        # 按类别聚类
        for doc_id, metadata in self.knowledge_manager.metadata["documents"].items():
            category = metadata.get("category", "general")
            if category not in clusters:
                clusters[category] = []
            clusters[category].append(doc_id)
        
        return clusters
    
    def suggest_tags(self, content: str, existing_tags: List[str] = None) -> List[str]:
        """建议标签"""
        # 简化实现：基于关键词提取
        from collections import Counter
        import re
        
        # 提取关键词
        words = re.findall(r'\b[a-zA-Z]{3,}\b', content.lower())
        word_freq = Counter(words)
        
        # 获取高频词作为标签建议
        suggested_tags = [word for word, freq in word_freq.most_common(10) 
                         if freq > 1 and word not in (existing_tags or [])]
        
        return suggested_tags[:5]

# 使用示例
def demo_personal_knowledge_assistant():
    """演示个人知识助手"""
    from langchain.llms import OpenAI
    
    # 初始化组件
    knowledge_manager = KnowledgeManager("./demo_knowledge_base")
    
    # 添加示例文档
    sample_docs = [
        {
            "content": "人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。",
            "source": "AI_basics.txt",
            "category": "research",
            "tags": ["AI", "technology", "research"]
        },
        {
            "content": "今天学习了Python编程，特别是面向对象编程的概念。类和对象是核心概念。",
            "source": "learning_notes.txt", 
            "category": "notes",
            "tags": ["Python", "programming", "learning"]
        },
        {
            "content": "项目进度更新：完成了用户界面设计，下一步是后端API开发。",
            "source": "project_update.txt",
            "category": "work",
            "tags": ["project", "progress", "development"]
        }
    ]
    
    # 添加文档
    for doc in sample_docs:
        knowledge_manager.add_document(**doc)
    
    # 初始化问答系统
    llm = OpenAI(temperature=0.7)
    qa_system = IntelligentQA(knowledge_manager, llm)
    
    # 测试问答
    questions = [
        "什么是人工智能？",
        "我学习了哪些编程概念？",
        "项目的当前状态如何？"
    ]
    
    print("=== 个人知识助手演示 ===")
    
    # 显示知识库统计
    stats = knowledge_manager.get_statistics()
    print(f"知识库统计: {stats}")
    
    # 测试问答
    for question in questions:
        print(f"\n问题: {question}")
        result = qa_system.ask(question)
        print(f"回答: {result['answer']}")
        print(f"置信度: {result['confidence']:.2f}")
    
    # 知识发现
    discovery = KnowledgeDiscovery(knowledge_manager)
    clusters = discovery.discover_knowledge_clusters()
    print(f"\n知识聚类: {clusters}")
    
    return knowledge_manager, qa_system

if __name__ == "__main__":
    demo_personal_knowledge_assistant()
```

### 2. Web界面实现

```python
# personal_knowledge_assistant/web/app.py
import streamlit as st
import asyncio
from typing import Dict, Any, List
import plotly.express as px
import plotly.graph_objects as go
import pandas as pd

class KnowledgeAssistantUI:
    """知识助手Web界面"""
    
    def __init__(self, knowledge_manager, qa_system, discovery_system):
        self.knowledge_manager = knowledge_manager
        self.qa_system = qa_system
        self.discovery_system = discovery_system
    
    def render_main_interface(self):
        """渲染主界面"""
        st.set_page_config(
            page_title="个人知识助手",
            page_icon="🧠",
            layout="wide"
        )
        
        st.title("🧠 个人知识助手")
        st.markdown("智能管理您的个人知识资产")
        
        # 侧边栏
        self.render_sidebar()
        
        # 主要内容区域
        tab1, tab2, tab3, tab4 = st.tabs(["💬 智能问答", "📚 知识管理", "🔍 知识发现", "📊 统计分析"])
        
        with tab1:
            self.render_qa_interface()
        
        with tab2:
            self.render_knowledge_management()
        
        with tab3:
            self.render_knowledge_discovery()
        
        with tab4:
            self.render_analytics()
    
    def render_sidebar(self):
        """渲染侧边栏"""
        with st.sidebar:
            st.header("⚙️ 设置")
            
            # 知识库统计
            stats = self.knowledge_manager.get_statistics()
            st.metric("文档总数", stats["total_documents"])
            st.metric("类别数量", len(stats["categories"]))
            st.metric("标签数量", len(stats["tags"]))
            
            st.divider()
            
            # 快速操作
            st.header("🚀 快速操作")
            
            if st.button("🔄 刷新索引"):
                with st.spinner("正在刷新索引..."):
                    # 这里可以添加索引刷新逻辑
                    st.success("索引刷新完成！")
            
            if st.button("🧹 清理缓存"):
                with st.spinner("正在清理缓存..."):
                    # 这里可以添加缓存清理逻辑
                    st.success("缓存清理完成！")
    
    def render_qa_interface(self):
        """渲染问答界面"""
        st.header("💬 智能问答")
        
        # 问答输入
        col1, col2 = st.columns([3, 1])
        
        with col1:
            question = st.text_input(
                "请输入您的问题：",
                placeholder="例如：我之前学习了哪些技术？"
            )
        
        with col2:
            ask_button = st.button("🔍 提问", type="primary")
        
        # 高级选项
        with st.expander("🔧 高级选项"):
            col1, col2 = st.columns(2)
            with col1:
                selected_category = st.selectbox(
                    "限制搜索类别",
                    ["全部"] + list(self.knowledge_manager.metadata["categories"].keys())
                )
            with col2:
                max_results = st.slider("最大结果数", 1, 20, 5)
        
        # 处理问答
        if ask_button and question:
            with st.spinner("正在思考..."):
                # 执行问答
                category = None if selected_category == "全部" else selected_category
                result = self.qa_system.ask(question)
                
                # 显示回答
                st.subheader("📝 回答")
                st.write(result["answer"])
                
                # 显示置信度
                confidence = result["confidence"]
                st.metric("置信度", f"{confidence:.1%}")
                
                # 显示来源
                if result["sources"]:
                    st.subheader("📚 参考来源")
                    for i, source in enumerate(result["sources"][:3]):
                        with st.expander(f"来源 {i+1} (相似度: {source.get('score', 0):.2f})"):
                            st.write(source["content"])
                            st.caption(f"来源: {source['metadata'].get('source', '未知')}")
        
        # 对话历史
        st.subheader("📜 对话历史")
        history = self.qa_system.get_conversation_history()
        
        if history:
            for i, conv in enumerate(reversed(history[-5:])):  # 显示最近5条
                with st.expander(f"Q{len(history)-i}: {conv['question'][:50]}..."):
                    st.write(f"**问题**: {conv['question']}")
                    st.write(f"**回答**: {conv['answer']}")
                    st.caption(f"置信度: {conv['confidence']:.1%}")
        else:
            st.info("暂无对话历史")
    
    def render_knowledge_management(self):
        """渲染知识管理界面"""
        st.header("📚 知识管理")
        
        # 文档上传
        st.subheader("📤 添加文档")
        
        # 单文档添加
        with st.expander("📄 添加单个文档"):
            doc_content = st.text_area("文档内容", height=200)
            
            col1, col2, col3 = st.columns(3)
            with col1:
                doc_source = st.text_input("来源", placeholder="例如：meeting_notes.txt")
            with col2:
                doc_category = st.selectbox(
                    "类别",
                    ["general", "work", "personal", "research", "notes"]
                )
            with col3:
                doc_tags = st.text_input("标签", placeholder="用逗号分隔")
            
            if st.button("➕ 添加文档"):
                if doc_content and doc_source:
                    tags = [tag.strip() for tag in doc_tags.split(",") if tag.strip()]
                    
                    try:
                        doc_id = self.knowledge_manager.add_document(
                            content=doc_content,
                            source=doc_source,
                            category=doc_category,
                            tags=tags
                        )
                        st.success(f"文档已添加！ID: {doc_id}")
                    except Exception as e:
                        st.error(f"添加文档失败: {str(e)}")
                else:
                    st.warning("请填写文档内容和来源")
        
        # 批量导入
        with st.expander("📁 批量导入"):
            import_dir = st.text_input("目录路径", placeholder="/path/to/documents")
            
            if st.button("🚀 开始导入"):
                if import_dir:
                    with st.spinner("正在导入文档..."):
                        try:
                            results = self.knowledge_manager.batch_import(import_dir)
                            
                            col1, col2, col3 = st.columns(3)
                            with col1:
                                st.metric("总文件数", results["total_files"])
                            with col2:
                                st.metric("成功导入", results["successful"])
                            with col3:
                                st.metric("导入失败", results["failed"])
                            
                            if results["errors"]:
                                st.error("部分文件导入失败:")
                                for error in results["errors"]:
                                    st.write(f"- {error['file']}: {error['error']}")
                        
                        except Exception as e:
                            st.error(f"批量导入失败: {str(e)}")
                else:
                    st.warning("请输入目录路径")
        
        # 文档搜索和管理
        st.subheader("🔍 文档搜索")
        
        search_query = st.text_input("搜索关键词")
        
        col1, col2 = st.columns(2)
        with col1:
            search_category = st.selectbox(
                "搜索类别",
                ["全部"] + list(self.knowledge_manager.metadata["categories"].keys()),
                key="search_category"
            )
        with col2:
            search_limit = st.slider("结果数量", 1, 50, 10)
        
        if search_query:
            category = None if search_category == "全部" else search_category
            results = self.knowledge_manager.search_documents(
                query=search_query,
                category=category,
                limit=search_limit
            )
            
            if results:
                st.write(f"找到 {len(results)} 个相关文档:")
                
                for i, result in enumerate(results):
                    with st.expander(f"文档 {i+1} (相似度: {result.get('score', 0):.2f})"):
                        st.write(result["content"])
                        
                        metadata = result["metadata"]
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.caption(f"来源: {metadata.get('source', '未知')}")
                        with col2:
                            st.caption(f"类别: {metadata.get('category', '未知')}")
                        with col3:
                            st.caption(f"标签: {', '.join(metadata.get('tags', []))}")
            else:
                st.info("未找到相关文档")
    
    def render_knowledge_discovery(self):
        """渲染知识发现界面"""
        st.header("🔍 知识发现")
        
        # 知识聚类
        st.subheader("📊 知识聚类")
        clusters = self.discovery_system.discover_knowledge_clusters()
        
        if clusters:
            # 创建聚类可视化
            cluster_data = []
            for category, doc_ids in clusters.items():
                cluster_data.append({
                    "类别": category,
                    "文档数量": len(doc_ids)
                })
            
            df = pd.DataFrame(cluster_data)
            
            col1, col2 = st.columns(2)
            with col1:
                fig = px.pie(df, values="文档数量", names="类别", title="文档类别分布")
                st.plotly_chart(fig, use_container_width=True)
            
            with col2:
                fig = px.bar(df, x="类别", y="文档数量", title="各类别文档数量")
                st.plotly_chart(fig, use_container_width=True)
        
        # 相关文档发现
        st.subheader("🔗 相关文档发现")
        
        # 选择文档
        doc_options = {}
        for doc_id, metadata in self.knowledge_manager.metadata["documents"].items():
            source = metadata.get("source", doc_id)
            doc_options[f"{source} ({doc_id})"] = doc_id
        
        if doc_options:
            selected_doc = st.selectbox("选择文档", list(doc_options.keys()))
            
            if selected_doc:
                doc_id = doc_options[selected_doc]
                
                with st.spinner("正在查找相关文档..."):
                    related_docs = self.discovery_system.find_related_documents(doc_id)
                
                if related_docs:
                    st.write(f"找到 {len(related_docs)} 个相关文档:")
                    
                    for i, doc in enumerate(related_docs):
                        with st.expander(f"相关文档 {i+1} (相似度: {doc.get('score', 0):.2f})"):
                            st.write(doc["content"][:300] + "...")
                            st.caption(f"来源: {doc['metadata'].get('source', '未知')}")
                else:
                    st.info("未找到相关文档")
        else:
            st.info("知识库中暂无文档")
    
    def render_analytics(self):
        """渲染统计分析界面"""
        st.header("📊 统计分析")
        
        stats = self.knowledge_manager.get_statistics()
        
        # 总体统计
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("总文档数", stats["total_documents"])
        with col2:
            st.metric("类别数", len(stats["categories"]))
        with col3:
            st.metric("标签数", len(stats["tags"]))
        with col4:
            st.metric("存储大小", stats["storage_size"])
        
        # 类别分析
        if stats["categories"]:
            st.subheader("📂 类别分析")
            
            category_data = []
            for category, info in stats["categories"].items():
                category_data.append({
                    "类别": category,
                    "文档数量": info["count"],
                    "创建时间": info["created_at"]
                })
            
            df_categories = pd.DataFrame(category_data)
            
            col1, col2 = st.columns(2)
            with col1:
                fig = px.bar(df_categories, x="类别", y="文档数量", 
                           title="各类别文档数量分布")
                st.plotly_chart(fig, use_container_width=True)
            
            with col2:
                st.dataframe(df_categories, use_container_width=True)
        
        # 标签分析
        if stats["tags"]:
            st.subheader("🏷️ 标签分析")
            
            # 标签云数据
            tag_data = []
            for tag, info in stats["tags"].items():
                tag_data.append({
                    "标签": tag,
                    "使用次数": info["count"]
                })
            
            df_tags = pd.DataFrame(tag_data)
            df_tags = df_tags.sort_values("使用次数", ascending=False).head(20)
            
            fig = px.bar(df_tags, x="使用次数", y="标签", 
                        orientation='h', title="热门标签 (Top 20)")
            st.plotly_chart(fig, use_container_width=True)
        
        # 时间趋势分析
        st.subheader("📈 时间趋势")
        
        # 这里可以添加文档添加时间的趋势分析
        # 简化实现，显示一个示例图表
        import datetime
        dates = pd.date_range(start='2024-01-01', end='2024-12-31', freq='M')
        values = [5, 8, 12, 15, 20, 18, 25, 30, 28, 35, 40, 45]
        
        fig = go.Figure()
        fig.add_trace(go.Scatter(x=dates, y=values, mode='lines+markers', 
                               name='文档添加数量'))
        fig.update_layout(title="文档添加趋势", xaxis_title="时间", yaxis_title="文档数量")
        st.plotly_chart(fig, use_container_width=True)

def main():
    """主函数"""
    # 这里需要初始化实际的组件
    # 由于依赖外部服务，这里提供一个模拟版本
    
    st.title("🧠 个人知识助手")
    st.info("这是个人知识助手的演示界面。要运行完整功能，请配置相应的API密钥和依赖。")
    
    # 显示功能特性
    st.header("✨ 主要功能")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("💬 智能问答")
        st.write("- 基于个人知识库的精准问答")
        st.write("- 上下文感知的对话能力")
        st.write("- 多轮对话支持")
        
        st.subheader("📚 知识管理")
        st.write("- 多格式文档导入")
        st.write("- 自动分类和标签")
        st.write("- 智能索引构建")
    
    with col2:
        st.subheader("🔍 知识发现")
        st.write("- 相关文档推荐")
        st.write("- 知识聚类分析")
        st.write("- 标签建议")
        
        st.subheader("📊 统计分析")
        st.write("- 知识库统计")
        st.write("- 使用趋势分析")
        st.write("- 可视化报表")

if __name__ == "__main__":
    main()
```

## 项目部署与使用

### 1. 环境配置

```bash
# 安装依赖
pip install streamlit llama-index langchain openai
pip install plotly pandas redis

# 设置环境变量
export OPENAI_API_KEY="your-openai-key"
export REDIS_URL="redis://localhost:6379"
```

### 2. 启动应用

```bash
# 启动Web界面
streamlit run personal_knowledge_assistant/web/app.py

# 或者运行核心演示
python personal_knowledge_assistant/core/knowledge_manager.py
```

### 3. 使用指南

1. **文档导入**：通过Web界面或API导入个人文档
2. **智能问答**：在问答界面提出问题，获得基于知识库的回答
3. **知识发现**：探索文档间的关联和模式
4. **统计分析**：查看知识库的使用统计和趋势

## 项目价值

1. **个人效率提升**：快速检索和利用个人知识资产
2. **知识管理自动化**：自动化的文档处理和索引
3. **智能洞察**：发现知识间的隐藏关联
4. **学习辅助**：通过问答加深对知识的理解
5. **技术展示**：展示LangChain和LlamaIndex的协同应用

这个个人知识助手项目展示了如何将两个框架的优势结合起来，构建一个功能完整、用户友好的智能知识管理系统。

## 扩展功能建议

### 1. 高级功能扩展
- **多模态支持**：集成图像、音频文档处理
- **协作功能**：支持团队知识共享
- **移动端应用**：开发移动端知识助手
- **语音交互**：集成语音问答功能

### 2. 性能优化
- **分布式存储**：支持大规模知识库
- **缓存策略**：优化查询响应速度
- **增量更新**：支持实时文档更新
- **负载均衡**：处理高并发访问

### 3. 安全与隐私
- **数据加密**：保护敏感信息
- **访问控制**：细粒度权限管理
- **审计日志**：记录操作历史
- **隐私保护**：本地化部署选项

这个项目为学员提供了完整的框架集成实践经验，是掌握LangChain和LlamaIndex协同应用的理想项目。
